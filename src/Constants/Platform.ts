import { ENV_TYPE } from '@ctrip/rn_com_car/dist/src/CarFetch/src/Constants';

// http://conf.ctripcorp.com/pages/viewpage.action?pageId=102597246
export const IMAGE_UPLOAD_DOMAIN_URL = {
  [ENV_TYPE.FAT]: 'uploadimg.fws.qa.nt.ctripcorp.com',
  [ENV_TYPE.UAT]: 'uploadimg.uat.qa.nt.ctripcorp.com',
  [ENV_TYPE.BATTLE]: 'uploadimg.fws.qa.nt.ctripcorp.com',
  [ENV_TYPE.PROD]: 'nephele.ctrip.com',
};

export const APP_TYPE = {
  ISD_C_APP: 'ISD_C_APP', // 国内 Ctrip App
  ISD_C_H5: 'ISD_C_H5', // 国内 Ctrip H5
  ISD_Q_APP: 'ISD_Q_APP', // 国内 Qunar App
  ISD_ZUCHE_APP: 'ISD_ZUCHE_APP', // 国内 独立 App
  OSD_C_APP: 'OSD_C_APP', // 海外 Ctrip App
  OSD_C_H5: 'OSD_C_H5', // 海外 Ctrip H5
  OSD_T_APP: 'OSD_T_APP',
  OSD_T_H5: 'OSD_T_H5',
  OSD_Q_APP: 'OSD_Q_APP', // 海外 Qunar App
  OSD_ZUCHE_APP: 'OSD_ZUCHE_APP', // 海外 独立 App
  UNKNOW: 'UNKNOW', // 未知
  IBU_APP: 'IBU_App', // Trip App
  IBU_Online: 'IBU_Online', // Trip App
};

export const APP_ID = {
  TRIP: '37',
  CTRIP: '99999999',
};

export const BUSINESS_TYPE = {
  ISD: '35',
  OSD: '34',
  IBU: '34',
  UNKNOW: '',
};

export const BUS_TYPE = {
  ISD: 82,
  // 在线预授权
  // ISD_AUTH: 81,
  ISD_AUTH: 82,
  // 程信分
  ISD_CREDIT: 10613,
  OSD: 80,
  IBU: 0,
  NEWOSD: 82,
};

export const RENTAL_GAP = {
  ISD: 2,
  OSD: 7,
  IBU: 3,
  UNKNOW: 7,
};

export const DROPOFF_INTERVAL = {
  ISD: 0.25,
  OSD: 0.5,
  IBU: 3,
  UNKNOW: 7,
};

export const LOG_TYPE = {
  FRONT_END_APP: 'FRONT_END_APP',
  FRONT_END_PC: 'FRONT_END_PC',
  FRONT_END_H5: 'FRONT_END_H5',
};

const getModulePath = (moduleName: string) =>
  `/${moduleName}/_crn_config?CRNModuleName=${moduleName}&CRNType=1`;

const RN_CAR_OSD = getModulePath('rn_car_osd');
const RN_CAR_ISD = getModulePath('rn_car_isd');
const RN_IBU_CAR = getModulePath('rn_ibu_car');
const RN_CAR_APP = getModulePath('rn_car_app');
const RN_CAR_MAIN = getModulePath('rn_xtaro_car_main');
const RN_XTARO_CAR_OSD = getModulePath('rn_xtaro_car_osd');

/* eslint-disable max-len */
export const CAR_CROSS_URL = {
  LOCATION: {
    OSD: `${RN_CAR_MAIN}&initialPage=Location&apptype=OSD_C_APP&CRNType=1&showType=present&statusBarStyle=2`,
  },
  VEHMODAL: {
    OSD: `${RN_CAR_MAIN}&initialPage=VehModal&apptype=OSD_C_APP&CRNType=1&isCross=true`,
  },
  MARKETLIST: {
    OSD: `${RN_CAR_MAIN}&initialPage=List&st=client&fromurl=common&landingto=list&apptype=OSD_C_APP&statusBarStyle=2`,
  },
  OrderCancel: {
    OSD: `${RN_CAR_MAIN}&initialPage=OrderCancel&apptype=OSD_C_APP&CRNType=1&isCross=true`,
  },
  MessageAssistant: {
    OSD: `${RN_CAR_MAIN}&initialPage=MessageAssistant&apptype=OSD_C_APP&CRNType=1&isCross=true`,
  },
  Guide: {
    OSD: `${RN_CAR_MAIN}&initialPage=Guide&apptype=OSD_C_APP&CRNType=1&isCross=true`,
  },
  Debug: {
    OSD: `${RN_CAR_MAIN}&initialPage=Debug&apptype=OSD_C_APP&CRNType=1&isCross=true`,
  },
  License: {
    OSD: `${RN_CAR_MAIN}&initialPage=License&apptype=OSD_C_APP&CRNType=1`,
  },
  Policy: {
    OSD: `${RN_CAR_MAIN}&initialPage=Policy&apptype=OSD_C_APP&CRNType=1&showType=present&statusBarStyle=2&isCross=true`,
  },
  OrderRefundDetail: {
    OSD: `${RN_CAR_MAIN}&initialPage=OrderRefundDetail&apptype=OSD_C_APP&CRNType=1&isCross=true`,
  },
  Credentials: {
    OSD: `${RN_CAR_MAIN}&initialPage=Credentials&apptype=OSD_C_APP&CRNType=1`,
  },
  SupplementList: {
    OSD: `${RN_CAR_MAIN}&initialPage=SupplementList&apptype=OSD_C_APP&CRNType=1&isCross=true`,
  },
  DamageDetail: {
    OSD: `${RN_CAR_MAIN}&initialPage=DamageDetail&apptype=OSD_C_APP&CRNType=1&isCross=true`,
  },
  Rerent: {
    OSD: `${RN_CAR_MAIN}&initialPage=Rerent&apptype=OSD_C_APP&CRNType=1&isCross=true`,
  },
  Supplement: {
    OSD: `${RN_CAR_MAIN}&initialPage=Supplement&apptype=OSD_C_APP&CRNType=1&isCross=true`,
  },
  ORDERDETAIL: {
    OSD: `${RN_CAR_OSD}&page=OrderDetail`,
    ISD: `${RN_CAR_ISD}&initialPage=isdorderdetail&new=1`,
    IBU: `${RN_IBU_CAR}&page=orderdetail`,
    NEWTRIP:
      '/rn_ibu_car_app/_crn_config?CRNModuleName=rn_ibu_car_app&CRNType=1&initialPage=OrderDetail',
    NEWOSD:
      '/rn_xtaro_car_main/_crn_config?CRNModuleName=rn_xtaro_car_main&apptype=OSD_C_APP&CRNType=1&initialPage=OrderDetail&statusBarStyle=2',
    NEWOSD2: `${RN_XTARO_CAR_OSD}&initialPage=OrderDetail`,
    NEWISD: `${RN_CAR_MAIN}&apptype=ISD_C_APP&initialPage=OrderDetail&statusBarStyle=2`,
    H5ISD: '/webapp/cw/rn_car_app/OrderDetail.html?apptype=ISD_C_CW',
  },
  // 预定条款页面
  Agreement: {
    ISD: `//m.ctrip.com/webapp/carhire/xsd/osdrentalprovision?fromurl=osdfill&title=预订条款&sourceFrom=ISD_C_APP&hideHeader=true`,
    OSD: '//m.ctrip.com/webapp/carhire/xsd/osdrentalprovision?fromurl=osdfill&title=预订条款&sourceFrom=OSD_C_APP&hideHeader=true',
    H5ISD: '//m.ctrip.com/html5/carhire/subscribeTerms',
  },
  ServerRule: {
    ISD: `${RN_CAR_ISD}&initialPage=isdagreement`,
    OSD: '//m.ctrip.com/webapp/carhire/xsd/osdrentalprovision?fromurl=osdfill&title=服务协议&hideHeader=true',
    H5ISD: '//m.ctrip.com/html5/carhire/subscribeTerms',
  },
  LIST: {
    OSD: `${RN_CAR_OSD}&initialPage=Market&landingto=list&st=client&fromurl=history&apptype=OSD_C_APP&statusBarStyle=2`,
    ISD: `${RN_CAR_MAIN}&initialPage=Market&landingto=list&st=client&fromurl=history&apptype=ISD_C_APP&statusBarStyle=2`,
  },
  CTQHOME: {
    OSD: `${RN_CAR_APP}&initialPage=Market&st=client&fromurl=common&landingto=Home&apptype=OSD_C_APP`,
    ISD: `${RN_CAR_APP}&initialPage=Market&st=client&fromurl=common&landingto=Home&apptype=ISD_C_APP`,
  },
  REBOOK: {
    ISD: `${RN_CAR_MAIN}&initialPage=Market&`,
  },
};

export const ORDER_BACK_PARAMS = {
  book: 'carbooking',
  newBook: 'CtqHome', // booking页面下单成功跳转订详，订详点回退直接跳转新首页
};
export const ClientType = {
  qunar: 'Qunar_App',
  ctrip: 'Ctrip_App',
  izuche: 'IZuche',
  h5: 'ctrip_h5',
  holiday: 'Ctrip_VacationApp',
};
export const SIDE_TOOL_BIZ_TYPE = {
  ISD: 'carrental',
  OSD: 'oversea-carrental',
};

export const LIST_SHOW_VENDOR_NUM = {
  ISD: 2,
  OSD: 2,
};

export const COMPONENT_CHANNEL = {
  ISD: 'ISD',
  OSD: 'OSD',
  TRIP: 'TRIP',
  COMMON: 'COMMON',
};
