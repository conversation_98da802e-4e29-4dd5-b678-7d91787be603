$black: #333333;
$blackOrigin: #000000;
$white: #ffffff;

$grayBorder: #e4e4e4;
$gray666: #666666;
$gray999: #999999;
$grayCCC: #cccccc;
$grayEEE: #eeeeee;
$gray1: #f8f8f8;
$gray2: #e1e5eb;
$gray3: #777777;
$gray4: #f6f8fa;
$C_555555: #555555;
$C_888888: #888888;
$gray9: #999999;
$gray6: #cdd7df;
$grayf4: #f4f4f4;
$grayc5: #c5c5c5;

$blue1: #006ff6;
$blue2: #e6f3fe;
$blue3: #0086f6;
$blue4: #f2f8fe;
$blue5: #3263a6;
$f3f7fa: #f3f7fa;
$eef1f6: #eef1f6;
$C_02245C: #02245c;
$C_7686AA: #7686aa;
$aaaaaa: #aaaaaa;
$ff6501: #ff6501;
$c00b988: #00b988;
$c14c800: #14c800;
$f3f3f3: #f3f3f3;
$ebf4ff: #ebf4ff;
$ebeff5: #ebeff5;
$C_01AE73: #01ae73;
$C_00ae73: #00ae73;

$orange1: #ff6600;
$fff8f2: #fff8f2;
$ff7700: #ff7700;
$f5f7fA: #f5f7fa;
$f8fafd: #f8fafd;

$green1: #0ad2a7;
$C_11df77: #11df77;
$C_dee4ee: #dee4ee;
$C_00b87a: #00b87a;
$C_effaff: #effaff; // cspell:disable-line
$C_f85e53: #f85e53;

$C_111: #111111;
$C_E1E1E1: #e1e1e1;
$C_8592A6: #8592a6;
$C_9A9FA5: #9a9fa5;
$C_ECECEC: #ececec; // cspell:disable-line
$C_FFF8F2: #fff8f2;
$C_FF7700: #ff7700;
$C_00254f: #00254f;
$C_05B87A: #05b87a;

$C_767A98: #767a98;
$C_37373A: #37373a;
$C_383333: #383333;
$C_F9FAFC: #f9fafc; // cspell:disable-line
$C_B5BACC: #b5bacc; // cspell:disable-line
$C_888888: #888888;
$C_E7E7E7: #e7e7e7;
$C_F0F0F0: #f0f0f0;

$C_0066F6: #0066f6;
$C_F5190A: #f5190a;
$C_F0F2F5: #f0f2f5;
$C_D9D9D9: #d9d9d9;
$C_D5D5D5: #d5d5d5;
$C_F2F7FE: #f2f7fe;
$C_08A66F: #08a66f;
$C_D1E5FF: #d1e5ff;
$C_fafbfd: #fafbfd;
$C_ff5500: #ff5500;
$C_e5e5e5: #e5e5e5;
$C_ACB4BF: #acb4bf;
$C_597FB3: #597fb3;
$C_0f4999: #0f4999;
$C_F51909: #f51909;
$C_EE3B28: #ee3b28;
$C_CFD8E6: #cfd8e6;
$C_F5F9FF: #f5f9ff;
$transparent: transparent;
$C_FF7529: #ff7529;
$C_E5E5E5: #e5e5e5;
$C_F2F2F2: #f2f2f2;
$C_808080: #808080;
$C_869ebf: #869ebf;

:export {
  blue1: $blue1;
  orange1: $orange1;
  white: $white;
}
