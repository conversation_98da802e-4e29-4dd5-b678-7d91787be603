/* eslint-disable react/jsx-props-no-spreading */
import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';
import Touchable from '@pages/components/Touchable';
import Text from '@pages/components/Text';
import getPixel from '@pages/utils/getPixel';
import Style from './index.module.scss';

type IUnderText = {
  onClick?: any;
  testID?: any;
  [key: string]: any;
};

const underBorderWidth = {
  medium: getPixel(2),
  semibold: getPixel(2.4),
  normal: getPixel(1),
  regular: getPixel(1),
  blod: getPixel(2.4),
};

const UnderText: React.FC<IUnderText> = memo(
  ({ onClick, testID, ...props }: IUnderText) => {
    const { underColor, fontWeight = 'normal', style } = props || {};
    const { marginRight, marginTop, marginLeft, marginBottom, ...otherStyle } =
      style || {};
    const Wrap = onClick ? Touchable : View;
    const curLineHeight = style?.lineHeight || getPixel(36);
    const curFontSize = style?.fontSize || getPixel(28);
    return (
      <View className={Style.wrap}>
        <Wrap
          onClick={onClick}
          testID={testID}
          className={Style.underLine}
          debounceTime={300}
          style={{
            borderBottomColor: underColor,
            borderBottomWidth: underBorderWidth[fontWeight],
            marginRight,
            marginTop,
            marginLeft,
            marginBottom,
            height:
              (curLineHeight - curFontSize) / 2 + curFontSize + getPixel(2),
          }}
        >
          <Text {...props} style={otherStyle} />
        </Wrap>
      </View>
    );
  },
);

export default UnderText;
