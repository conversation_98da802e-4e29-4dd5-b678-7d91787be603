import React, { CSSProperties } from 'react';
import { XViewExposure as View } from '@ctrip/xtaro';
import { classNames } from '@pages/utils/util';
import styles from './index.module.scss';

export type DashedLineProps = {
  style?: CSSProperties;
  className?: string;
  direction?: 'horizontal' | 'vertical';
  dashStyle?: CSSProperties;
  repeat?: number;
};
const DashedLine: React.FC<DashedLineProps> = ({
  style,
  className,
  direction,
  repeat,
  dashStyle,
}) => {
  return (
    <View
      style={style}
      className={classNames(styles[`${direction}`], className)}
    >
      {Array(repeat)
        .fill(null)
        .map((_, i) => {
          const key = String(i);
          return (
            <View
              key={key}
              style={dashStyle}
              className={styles[`${direction}Dash`]}
            />
          );
        })}
    </View>
  );
};
DashedLine.defaultProps = {
  style: {},
  className: '',
  direction: 'horizontal',
  dashStyle: {},
  repeat: 1,
};
export default DashedLine;
