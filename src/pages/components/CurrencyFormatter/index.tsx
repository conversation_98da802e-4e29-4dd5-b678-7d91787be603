import React, { CSSProperties, memo } from 'react';
import Text, { Weight } from '../Text';

export type ICurrencyFormatter = {
  style?: CSSProperties;
  currency?: string;
  price?: number;
  currencyStyle?: CSSProperties;
  priceStyle?: CSSProperties;
  fontWeight?: Weight;
};

const CurrencyFormatter: React.FC<ICurrencyFormatter> = memo(
  ({
    style,
    currency,
    currencyStyle,
    price,
    priceStyle,
    fontWeight,
  }: ICurrencyFormatter) => {
    const currentCurrency = currency?.replace(/CNY/g, '¥');
    return (
      <Text style={style}>
        <Text fontWeight={fontWeight} style={{ ...style, ...currencyStyle }}>
          {currentCurrency}
        </Text>
        <Text fontWeight={fontWeight} style={{ ...style, ...priceStyle }}>
          {price}
        </Text>
      </Text>
    );
  },
);

export default CurrencyFormatter;
