import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import Dimensions from '@c2x/apis/Dimensions';
import React, { memo } from 'react';
import {
  XView as View,
  XBoxShadow,
  xRouter,
  XViewExposure,
} from '@ctrip/xtaro';

import BbkComponentModal from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import BbkComponentTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkComponentButton from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import BbkDashedLine from '@ctrip/rn_com_car/dist/src/Components/Basic/Dashedline';
import {
  EventUrgeStatus,
  ServiceProgressDTO,
  ServiceProgressStatus,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Order/Types/ServiceProgress';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import {
  font,
  color,
  icon,
  tokenType,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './serviceProgressModalC2xStyles.module.scss';
import { ProgressItemType } from '../../enum';
import { ProgressItem } from '../ProgressStageNew';
import { CarLog, Utils } from '../../../../Util/Index';
import { UITestID } from '../../../../Constants/Index';
import getLocalTime from '../../method';
import Texts from '../../Texts';

const { getPixel, getPixelWithIphoneXBottom, useMemoizedFn, getLineHeight } =
  BbkUtils;

const { width, height } = Dimensions.get('window');
const headerHeight = 90;
const styles = StyleSheet.create({
  contentContainerStyle: {
    paddingTop: getPixel(26),
    paddingBottom: getPixelWithIphoneXBottom(128, 60),
  },
  contentWrap: {
    width,
    maxHeight: height * 0.85 - getPixel(headerHeight),
    backgroundColor: color.white,
    marginTop: -getPixel(1),
  },
  textStyle: {
    ...font.caption1LightStyle,
    lineHeight: getLineHeight(30),
  },
  buttonWrap: {
    height: getPixel(50),
    width: getPixel(124),
    paddingTop: 0,
    paddingBottom: 0,
    paddingLeft: 0,
    paddingRight: 0,
    minHeight: getPixel(50),
    minWidth: getPixel(124),
  },
  btn: {
    borderColor: color.deepBlueBase,
    borderWidth: StyleSheet.hairlineWidth,
    borderRadius: getPixel(8),
    paddingLeft: getPixel(26),
    paddingRight: getPixel(26),
    paddingTop: getPixel(12),
    paddingBottom: getPixel(12),
  },
});

interface ServiceProgressModalProps {
  orderId?: number;
  orderStatus?: number;
  pageId?: string;
  modalVisible: boolean;
  serviceProgressList?: Array<ServiceProgressDTO>;
  urgeServiceIds?: Array<string>;
  onHide: () => void;
  urgeServiceProgress: (data) => void;
}

const ServiceProgressModal = memo(
  ({
    orderId,
    orderStatus,
    pageId,
    modalVisible,
    serviceProgressList = [],
    urgeServiceIds = [],
    onHide = Utils.noop,
    urgeServiceProgress = Utils.noop,
  }: ServiceProgressModalProps) => {
    const closeModal = () => {
      onHide();
    };
    const onPressFooter = useMemoizedFn(item => {
      const urlBase = `/rn_xtaro_car_osd/main.js?CRNType=1&CRNModuleName=rn_xtaro_car_osd&initialPage=takePhotos&orderId=${orderId}&orderStatus=${orderStatus}&eventId=${item.serviceId || ''}`;
      let url = '';
      if (item.operateBtnShowType === 1) {
        url = `${urlBase}&from=supplement`;
      } else if (item.operateBtnShowType === 2) {
        url = `${urlBase}&from=preview`;
      }
      if (!url) {
        return;
      }
      xRouter.navigateTo({ url });
    });
    return (
      <BbkComponentModal
        modalVisible={modalVisible}
        animationInDuration={400}
        animationOutDuration={200}
        onRequestClose={closeModal}
        closeModalBtnTestID={
          UITestID.car_testid_page_order_serviceprogress_modal_closemask
        }
      >
        <View
          className={c2xStyles.wrapper}
          testID={UITestID.car_testid_comp_order_ServiceProgressModal}
          style={{
            maxHeight: BbkUtils.vh(85),
            minHeight: BbkUtils.vh(40),
          }}
        >
          <View className={c2xStyles.headerWrapper}>
            <BbkComponentTouchable
              testID={
                UITestID.car_testid_page_order_serviceprogress_modal_closeicon
              }
              className={c2xStyles.iconView}
              onPress={closeModal}
            >
              <Text type="icon" className={c2xStyles.iconClose}>
                {icon.cross}
              </Text>
            </BbkComponentTouchable>
            <Text className={c2xStyles.titleStyle} fontWeight="medium">
              {Texts.serviceRecords}
            </Text>
            <View className={c2xStyles.headerRight} />
          </View>
          <ScrollView
            style={styles.contentWrap}
            contentContainerStyle={styles.contentContainerStyle}
          >
            <View className={c2xStyles.contentView}>
              {serviceProgressList.map((item, index) => {
                const {
                  serviceId,
                  createTime,
                  progressList = [],
                  status,
                  operateBtnShowName,
                  eventUrgeStatus,
                } = item;
                const createTimeDisplay = getLocalTime(createTime);
                // 增加客服催状态判断是否已催
                const isUrgeStatus =
                  !!eventUrgeStatus && eventUrgeStatus !== EventUrgeStatus.Able;
                const isUrged =
                  isUrgeStatus || urgeServiceIds.includes(serviceId);
                const info = {
                  eventId: serviceId,
                  orderId: `${orderId}`,
                  pageId,
                };
                return (
                  <XBoxShadow
                    key={`serviceProgress_${serviceId}`}
                    className={c2xStyles.serviceContainer}
                    coordinate={{ x: 0, y: getPixel(6) }}
                    color="rgba(0, 0, 0, 0.08)"
                    opacity={1}
                    blurRadius={getPixel(16)}
                    elevation={2}
                  >
                    <View className={c2xStyles.serviceTitle}>
                      <Text
                        className={c2xStyles.contentTitleText}
                        fontWeight="medium"
                      >
                        {`${Texts.serviceTime}${createTimeDisplay}`}
                      </Text>
                      {isUrged && status !== ServiceProgressStatus.Complete && (
                        <View>
                          <Text className={c2xStyles.handleServiceTipText}>
                            {Texts.handleServiceTip}
                          </Text>
                        </View>
                      )}
                      {status !== ServiceProgressStatus.Complete &&
                        !isUrged && (
                          <XViewExposure
                            testID={CarLog.LogExposure({
                              name: '曝光_事件咨询进度_催处理按钮',

                              info,
                            })}
                          >
                            <BbkComponentButton
                              onPress={() => {
                                urgeServiceProgress({
                                  orderId,
                                  eventId: serviceId,
                                });
                              }}
                              testID={`${UITestID.car_testid_page_order_serviceprogress_modal_urgeservice}_${index}`}
                              text={Texts.urgeToDeal}
                              textStyle={styles.textStyle}
                              buttonStyle={styles.buttonWrap}
                              colorType={tokenType.ColorType.Orange}
                              buttonType={tokenType.ButtonType.Gradient}
                            />
                          </XViewExposure>
                        )}
                    </View>
                    <BbkDashedLine />
                    <View className={c2xStyles.progressContainer}>
                      {progressList.map((progress, pIndex) => {
                        const { title, description, lastReplyTime } = progress;
                        return (
                          <ProgressItem
                            key={`ProgressItem_${serviceId}_${String(pIndex)}`}
                            title={title}
                            typeDesc={description}
                            isLatest={pIndex === progressList.length - 1}
                            lastReplyTime={lastReplyTime}
                            type={
                              status === ServiceProgressStatus.Complete
                                ? ProgressItemType.refundSuccess
                                : ProgressItemType.inProgress
                            }
                          />
                        );
                      })}
                      {!!operateBtnShowName && (
                        <View className={c2xStyles.btnWrapper}>
                          <BbkComponentTouchable
                            onPress={() => onPressFooter(item)}
                            style={styles.btn}
                          >
                            <Text
                              className={c2xStyles.btnText}
                              fontWeight="medium"
                            >
                              {operateBtnShowName}
                            </Text>
                          </BbkComponentTouchable>
                        </View>
                      )}
                    </View>
                  </XBoxShadow>
                );
              })}
            </View>
          </ScrollView>
        </View>
      </BbkComponentModal>
    );
  },
);

export default ServiceProgressModal;
