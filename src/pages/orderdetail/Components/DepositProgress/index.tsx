import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  XViewExposure,
  xRouter,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, icon, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import orderDetailStore, {
  getFreeDepositProgress,
  getDepositInfo,
  getOnlyVehicleDamageId,
  getDamageInfoRenderData,
} from '@pages/orderdetail/state/orderdetail';
import c2xStyles from './depositProgressC2xStyles.module.scss';
import {
  OrderDetail as OrderDetailCons,
  UITestID,
  StorageKey,
  Platform,
} from '../../../../Constants/Index';
import { Utils, CarLog, CarStorage } from '../../../../Util/Index';
import { photoBrowserShow } from '../CarImage';
import {
  IProgressInfo,
  ILabel,
  LabelType,
  ILink,
  DotLevelType,
  LinkTypes,
  IDot,
} from '../../Types';

const { getPixel } = BbkUtils;

const { DepositStatus, SupplmentType } = OrderDetailCons;

const { getBbkImageUrl } = Utils;
const styles = StyleSheet.create({
  depositProgressWrapWithFree: {
    marginTop: 0,
  },
  creditRentLogo: {
    width: getPixel(108),
    height: getPixel(32),
    borderRadius: getPixel(4),
  },
  zhimaLogo: {
    width: getPixel(130),
    height: getPixel(32),
    borderRadius: getPixel(4),
  },
  labelTextGray: {
    color: color.grayDescLine,
  },
  greenDotLarge: {
    width: getPixel(20),
    height: getPixel(20),
    position: 'absolute',
    left: getPixel(70),
    top: getPixel(9),
    zIndex: 10,
  },
  orangeDotLarge: {
    width: getPixel(20),
    height: getPixel(20),
    position: 'absolute',
    left: getPixel(70),
    top: getPixel(9),
    zIndex: 10,
  },
  zhimaView: {
    borderColor: color.C_4673BE,
    borderWidth: StyleSheet.hairlineWidth,
    borderRadius: getPixel(4),
    paddingLeft: getPixel(6),
    paddingRight: getPixel(6),
  },
  zhimaText: {
    color: color.C_4673B2,
    fontSize: getPixel(22),
    // @ts-expect-error
    fontWeight: '400',
    fontFamily: 'PingFangSC-Regular',
    lineHeight: getPixel(32),
  },
});

// 免押Logo

interface IDepositLogo {
  depositStatus: number;
  isFirst: boolean;
}
const DepositLogo = memo(({ depositStatus, isFirst }: IDepositLogo) => {
  if (!isFirst) return null;

  // 出境芝麻免押样式更新
  const tagText = {
    [DepositStatus.CreditRent]: '程信分',
    [DepositStatus.Zhima]: '芝麻免押',
  }[depositStatus];
  return (
    <View style={styles.zhimaView}>
      <Text style={styles.zhimaText}>{tagText}</Text>
    </View>
  );
});

const Label = memo(({ color, text }: ILabel) => {
  if (!text) return null;
  let suffix = '';
  if (color === LabelType.Gray) {
    suffix = 'gray';
  } else if (color === LabelType.Orange) {
    suffix = 'orange';
  } else if (color === LabelType.Green) {
    suffix = 'green';
  }
  return (
    <View className={c2xStyles.labelWrap}>
      <View className={c2xStyles.labelContent}>
        <Text
          className={c2xStyles.labelText}
          style={color === LabelType.Gray && styles.labelTextGray}
          fontWeight="medium"
        >
          {text}
        </Text>
      </View>
      <Image
        className={c2xStyles.labelBg}
        src={getBbkImageUrl(`deposit_progress_label_bg_${suffix}.png`)}
        mode="aspectFill"
      />
    </View>
  );
});

// 高亮链接
interface IHighlightLink {
  text: string;
  onPress: () => void;
}
const HighlightLink = memo(({ text, onPress }: IHighlightLink) => {
  if (!text) return null;
  return (
    <BbkTouchable
      testID={UITestID.car_testid_page_order_depositdetail_modal_highlightlink}
      className={c2xStyles.highlightLinkWrap}
      onPress={onPress}
    >
      <Text className={c2xStyles.highlightLinkText}>{text}</Text>
      <Text className={c2xStyles.highlightLinkArrowRight} type="icon">
        {icon.arrowRight}
      </Text>
      <View className={c2xStyles.touchAbleArea} />
    </BbkTouchable>
  );
});

// 扣费失败Bar
interface IDeductionFail {
  text: string;
  onPress?: () => void;
}
const DeductionFail = memo(({ text, onPress }: IDeductionFail) => {
  if (!text) return null;
  const Container = onPress ? BbkTouchable : View;
  return (
    <Container
      onPress={onPress}
      testID={UITestID.car_testid_page_order_depositdetail_modal_deductionfail}
    >
      <LinearGradient
        className={c2xStyles.deductionFailWrap}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 1.0 }}
        locations={[0, 0.5, 1]}
        colors={[
          setOpacity(color.refundFaile, 0.06),
          setOpacity(color.refundFaile, 0.06),
          setOpacity(color.white, 0),
        ]}
      >
        <View className={c2xStyles.deductionView}>
          <Text className={c2xStyles.deductionFailIcon} type="icon">
            {icon.warning}
          </Text>
          <View className={c2xStyles.deductionViewText}>
            <Text className={c2xStyles.deductionFailText}>{text}</Text>
          </View>
          {!!onPress && (
            <Text className={c2xStyles.deductionFailArrowRight} type="icon">
              {icon.arrowRight}
            </Text>
          )}
        </View>
      </LinearGradient>
    </Container>
  );
});

const LinkComponent = memo(
  ({
    type,
    desc,
    onlyVehicleDamageId,
    orderId,
    orderStatus,
    setVehicleDamageId,
    openRefundDetailModal,
  }: ILink) => {
    if (!desc) return null;
    // 车损  海外不区分扣款类型，全部以国内车损样式展示
    let onPress = Utils.noop;
    if (onlyVehicleDamageId) {
      onPress = () => {
        // 订单详情页押金弹窗-扣费详情入口_点击
        CarLog.LogCode({
          name: '订单详情页扣费详情入口_点击',

          info: {
            orderId,
            orderStatus,
            deductId: onlyVehicleDamageId,
          },
        });
        setVehicleDamageId(onlyVehicleDamageId);
        const { vehicleDamageList, scannedImages, osdDeductionList } =
          orderDetailStore.getState();
        const damageInfoRenderData = getDamageInfoRenderData(
          osdDeductionList,
          onlyVehicleDamageId,
        );
        const param = {
          storeParams: [
            {
              type: 'ORDER_CROSS_PARAMS',
              data: {
                vehicleDamageList,
                osdDeductionList,
                vehicleDamageId: onlyVehicleDamageId,
                orderId,
                scannedImages,
                damageInfoRenderData,
              },
            },
          ],
        };
        // 保存Store数据，用于传参
        CarStorage.save(
          StorageKey.CAR_CROSS_PARAMS,
          JSON.stringify(param),
          '1m',
        );
        const url = Platform.CAR_CROSS_URL.DamageDetail.OSD;
        xRouter.navigateTo({ url });
      };
    } else {
      onPress = () => {
        const {
          violationList,
          vehicleDamageList,
          osdDeductionList,
          removeDetail,
          violationDesc,
        } = orderDetailStore.getState();
        const param = {
          urlParams: {
            type: SupplmentType.VehicleDamage,
          },
          storeParams: [
            {
              type: 'ORDER_CROSS_PARAMS',
              data: {
                violationList,
                vehicleDamageList,
                osdDeductionList,
                removeDetail,
                violationDesc,
                orderId,
                orderStatus,
              },
            },
          ],
        };
        // 保存Store数据，用于传参
        CarStorage.save(
          StorageKey.CAR_CROSS_PARAMS,
          JSON.stringify(param),
          '1m',
        );
        const url = Platform.CAR_CROSS_URL.SupplementList.OSD;
        xRouter.navigateTo({ url });
      };
    }
    // 海外场景下，如果是失败场景，则展示扣款失败样式
    if (type === LinkTypes.FeeDeductionFailed) {
      return <DeductionFail text={desc} />;
    }
    return <HighlightLink text={desc} onPress={onPress} />;
  },
);

const Dot = memo(({ level, color }: IDot) => {
  if (level === DotLevelType.Large) {
    let imageUrl;
    let style;
    switch (color) {
      case LabelType.Green:
        imageUrl = getBbkImageUrl('deposit_progress_green_dot.png');
        style = styles.greenDotLarge;
        break;
      case LabelType.Orange:
        imageUrl = getBbkImageUrl('deposit_progress_orange_dot.png');
        style = styles.orangeDotLarge;
        break;
      default:
        return null;
    }
    if (imageUrl && style) {
      return <Image style={style} src={imageUrl} mode="aspectFit" />;
    }
  }
  if (color === LabelType.Orange) {
    return <View className={c2xStyles.orangeDot} />;
  }
  if (color === LabelType.Gray) {
    return <View className={c2xStyles.grayDot} />;
  }
  return null;
});

// 进度线
interface ILine {
  currentIndex: number;
  index: number;
  isLast: boolean;
}
const Line = memo(({ currentIndex, index, isLast }: ILine) => {
  if (isLast) return null;
  if (index < currentIndex) {
    return <View className={c2xStyles.grayLine} />;
  }
  return (
    <Image
      className={c2xStyles.grayDashedLine}
      src={getBbkImageUrl('deposit_progress_gray_dashed_line_2.png')}
      mode="aspectFill"
    />
  );
});

const FreeDepositProgressItem = memo(
  ({
    mainText,
    subText,
    links,
    level,
    color,
    name,
    depositStatus = 1,
    isFirst,
    isLast,
    currentIndex,
    index,
    onlyVehicleDamageId,
    deductId,
    orderId,
    orderStatus,
    setVehicleDamageId,
    openRefundDetailModal,
  }: IProgressInfo) => {
    return (
      <View className={c2xStyles.itemWrap}>
        <View className={c2xStyles.itemContent}>
          <Dot level={level} color={color} />
          <Line currentIndex={currentIndex} index={index} isLast={isLast} />
          <Label color={color} text={name} />
          <View className={c2xStyles.mainTextWrap}>
            {!!mainText && (
              <Text className={c2xStyles.mainText}>{mainText}</Text>
            )}
            <DepositLogo isFirst={isFirst} depositStatus={depositStatus} />
          </View>
          {subText?.length > 0 &&
            subText.map(item => (
              <View className={c2xStyles.subTextWrap} key={item?.subDesc}>
                {!!item?.subDesc && (
                  <Text className={c2xStyles.subText}>{item?.subDesc}</Text>
                )}
                {item?.feeVoucher?.length > 0 && (
                  <BbkTouchable
                    onPress={() => {
                      photoBrowserShow({
                        photoList:
                          item.feeVoucher.map(feeVoucherItem => ({
                            imageUrl: feeVoucherItem,
                          })) || [],
                        showPhotoIndex: 0,
                      });
                    }}
                    testID={`${UITestID.car_testid_page_order_depositdetail_modal_progress_item}_${item?.subDesc}`}
                  >
                    <Text className={c2xStyles.subTextIcon} type="icon">
                      {icon.circleQuestion}
                    </Text>
                  </BbkTouchable>
                )}
              </View>
            ))}
          {links?.length > 0 &&
            links.map(item => (
              <XViewExposure
                testID={CarLog.LogExposure({
                  name: '订单详情页扣费详情入口_曝光',

                  info: {
                    orderId,
                    orderStatus,
                    deductId,
                  },
                })}
              >
                <LinkComponent
                  key={item.type}
                  type={item.type}
                  desc={item.desc}
                  orderId={orderId}
                  orderStatus={orderStatus}
                  onlyVehicleDamageId={deductId}
                  setVehicleDamageId={setVehicleDamageId}
                  openRefundDetailModal={openRefundDetailModal}
                />
              </XViewExposure>
            ))}
        </View>
      </View>
    );
  },
);

// 进度组件
const DepositProgress = memo(props => {
  const { setVehicleDamageId, setOrderModalsVisible } =
    orderDetailStore.getState();
  const orderBaseInfo = orderDetailStore(state => state.orderBaseInfo);
  const freeDeposit = orderDetailStore(state => state.freeDeposit);
  const vehicleDamageList = orderDetailStore(state => state.vehicleDamageList);
  const freeDepositProgress = getFreeDepositProgress(freeDeposit);
  const depositInfo = getDepositInfo(freeDeposit);
  const onlyVehicleDamageId = getOnlyVehicleDamageId(vehicleDamageList);
  const openRefundDetailModal = () =>
    setOrderModalsVisible({ refundDetailModal: { visible: true } });

  const { title, subTitle, progressInfos, notice } = freeDepositProgress;
  const { orderId, orderStatus } = orderBaseInfo;
  if (!progressInfos?.length) return null;
  let currentIndex = progressInfos.findIndex(
    item => item.color === LabelType.Gray,
  );
  if (currentIndex === -1) {
    currentIndex = progressInfos.length - 1;
  } else {
    currentIndex -= 1;
  }
  const { depositStatus } = depositInfo;
  const isShowFreeDeposit = [
    DepositStatus.CreditRent,
    DepositStatus.Zhima,
  ].includes(depositStatus);

  const isShowNotice = !!notice;
  return (
    <>
      <View
        className={c2xStyles.depositProgressWrap}
        style={isShowFreeDeposit && styles.depositProgressWrapWithFree}
      >
        <View className={c2xStyles.depositProgressHeader}>
          {!!title && (
            <Text className={c2xStyles.depositProgressTitle} fontWeight="bold">
              {title}
            </Text>
          )}
          {!!subTitle && (
            <Text className={c2xStyles.depositProgressSubTitle}>
              {subTitle}
            </Text>
          )}
        </View>
        <View
          className={c2xStyles.depositProgressContent}
          testID={UITestID.c_testid_orderDetail_depositProgressContent}
        >
          <View className={c2xStyles.topGrayLine} />
          {progressInfos.length > 0 &&
            progressInfos.map((progressInfo, index) => (
              <FreeDepositProgressItem
                key={progressInfo.mainText}
                {...progressInfo}
                orderId={orderId}
                orderStatus={orderStatus?.orderStatus}
                depositStatus={depositStatus}
                isFirst={index === 0}
                isLast={index === progressInfos.length - 1}
                currentIndex={currentIndex}
                index={index}
                onlyVehicleDamageId={onlyVehicleDamageId}
                setVehicleDamageId={setVehicleDamageId}
                openRefundDetailModal={openRefundDetailModal}
              />
            ))}
        </View>
      </View>
      {isShowNotice && (
        <View className={c2xStyles.depositNoticeWrap}>
          <Text className={c2xStyles.depositNotice}>{notice}</Text>
        </View>
      )}
    </>
  );
});

export default DepositProgress;
