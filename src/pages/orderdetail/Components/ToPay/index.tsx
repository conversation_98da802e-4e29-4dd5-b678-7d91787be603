import React, { memo, useCallback, useEffect, useState, useRef } from 'react';
import {
  XAnimated,
  XBoxShadow,
  xCreateAnimation,
  XImage,
  XView,
} from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import Touchable from '@pages/components/Touchable';
import orderDetailStore, {
  handleOrderDetailGoToIns,
} from '@pages/orderdetail/state/orderdetail';
import Shark from '@pages/shark';
import EventName from '../../../../Constants/EventName';
import { CarLog, Utils } from '../../../../Util/Index';
import styles from './index.module.scss';

export interface ToPayProps {
  onTimeOut?: () => void;
}

const ToPay = ({ onTimeOut = Utils.noop }: ToPayProps) => {
  const orderInfo = orderDetailStore(state => state.orderBaseInfo);
  const ctripContinuePay = orderDetailStore(state => state.ctripContinuePay);

  const toPay = useCallback(
    data => {
      CarLog.LogCode({ name: '点击_订单详情页_继续支付卡片' });
      const goToInsFun = addInsParams => {
        handleOrderDetailGoToIns({
          eventName: EventName.insConfirmBackToOrderDetail,
          addInsParams,
        });
      };
      ctripContinuePay(data, goToInsFun);
    },
    [ctripContinuePay],
  );

  const newAnimation = useRef(
    xCreateAnimation({
      duration: 4000,
      delay: 0,
      timingFunction: 'linear',
    }),
  );
  const execAnimateIndex = useRef(1);

  const [animation, setAnimation] = useState<any>();
  const startAnimation = useCallback(() => {
    newAnimation.current.rotate(execAnimateIndex.current * 360).step();
    setAnimation(newAnimation.current.export());
    execAnimateIndex.current += 1;
  }, []);

  useEffect(() => {
    startAnimation();
    return () => {
      setAnimation(null);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleAnimationEnd = useCallback(() => {
    startAnimation();
  }, [startAnimation]);

  const { orderTip } = orderInfo ?? {};
  const continuePayInfo = orderDetailStore(state => state.continuePayInfo);
  const { needContinuePay, leftMinutes, leftSeconds } = continuePayInfo ?? {};
  const [visible, setVisible] = useState(false);
  useEffect(() => {
    setVisible(needContinuePay);
  }, [needContinuePay]);

  const [countdown, setCountdown] = useState('');

  useEffect(() => {
    let timeoutId;
    let timeLeft = leftMinutes * 60 + leftSeconds;
    const updateCountdown = () => {
      if (timeLeft <= 0) {
        onTimeOut();
        return;
      }

      const minutes = Math.floor(timeLeft / 60);
      const seconds = timeLeft % 60;
      setCountdown(
        `${minutes.toString().padStart(2, '0')}:${seconds
          .toString()
          .padStart(2, '0')}`,
      );
      timeLeft -= 1;
      timeoutId = setTimeout(updateCountdown, 1000);
    };
    updateCountdown();
    return () => {
      clearTimeout(timeoutId);
    };
  }, [leftMinutes, leftSeconds, onTimeOut]);

  if (!visible) {
    return null;
  }

  return (
    <XBoxShadow
      className={styles.container}
      coordinate={{ x: 0, y: 2 }}
      color="rgba(0, 0, 0, 0.06)"
      opacity={1}
      elevation={1}
      blurRadius={4}
    >
      <XView className={styles.content}>
        <XView className={styles.animationBox}>
          <XAnimated.Image
            animation={animation}
            onTransitionEnd={handleAnimationEnd}
            className={styles.animationImage}
            mode="aspectFill"
            src="https://dimg04.c-ctrip.com/images/1tg1412000lawyt646ECA.png"
          />
          <XImage
            className={styles.carImage}
            mode="aspectFill"
            src="https://dimg04.c-ctrip.com/images/1tg5p12000lawylh4DB93.png"
          />
        </XView>

        <XView className={styles.textContainer}>
          <Text className={styles.countdownText} fontWeight="bold">
            {countdown}
          </Text>
          <Text fontWeight="normal" className={styles.tipText}>
            {orderTip?.urgentWarning}
          </Text>
        </XView>
      </XView>
      <Touchable
        debounceTime={300}
        className={styles.pay}
        testId="ta-toPay"
        onClick={toPay}
      >
        <Text className={styles.payText} fontWeight="bold">
          {Shark.getShark('key.cars.order.goPay')}
        </Text>
      </Touchable>
    </XBoxShadow>
  );
};

export default memo(ToPay);
