@import '../../../scss/color.scss';

.container {
  border-radius: 16px;
  background: $white;
  margin-top: 24px;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  padding: 8px 34px 16px 8px;
}

.content {
  flex: 1 1 0;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
}

.animationBox {
  position: relative;
  width: 116px;
  height: 116px;
}

.animationImage {
  width: 100%;
  height: 100%;
}

.carImage {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.textContainer {
  margin-left: 12px;
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
}

.countdownText {
  font-size: 40px;
  line-height: 48px;
  color: $C_111;
}

.tipText {
  font-size: 24px;
  line-height: 32px;
  color: $C_555555;
}

.pay {
  padding: 0 30px;
  height: 72px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background: $C_FF7700;
}

.payText {
  font-size: 32px;
  letter-spacing: 0px;
  line-height: 42px;
  color: $white;
}
