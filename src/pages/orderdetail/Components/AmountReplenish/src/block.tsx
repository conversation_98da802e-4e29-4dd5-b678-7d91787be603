import React, { memo } from 'react';
import { XView as View, XBoxShadow } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import Button from '@pages/components/Button';
import CurrencyFormatter from '@pages/components/CurrencyFormatter';
import shark from '@pages/shark';
import getPixel from '@pages/utils/getPixel';
import c2xStyles from './blockC2xStyles.module.scss';
import { UITestID } from '../../../../../Constants/Index';

interface AddPaymentsType {
  toPayAmount?: number;
  currency?: string;
  totalSize?: number;
  totalAmount?: number;
  toPayCount?: number;
  additionalPaymentList?: any;
  reason?: string;
}
export interface IProps {
  addPayments: AddPaymentsType;
  waitPayArr?: any[];
  btnClick?: () => void;
  taTestID?: string;
}
const Replenish = ({
  addPayments: { toPayCount, toPayAmount, reason, currency },
  waitPayArr = [],
  btnClick,
  taTestID,
}: IProps) => {
  return (
    <XBoxShadow
      testID={taTestID}
      className={c2xStyles.subWrap}
      coordinate={{
        x: getPixel(1),
        y: getPixel(4),
      }}
      color="rgba(0, 0, 0, 0.06)"
      opacity={1}
      blurRadius={getPixel(12)}
      elevation={8}
    >
      <View style={{ flex: 1 }}>
        <View style={{ flexDirection: 'row' }}>
          <Text className={c2xStyles.amountTitle} fontWeight="medium">
            {shark.getShark('key.cars.order.waitsupplementTip', [
              `${toPayCount}`,
            ])}
          </Text>
          <CurrencyFormatter
            currency={currency || ''}
            price={toPayAmount || 0}
            className={c2xStyles.amountTitle}
          />
        </View>
        <Text numberOfLines={1} className={c2xStyles.replenishDesc}>
          {`${shark.getShark('key.cars.order.supplementReason')}${reason}`}
        </Text>
      </View>
      <Button
        testID={UITestID.car_testid_page_order_replenish_block_button}
        className={c2xStyles.toPayRightWrap}
        onClick={btnClick}
        debounceTime={300}
      >
        <Text fontWeight="bold" className={c2xStyles.toPayRightText}>
          {waitPayArr.length > 1
            ? shark.getShark('key.cars.order.seeToPay')
            : shark.getShark('key.cars.order.goPay')}
        </Text>
      </Button>
    </XBoxShadow>
  );
};
export default memo(Replenish);
