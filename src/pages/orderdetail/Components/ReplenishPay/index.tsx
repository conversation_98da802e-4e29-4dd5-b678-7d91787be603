/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { XViewExposure, xRouter } from '@ctrip/xtaro';
import orderDetailStore, {
  getOrderDataByPhone,
} from '@pages/orderdetail/state/orderdetail';
import useMemoizedFn from '@pages/hooks';
import { CarLog, EventHelper, CarStorage } from '../../../../Util/Index';
import { EventName, StorageKey, Platform } from '../../../../Constants/Index';
import InfoBlock from '../InfoBlock';

import Replenish from '../AmountReplenish/src/block';
import { IReplenishPay } from '../../Types';

const ReplenishPay: React.FC<IReplenishPay> = ({
  style,
  testID,
  isPayBlock,
  noSpace,
}) => {
  const authType = orderDetailStore(state => state.authType);
  const isOrderDataByPhone = getOrderDataByPhone(authType);
  const addPayments = orderDetailStore(state => state.additionPaymentInfo);
  const createPayment = orderDetailStore(state => state.createPayment);
  const goPayFun = useMemoizedFn(info => {
    if (isOrderDataByPhone) {
      EventHelper.sendEvent(EventName.orderBtnsClickFromPhoneCheck, {});
      return;
    }
    if (info?.selectedId) {
      CarLog.LogCode({ name: '点击_订单详情页_补款详情' });
      const { additionPaymentInfo, jumpModifyToPay, reqOrderParams } =
        orderDetailStore.getState();
      const param = {
        urlParams: {
          selectedId: info.selectedId,
        },
        storeParams: [
          {
            type: 'ORDER_CROSS_PARAMS',
            data: {
              additionPaymentInfo,
              jumpModifyToPay,
              reqOrderParams,
            },
          },
        ],
      };
      // 保存Store数据，用于传参
      CarStorage.save(StorageKey.CAR_CROSS_PARAMS, JSON.stringify(param), '1m');
      const url = Platform.CAR_CROSS_URL.Supplement.OSD;
      xRouter.navigateTo({ url });
    } else {
      const { renewalOrderLst = [], bizScene } = info; // todo
      if (bizScene === 3 && renewalOrderLst[0]?.renewalOrderId) {
        const {
          renewalOrders,
          orderBaseInfo,
          pickupStore,
          modalsVisible,
          reqOrderParams,
        } = orderDetailStore.getState();
        const param = {
          urlParams: {
            renewalOrderId: renewalOrderLst[0]?.renewalOrderId,
          },
          storeParams: [
            {
              type: 'ORDER_CROSS_PARAMS',
              data: {
                renewalOrders,
                orderBaseInfo,
                pickupStore,
                modalsVisible,
                orderId: reqOrderParams?.orderId,
              },
            },
          ],
        };
        // 保存Store数据，用于传参
        CarStorage.save(
          StorageKey.CAR_CROSS_PARAMS,
          JSON.stringify(param),
          '1m',
        );
        const url = Platform.CAR_CROSS_URL.Rerent.OSD;
        xRouter.navigateTo({ url });
        return;
      }
      CarLog.LogCode({ name: '点击_订单详情补款去支付' });
      createPayment(info);
    }
  });
  const visible = addPayments?.additionalPaymentList?.length;

  const filterArr = useMemoizedFn(list => {
    if (!list || !list.length) return [];
    const arr = list.filter(item => item.payStatus === 0);
    return arr;
  });
  const waitPayArr = filterArr(addPayments?.additionalPaymentList) || [];
  const btnClick = useMemoizedFn(() => {
    goPayFun({
      selectedId: waitPayArr.length > 0 ? 'waitPay' : 'all',
    });
  });
  const onGoPay = useMemoizedFn(() => {
    goPayFun(waitPayArr[0]);
  });
  if (!visible) return false;
  const { totalSize, totalAmount } = addPayments || {};
  const titleDesc = `${totalSize}笔，补款总额¥${totalAmount}`;

  return (
    <XViewExposure testID={visible ? testID : ''} style={style}>
      {isPayBlock && waitPayArr.length > 0 && (
        <Replenish
          addPayments={addPayments}
          waitPayArr={waitPayArr}
          btnClick={onGoPay}
          taTestID="ta-toPaySupplementary"
        />
      )}

      {!isPayBlock && (
        <InfoBlock
          title="补款记录"
          titleDesc={titleDesc}
          detailText="补款明细"
          btnClick={btnClick}
          noSpace={noSpace}
          taTestID="ts-supplementaryDetail"
        />
      )}
    </XViewExposure>
  );
};

export default ReplenishPay;
