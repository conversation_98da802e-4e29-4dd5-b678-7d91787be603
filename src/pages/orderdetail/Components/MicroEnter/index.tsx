import React, { memo } from 'react';
import { XView as View, xClassNames, XImage, xRouter } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import useMemoizedFn from '@pages/hooks';
import UnderText from '@pages/components/UnderText';
import orderDetailStore from '@pages/orderdetail/state/orderdetail';
import c2xStyles from './blockC2xStyles.module.scss';
import { UITestID, ImageUrl } from '../../../../Constants/Index';

export interface Props {}
const MicroEnter: React.FC<Props> = () => {
  const carAssistantSummary = orderDetailStore(
    state => state.carAssistantSummary,
  );
  const data = carAssistantSummary.find(item => item.type === 3);
  const btnClick = useMemoizedFn(() => {
    const jumpUrl = data?.button?.appWeChatUrl;
    xRouter.navigateTo({ url: jumpUrl });
  });
  if (!data) return null;
  const { button = {} } = data;
  return (
    <View testID="ta-wechat" className={c2xStyles.subWrap}>
      <XImage
        src={`${ImageUrl.DIMG04_PATH}1tg1112000l96gwzw89FB.png`}
        className={c2xStyles.logoImg}
      />
      <Text
        fontWeight="normal"
        className={xClassNames(c2xStyles.text, c2xStyles.mr16)}
      >
        {data.title}
      </Text>
      {!!button?.statusType && (
        <UnderText
          testID={UITestID.car_testid_page_order_replenish_block_button}
          className={c2xStyles.underText}
          onClick={btnClick}
          fontWeight="medium"
        >
          {button?.title}
        </UnderText>
      )}
    </View>
  );
};
export default memo(MicroEnter);
