@import '../../../scss/color.scss';

.container {
  padding: 32px;
  display: flex;
  flex-direction: column;
  background: $white;
}

.titleLine {
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
}

.title {
  font-size: 36px;
  letter-spacing: 0px;
  line-height: 48px;
  color: $C_111;
  font-family: PingFangSC-Semibold;
}

.tag {
  margin-left: 8px;
  padding: 0 6px;
  line-height: 36px;
  font-size: 24px;
  border: 1px solid $C_ff5500;
  letter-spacing: -0.7px;
  border-radius: 5px;
  color: $C_ff5500;
}

.iconArrow {
  margin-left: auto;
  font-size: 36px;
  color: $C_555555;
}

.textGroup {
  margin-top: 18px;
  display: flex;
  flex-direction: column;
}

.textLine {
  margin-bottom: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.number {
  font-size: 40px;
  color: $C_111;
}

.text {
  margin-left: 18px;
  font-size: 28px;
  line-height: 36px;
  color: $C_111;
}

.buttonGroup {
  margin-top: 16px;
  display: flex;
  flex-direction: row;
  gap: 16px;
}

.button {
  flex: 1 1 0;
  height: 68px;
  border-radius: 8px;
  background: $C_F0F2F5;
}

.buttonText {
  line-height: 68px;
  font-size: 28px;
  text-align: center;
  color: $C_111;
}
