import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { xRouter, XView } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import Icon from '@pages/components/Icon';
import Touchable from '@pages/components/Touchable';
import orderDetailStore from '@pages/orderdetail/state/orderdetail';
import { TipItemType } from '@pages/orderdetail/Types';
import Shark from '@pages/shark';
import { OrderStatusCtrip } from '../../../../Constants/OrderDetail';
import styles from './index.module.scss';

const testIDMap = {
  [TipItemType.PickUpMaterials]: 'ta-checkMaterials',
  [TipItemType.TRANSLATION]: 'ta-translationDocument',
};

const numberIcon = [
  '&#xf21a0;',
  '&#xf21a1;',
  '&#xf21a2;',
  '&#xf21a3;',
  '&#xf2224;',
  '&#xf2225;',
  '&#xf2226;',
  '&#xf2227;',
];

const PickupMaterials = () => {
  const rentalEssential = orderDetailStore(state => state.rentalEssential);
  const { pickUpMaterial, displayPickUpMaterial } = rentalEssential ?? {};
  const defaultIsFold = !displayPickUpMaterial;
  const carAssistantSummary = orderDetailStore(
    state => state.carAssistantSummary,
  );
  const links = carAssistantSummary.filter(
    v =>
      v.type === TipItemType.PickUpMaterials ||
      v.type === TipItemType.TRANSLATION,
  );

  const orderBaseInfo = orderDetailStore(state => state.orderBaseInfo);
  const { orderStatus } = orderBaseInfo ?? {};

  const { isShow, supportFold } = useMemo(() => {
    switch (orderStatus?.orderStatusCtrip) {
      case OrderStatusCtrip.PROCESSING:
      case OrderStatusCtrip.CONFIRMED:
        return {
          isShow: true,
          supportFold: true,
        };
      case OrderStatusCtrip.IN_SERVICE:
        return {
          isShow: true,
          supportFold: true,
        };
      case OrderStatusCtrip.WAITING_PAY:
      case OrderStatusCtrip.PAYING:
        return {
          isShow: true,
        };
      default:
        return {
          isShow: false,
        };
    }
  }, [orderStatus?.orderStatusCtrip]);

  const [isFold, setIsFold] = useState<boolean>();
  useEffect(() => {
    setIsFold(defaultIsFold);
  }, [defaultIsFold]);

  const handleFoldChange = useCallback(() => {
    if (!supportFold) {
      return;
    }
    setIsFold(!isFold);
  }, [isFold, supportFold]);

  return isShow ? (
    <XView className={styles.container}>
      <Touchable debounceTime={300} onClick={handleFoldChange}>
        <XView className={styles.titleLine}>
          <Text className={styles.title} fontWeight="semibold">
            {Shark.getShark('key.cars.order.pickupMaterials')}
          </Text>
          <Text className={styles.tag} fontWeight="bold">
            {Shark.getShark('key.cars.order.mustCarry')}
          </Text>
          {supportFold && (
            <Icon className={styles.iconArrow}>
              {isFold ? '&#xe0b2;' : '&#xe0b3;'}
            </Icon>
          )}
        </XView>
      </Touchable>
      {!isFold && (
        <XView className={styles.textGroup}>
          {pickUpMaterial?.map(({ title }, index) => (
            <XView className={styles.textLine}>
              <Icon className={styles.number}>{numberIcon[index]}</Icon>
              <Text className={styles.text}>{title}</Text>
            </XView>
          ))}
          <XView className={styles.buttonGroup}>
            {links?.map(({ title, url, type }) => (
              <Touchable
                testID={testIDMap[type]}
                className={styles.button}
                debounceTime={300}
                onClick={() => xRouter.navigateTo({ url })}
              >
                <Text className={styles.buttonText} fontWeight="bold">
                  {title}
                </Text>
              </Touchable>
            ))}
          </XView>
        </XView>
      )}
    </XView>
  ) : null;
};

export default PickupMaterials;
