import { isEmpty as lodashIsEmpty } from 'lodash-es';
import Keyboard from '@c2x/apis/Keyboard';
import Loading from '@c2x/apis/Loading';
import React, { PureComponent } from 'react';

import UIToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import { CarLog, Utils, CarFetch } from '../../../../Util/Index';
import { LogKeyDev } from '../../../../Constants/Index';
import ModifyDriverInfoInputModal from '../ModifyDriverInfoInputModal';
import Channel from '../../../../Util/Channel';
import LocalContactsModal from '../LocalContactsModal';
import { ModifyDriverInfoType, ContactType } from '../../enum';
import { LocalContactInfoType } from '../../Types';

const CTXT = {
  get errMsg() {
    return '提交失败，请稍后重试';
  },
  get submitSuccess() {
    return '提交成功';
  },
};

function pageTrace(request, res, eventtype: string, err) {
  const isSuccess =
    (res && res.baseResponse && res.baseResponse.isSuccess) || false;
  const logInfo = {
    pageId: Channel.getPageId().Order.ID,
    eventResult: isSuccess,
    extendInfo: {
      res,
      request,
      eventErrorMsg: err ? JSON.stringify(err) : '',
    },
  };
  CarLog.LogTrace({ key: eventtype, info: logInfo });
}

interface FeeProps {
  orderId: number;
  visible: boolean;
  setModifyFlightNoModalVisible: (args) => void;
  fetchOrder: () => void;
  driverInfo: any;
  type?: string;
  pickUpAreaCode?: string;
  localContactsData?: LocalContactInfoType[];
  localDateTime?: string;
}

interface FeeState {
  flightNo: string;
  isActive: boolean;
  localContactsModalVisible: boolean;
  localContactsInfo: LocalContactInfoType[];
  errorMsg: string;
}

export default class ModifyDriverInfoModal extends PureComponent<
  FeeProps,
  FeeState
> {
  constructor(props) {
    super(props);
    this.state = {
      flightNo: props.driverInfo?.flightNo || '',
      isActive: false,
      localContactsModalVisible: false,
      localContactsInfo: props.localContactsData,
      errorMsg: '',
    };
  }

  componentDidUpdate(prevProps) {
    const { localContactsData, driverInfo, visible } = this.props;
    if (
      JSON.stringify(localContactsData) !==
      JSON.stringify(prevProps.localContactsData)
    ) {
      this.setState({
        localContactsInfo: localContactsData,
      });
    }
    if (visible !== prevProps.visible) {
      this.setState({
        errorMsg: '',
      });
    }
    if (visible !== prevProps.visible) {
      this.setState({
        errorMsg: '',
      });
    }
    if (driverInfo?.flightNo !== prevProps.driverInfo?.flightNo) {
      this.setState({
        flightNo: driverInfo?.flightNo,
      });
    }
  }

  getContactWayValue = contactInfo => {
    const { pickUpAreaCode } = this.props;
    const { contactType, data } = contactInfo || {};
    if (contactType === ContactType.localPhone && data) {
      return `${pickUpAreaCode}-${data}`;
    }
    return data || '';
  };

  onSubmit = onlyVerify => {
    const { flightNo } = this.state;
    const activeContactInfo = this.getActiveContactInfo();
    const {
      fetchOrder,
      orderId,
      setModifyFlightNoModalVisible,
      localDateTime,
      type,
    } = this.props;
    const itemParam =
      type === ModifyDriverInfoType.flightNo
        ? { flightNum: flightNo || '' }
        : {
            contactWayType: activeContactInfo?.contactType,
            contactWayValue: this.getContactWayValue(activeContactInfo),
          };
    if (!onlyVerify) Loading.showMaskLoading();
    const param = {
      orderId,
      changeType: 1,
      item: itemParam,
      pickupTime: localDateTime,
      onlyVerify: !!onlyVerify,
    };
    CarFetch.changeOrder(param)
      .then(res => {
        Loading.hideMaskLoading();
        const { tipType, msg } = res || {};
        if (res && res.baseResponse && res.baseResponse.isSuccess) {
          if (!onlyVerify) {
            fetchOrder();
            setModifyFlightNoModalVisible({ visible: false });
          }
        } else if (tipType === 1 && msg) {
          // tipType 1 alert;2 txt
          UIToast.show(msg);
          fetchOrder();
        } else if (msg) {
          this.setState({
            errorMsg: msg,
          });
          Keyboard.dismiss();
        }

        pageTrace(param, res, LogKeyDev.c_car_fee_deduction, null);
      })
      .catch(err => {
        Loading.hideMaskLoading();
        if (!onlyVerify) {
          UIToast.show(CTXT.errMsg);
        }
        pageTrace(param, null, LogKeyDev.c_car_fee_deduction, err);
        fetchOrder();
      });
    CarLog.LogCode({
      name: '点击_订单详情页_编辑航班号',

      pageId: Channel.getPageId().Order.ID,
      flightNo,
    });
  };

  changeFlightNo = value => {
    let text = value;
    const { type } = this.props;
    const { flightNo } = this.state;
    if (Utils.strTrim(value)?.length > 20) return;
    if (flightNo?.length < value.length) {
      // 非删除时
      text = Utils.strTrim(value);
      // text = Utils.strFormat(text, 2);
    }
    if (type === ModifyDriverInfoType.flightNo) {
      this.setState({
        flightNo: text,
        isActive: !lodashIsEmpty(text),
      });
    }
  };

  changeLocalContactInfo = value => {
    const { localContactsInfo } = this.state;
    const activeContactInfo = this.getActiveContactInfo();
    const data = localContactsInfo.map(item =>
      item.contactType === activeContactInfo.contactType
        ? { ...item, data: value }
        : item,
    );
    this.setState({
      isActive: !lodashIsEmpty(value),
      localContactsInfo: data,
    });
  };

  hideLocalContactsModal = () => {
    this.setState({ localContactsModalVisible: false });
  };

  onSelectContactType = () => {
    this.setState({ localContactsModalVisible: true });
  };

  changeContactType = info => {
    const { localContactsInfo } = this.state;
    const data = localContactsInfo.map(item =>
      item.contactType === info.contactType
        ? { ...item, isSelected: true }
        : { ...item, isSelected: false },
    );
    this.setState({
      localContactsInfo: data,
      localContactsModalVisible: false,
    });
  };

  getActiveContactInfo = () => {
    const { localContactsInfo } = this.state;
    let activeContactInfo = localContactsInfo.find(item => !!item.isSelected);
    if (!activeContactInfo && localContactsInfo.length) {
      [activeContactInfo] = localContactsInfo;
    }
    return activeContactInfo;
  };

  closeModal = () => {
    const { setModifyFlightNoModalVisible } = this.props;
    setModifyFlightNoModalVisible({ visible: false });
    this.setState({ errorMsg: '' });
  };

  clearFlightErrorMsg = () => {
    this.setState({ errorMsg: '' });
  };

  render() {
    const { visible, type, pickUpAreaCode, driverInfo } = this.props;
    const {
      flightNo,
      isActive,
      localContactsModalVisible,
      errorMsg,
      localContactsInfo,
    } = this.state;
    const { decryptTelphone, areaCode } = driverInfo || {};
    const activeContactInfo = this.getActiveContactInfo();
    const mobile =
      areaCode && decryptTelphone ? `+${areaCode}-${decryptTelphone}` : '';
    return (
      <>
        <ModifyDriverInfoInputModal
          visible={visible}
          title="修改信息"
          type={type}
          iptTitle="驾驶员航班号"
          btnDisabled={!isActive && !flightNo}
          flightIptValue={flightNo}
          buttonTxt="提交"
          iptPlaceholder="驾驶员航班号"
          onRequestClose={this.closeModal}
          onSubmit={this.onSubmit}
          onChangeFlightNo={this.changeFlightNo}
          onChangeLocalContactInfo={this.changeLocalContactInfo}
          onSelectContactType={this.onSelectContactType}
          localContactInfo={activeContactInfo}
          pickUpAreaCode={pickUpAreaCode}
          mobile={mobile}
          errorMsg={errorMsg}
          clearFlightErrorMsg={this.clearFlightErrorMsg}
        />

        <LocalContactsModal
          visible={localContactsModalVisible}
          onClose={this.hideLocalContactsModal}
          useCRNModal={true}
          onSelectContact={this.changeContactType}
          selectedType={activeContactInfo?.contactType}
          pickUpAreaCode={pickUpAreaCode}
          optionalContactsData={localContactsInfo}
        />
      </>
    );
  }
}
