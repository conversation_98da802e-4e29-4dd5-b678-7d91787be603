@import '../../../scss/color.scss';

.vehicleDescRefactor {
  flex: 1;
}

.vehName {
  font-size: 36px;
  font-family: PingFangSC-Semibold;
  line-height: 48px;
  color: $C_111;
}
.vehNameUnderLine {
  font-size: 36px;
  line-height: 48px;
  font-family: PingFangSC-Semibold;
  color: $C_111;
}
.ml16 {
  margin-left: 16px;
}

.cameraBtn {
  height: 78px;
  background-color: $C_F0F2F5;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 24px;
}
.cameraImg {
  width: 40px;
  height: 40px;
  margin-right: 12px;
}
.cameraText {
  color: $C_111;
  font-size: 28px;
  font-family: PingFangSC-Medium;
  line-height: 38px;
}
.wrap {
  padding: 40px 32px;
  background-color: $white;
  margin-bottom: 16px;
}
.wrap2 {
  padding: 32px;
  padding-bottom: 16px;
}
.content {
  flex-direction: row;
  position: relative;
  justify-content: space-between;
}
.flex1 {
  flex: 1;
}
.line {
  flex-direction: row;
  position: relative;
}
.vehicleImageLeft {
  width: 210px;
  height: 140px;
  margin-right: 20px;
}
.vehicleImageRight {
  width: 170px;
  height: 111px;
}
.vendorImage {
  width: 86px;
  height: 47px;
  margin-right: 8px;
}
.vehHeaderContainer {
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
}
