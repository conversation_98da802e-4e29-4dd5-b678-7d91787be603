/* eslint-disable import/no-extraneous-dependencies */
import React, { memo } from 'react';
import { XView as View, XImage, xRouter } from '@ctrip/xtaro';
import Button from '@pages/components/Button';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Text from '@pages/components/Text';
import Touchable from '@pages/components/Touchable';
import { useMemoizedFn } from '@pages/hooks';
import memoize from 'memoize-one';
import orderDetailStore from '@pages/orderdetail/state/orderdetail';
import shark from '@pages/shark';
import UnderText from '@pages/components/UnderText';
import c2xStyles from './vehicleC2xStyles.module.scss';
import { SimpleVehicleDesc } from '../CarVehicleDescribe';
import { CarLog } from '../../../../Util/Index';
import { UITestID, ImageUrl } from '../../../../Constants/Index';
import { FUELTYPE } from '../../../../Constants/OrderDetail';
import Texts from '../../Texts';
import {
  VehicleInfoType,
  VendorInfoType,
  OrderBaseInfoType,
} from '../../../../Types/Dto/OrderDetailRespaonseType';

type IObj = {
  [key: string]: any;
};
export interface VehicleBootProps {
  vehicleInfo: VehicleInfoType;
  vendorInfo: VendorInfoType;
  similarVehicleInfo: any;
  finalQueryIsFinish: boolean;
  logBaseInfo?: IObj;
  setOrderModalsVisible: (data: any) => void;
  orderBaseInfo: OrderBaseInfoType;
  takeCarPhotoData: TakeCarPhotoData;
  isInModal?: boolean;
}

export const mappingFuelType = memoize((fueltype: any = {}) => {
  let fuelIcon = '&#xf15ff;';
  let curtext = '';
  switch (fueltype) {
    case FUELTYPE.gasoline:
      curtext = Texts.gasolineText;
      break;
    case FUELTYPE.diesel:
      curtext = Texts.dieseloilText;
      break;
    case FUELTYPE.hybrid:
      curtext = Texts.hybridText;
      fuelIcon = '&#xf1600;';
      break;
    case FUELTYPE.electric:
      curtext = Texts.electricText;
      fuelIcon = '&#xf15fe;';
      break;
    case FUELTYPE.fuel:
      curtext = Texts.fuelText;
      break;
    case FUELTYPE.unKnownFuel:
      curtext = Texts.unKnownFuelText;
      break;
    default:
      break;
  }
  return {
    text: curtext,
    icon: {
      iconContent: fuelIcon,
      iconVersion: 'v2',
    },
    type: fueltype,
    taTestID: 'ta-fuel',
  };
});

export const getVehicleLabelsHorizontal = (vehicleProps = {}) => {
  const vehicle: any = vehicleProps;
  const { passengerNum: passengerNo, doorNum: doorNo } = vehicle;
  let vehicleLabelsHorizontal = [
    passengerNo && {
      text: `${passengerNo}${'座'}`,
    },
    doorNo && {
      text: `${doorNo}${'门'}`,
    },
  ];

  vehicleLabelsHorizontal = vehicleLabelsHorizontal.filter(v => v);
  return vehicleLabelsHorizontal as any;
};

export const getVehicleLabels = (vehicle: any = {}) => {
  const { hasAC, transmission, fuelType, fourDrive } = vehicle;
  let vehicleLabels = [
    transmission && {
      text: transmission === 'MT' ? '手动挡' : '自动挡',
    },
    hasAC && {
      text: 'A/C',
    },
    fuelType && mappingFuelType(`${fuelType}`),
    fourDrive && {
      text: Texts.driveModeText,
      type: 'driveMode',
    },
  ];

  vehicleLabels = vehicleLabels.filter(v => !!v?.text);

  return vehicleLabels;
};

export const getLuggageLabel = (vehicle: any = {}) => {
  const { luggageNum } = vehicle;
  let vehicleLabels = [
    luggageNum && {
      text: `${luggageNum}${'个24寸行李箱'}`,
      rightIcon: {
        iconContent: '&#xe010;',
      },
      type: 'luggage',
      taTestID: 'ta-suitcase',
    },
  ];

  vehicleLabels = vehicleLabels.filter(v => !!v?.text);

  return vehicleLabels;
};
interface TakeCarPhotoData {
  title: string;
  subTitle: string;
}
interface TakeCarPhotoProps {
  takeCarPhotoData: TakeCarPhotoData;
  logBaseInfo?: IObj;
  orderId?: string;
  orderStatus?: number;
}
const TakeCarPhoto = memo(
  ({
    takeCarPhotoData,
    logBaseInfo,
    orderId,
    orderStatus,
  }: TakeCarPhotoProps) => {
    const { title, subTitle } = takeCarPhotoData || {};
    // TODO 取还车拍照留证链接服务端返回，前端直接跳转 待联调
    const goTakePhoto = useMemoizedFn(() => {
      const url = `/rn_xtaro_car_osd/main.js?CRNType=1&CRNModuleName=rn_xtaro_car_osd&initialPage=takePhotos&orderId=${orderId}&orderStatus=${orderStatus}`;
      xRouter.navigateTo({ url });
    });
    return (
      <View testID="ta-takePhoto">
        <Button
          testID={CarLog.LogExposure({
            name: '曝光_履约卡_拍照入口',
            info: logBaseInfo,
          })}
          buttonSize="lg"
          className={c2xStyles.cameraBtn}
          onClick={goTakePhoto}
          debounceTime={300}
        >
          <XImage
            src={`${ImageUrl.DIMG04_PATH}1tg2r12000l9drurq9254.png`}
            className={c2xStyles.cameraImg}
          />
          <Text className={c2xStyles.cameraText} fontWeight="medium">
            {title}
            {subTitle ? `· ${subTitle}` : ''}
          </Text>
        </Button>
      </View>
    );
  },
);

const Vehicle = memo(
  ({
    vehicleInfo,
    vendorInfo,
    similarVehicleInfo,
    finalQueryIsFinish,
    logBaseInfo,
    setOrderModalsVisible,
    orderBaseInfo,
    takeCarPhotoData,
    isInModal,
  }: VehicleBootProps) => {
    const jumpVehicleInfo = () => {
      CarLog.LogCode({
        name: '点击_订单详情页_车辆详情',
        info: {
          orderId: String(orderBaseInfo?.orderId),
          orderStatus: orderBaseInfo?.orderStatusDesc,
        },
      });
      setOrderModalsVisible({
        vehicleInfoModal: {
          visible: true,
        },
      });
    };

    const pressFuelDesc = () => {
      setOrderModalsVisible({
        fuelDescModalVisible: {
          visible: true,
        },
      });
    };

    if (!vehicleInfo || !vendorInfo) return null;
    const { vehicleName, imageUrl } = vehicleInfo || {};
    const { vendorImageUrl } = vendorInfo || {};
    const vehicleLabelsHorizontal = getVehicleLabelsHorizontal(vehicleInfo);
    const vehicleLabels = getVehicleLabels(vehicleInfo);
    const luggageLabel = getLuggageLabel(vehicleInfo);
    const Wrapper = isInModal ? View : Touchable;
    return (
      <Wrapper
        testID={
          !isInModal
            ? CarLog.LogExposure({
                name: '曝光_订单详情页_车辆信息模块',
                info: logBaseInfo,
              })
            : ''
        }
        debounceTime={300}
        disabled={!finalQueryIsFinish || isInModal}
        className={isInModal ? c2xStyles.wrap2 : c2xStyles.wrap}
        onClick={jumpVehicleInfo}
      >
        <View
          className={c2xStyles.content}
          testID={UITestID.car_testid_page_order_detail_car_detail_btn}
        >
          {isInModal && (
            <View className={c2xStyles.line}>
              <XImage
                src={BbkUtils.fixProtocol(imageUrl)}
                className={c2xStyles.vehicleImageRight}
                mode="aspectFit"
              />
            </View>
          )}
          <View
            style={{
              flex: 1,
              justifyContent: isInModal ? 'center' : 'flex-start',
            }}
          >
            {!isInModal && (
              <View className={c2xStyles.vehHeaderContainer}>
                <XImage
                  src={BbkUtils.fixProtocol(vendorImageUrl)}
                  className={c2xStyles.vendorImage}
                  mode="aspectFit"
                />
                <Text className={c2xStyles.vehName} fontWeight="bold">
                  {vehicleName}
                </Text>
                <UnderText
                  className={c2xStyles.vehNameUnderLine}
                  fontWeight="semibold"
                >
                  {!!vehicleInfo.special &&
                    `${shark.getShark('key.cars.common.specifiedModel')}`}
                  {!!similarVehicleInfo?.introduce &&
                    `${shark.getShark('key.cars.list.orSimilar')}`}
                </UnderText>
              </View>
            )}
            <View className={c2xStyles.line}>
              <View className={c2xStyles.vehicleDescRefactor}>
                <SimpleVehicleDesc
                  data={[
                    vehicleInfo?.vehicleGroupName && {
                      text: vehicleInfo.vehicleGroupName,
                    },
                    ...vehicleLabelsHorizontal,
                    ...vehicleLabels,
                  ]}
                  showFuelDesc={true}
                  fuelDescOnPress={pressFuelDesc}
                  isOrderDetail={true}
                  isDarkColor={true}
                  isInModal={isInModal}
                />
                {!!luggageLabel?.length && (
                  <SimpleVehicleDesc
                    data={luggageLabel}
                    lastIsBlock={true}
                    showFuelDesc={true}
                    isOrderDetail={true}
                    isDarkColor={true}
                    isInModal={isInModal}
                  />
                )}
              </View>
            </View>
          </View>
          {!isInModal && (
            <View className={c2xStyles.line}>
              <XImage
                src={BbkUtils.fixProtocol(imageUrl)}
                className={c2xStyles.vehicleImageRight}
              />
            </View>
          )}
        </View>
        {!!takeCarPhotoData && (
          <TakeCarPhoto
            takeCarPhotoData={takeCarPhotoData}
            logBaseInfo={logBaseInfo}
            orderId={orderBaseInfo?.orderId}
            orderStatus={orderBaseInfo?.orderStatus?.orderStatus}
          />
        )}
      </Wrapper>
    );
  },
);
interface OrderVehicleProps {
  isInModal?: boolean;
  logBaseInfo?: IObj;
}
const OrderVehicleFC: React.FC<OrderVehicleProps> = ({
  isInModal,
  logBaseInfo,
}) => {
  const vehicleInfo = orderDetailStore(state => state.vehicleInfo);
  const vendorInfo = orderDetailStore(state => state.vendorInfo);
  const similarVehicleInfo = orderDetailStore(
    state => state.similarVehicleInfo,
  );
  const orderBaseInfo = orderDetailStore(state => state.orderBaseInfo);
  const finalQueryIsFinish = orderDetailStore(
    state => state.queryOrderAllDataSuccess,
  );

  const { setOrderModalsVisible } = orderDetailStore.getState();
  const carAssistantSummary = orderDetailStore(
    state => state.carAssistantSummary,
  );
  // 车况拍照
  const takeCarPhotoData = carAssistantSummary.find(item => item.type === 7);
  return (
    <Vehicle
      logBaseInfo={logBaseInfo}
      vehicleInfo={vehicleInfo}
      vendorInfo={vendorInfo}
      similarVehicleInfo={similarVehicleInfo}
      orderBaseInfo={orderBaseInfo}
      finalQueryIsFinish={finalQueryIsFinish}
      setOrderModalsVisible={setOrderModalsVisible}
      takeCarPhotoData={takeCarPhotoData}
      isInModal={isInModal}
    />
  );
};

export default memo(OrderVehicleFC);
