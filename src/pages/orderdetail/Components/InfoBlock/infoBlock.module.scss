@import '../../../scss/color.scss';

.wrap {
  padding-left: 32px;
  padding-right: 32px;
  background-color: $white;
}
.noSpace {
  padding-left: 0;
  padding-right: 0;
}
.content {
  padding-top: 36px;
  padding-bottom: 32px;
}
.line {
  height: 1px;
  transform: scaleY(0.5);
  background-color: $C_e5e5e5;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.title {
  margin-right: 24px;
  font-size: 28px;
  color: $C_111;
}
.titleDesc {
  color: $C_111;
  font-size: 28px;
}
.flexRow {
  flex-direction: row;
  align-items: center;
  margin-bottom: 24px;
}
.btnWrap {
  font-size: 28px;
  line-height: 36px;
  color: $C_111;
  font-family: PingFangSC-Medium;
}
.text {
  color: $C_111;
  font-size: 28px;
}
.label {
  border-radius: 5px;
  border-width: 1px;
  border-style: solid;
  border-color: $C_869ebf;
  padding-left: 5px;
  padding-right: 5px;
  height: 36px;
  margin-left: 10px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.labelText {
  font-size: 24px;
  font-family: PingFangSC-Medium;
  line-height: 36px;
  color: $blue5;
}
