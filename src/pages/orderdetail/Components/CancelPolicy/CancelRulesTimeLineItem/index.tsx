/* eslint-disable react-native/no-color-literals */
import React from 'react';
import { XView as View, xClassNames } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import getPixel from '@pages/utils/getPixel';
import DashedLine from '@pages/components/DashedLine';
import c2xStyles from './index.module.scss';

const CancelRulesTimeLineItem = (props: any) => {
  const { cancelRuleInfo, isActive, hasDash } = props;
  const { title, description } = cancelRuleInfo || {};
  return (
    <View>
      <View
        className={xClassNames(
          c2xStyles.cancelRuleTimeLine,
          isActive && c2xStyles.mt8,
        )}
      >
        <View
          className={xClassNames(
            c2xStyles.dot,
            isActive && c2xStyles.dotActive,
          )}
        />
        <Text className={c2xStyles.lineText}>{description}</Text>
        <Text
          className={xClassNames(
            c2xStyles.lineDescription1,
            isActive && c2xStyles.lineDescription2,
          )}
          fontWeight="medium"
        >
          {title}
        </Text>
      </View>
      {!!hasDash && (
        <DashedLine
          direction="vertical"
          dashStyle={{
            backgroundColor: '#d5d5d5',
            width: getPixel(3),
          }}
          className={c2xStyles.dashedLine}
          repeat={3}
        />
      )}
    </View>
  );
};

export default CancelRulesTimeLineItem;
