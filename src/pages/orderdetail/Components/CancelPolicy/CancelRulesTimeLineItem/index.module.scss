@import '../../../../scss/color.scss';

.cancelRuleTimeLine {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 36px;
  position: relative;
  z-index: 10;
}
.mt8 {
  margin-bottom: 8px;
}
.dot {
  width: 20px;
  height: 20px;
  border-radius: 20px;
  border: 3px solid $C_555555;
  margin-right: 28px;
  margin-left: 8px;
}
.dotActive {
  width: 26px;
  height: 26px;
  border: 5px solid $C_D1E5FF;
  background-color: $blue1;
  margin-left: 5px;
  margin-right: 25px;
}
.lineText {
  margin-right: 24px;
  font-size: 28px;
  color: $C_111;
  line-height: 36px;
  font-family: PingFangSC-Regular;
}
.lineDescription2 {
  font-size: 28px;
  color: $blue5;
}
.lineDescription1 {
  font-size: 28px;
  line-height: 36px;
  color: $blackOrigin;
  font-family: PingFangSC-Medium;
}
.dashedLine {
  position: absolute;
  left: 16px;
  bottom: -5px;
  z-index: 1;
}
