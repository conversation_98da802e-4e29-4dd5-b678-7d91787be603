@import '../../../scss/color.scss';

.cancelRuleNotice {
  font-size: 22px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $gray999;
  margin-top: 12px;
}
.cancelRuleBottomDesc {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $gray666;
}
.cancelRuleBottomDescIcon {
  margin-right: 8px;
  font-size: 28px;
  align-self: flex-start;
}
.cancelDesc {
  color: $gray999;
}
.Container {
  padding-top: 32px;
  background-color: $white;
}
.newCancelRuleWrap {
  padding-bottom: 0px;
  margin-bottom: -1px;
  z-index: 10;
}
.PTitle {
  font-size: 28px;
  line-height: 36px;
  margin-top: 16px;
  color: $C_111;
}
.buttons {
  margin-top: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.cancelPolicyText {
  font-size: 28px;
  line-height: 36px;
  font-family: PingFangSC-Medium;
  color: $C_111;
  margin-top: 16px;
}
.description {
  font-size: 28px;
  color: $C_111;
  line-height: 36px;
}
.free {
  color: $blue5;
}
