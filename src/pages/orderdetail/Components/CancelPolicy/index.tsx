import React, { useState } from 'react';
import { XView as View, xClassNames } from '@ctrip/xtaro';
import useMemoizedFn from '@pages/hooks';
import UnderText from '@pages/components/UnderText';
import Shark from '@pages/shark';
import Text from '@pages/components/Text';
import c2xStyles from './index.module.scss';
import { IPenaltyChangeTip } from '../../Types';
import CancelPolicyModalOSD from '../CancelPolicyModalOSD';
import CancelRulesTimeLineItem from './CancelRulesTimeLineItem';
import { getCancelRules } from '../ModifyOrderInfoExplain/src/ModifyOrderInfoExplainOSD';

interface IProps {
  cancelRuleInfo: any;
  orderBaseInfo: any;
  isPenaltyChange?: boolean;
  penaltyChangeTip?: IPenaltyChangeTip;
  testID?: string;
}
const CancelPolicy = (data: IProps) => {
  const [cancelPolicyModalVisible, setCancelPolicyModalVisible] =
    useState(false);
  const showCancelPolicyModal = useMemoizedFn(() => {
    setCancelPolicyModalVisible(true);
  });
  const onPolicyModalClose = useMemoizedFn(() => {
    setCancelPolicyModalVisible(false);
  });
  if (!data?.cancelRuleInfo && !data?.orderBaseInfo) return null;
  const { cancelRuleInfo } = data || {};
  const { topTips } = cancelRuleInfo || {};
  // type 1免费 0收费
  return (
    <>
      <View className={xClassNames(c2xStyles.Container)} testID={data?.testID}>
        {topTips?.length === 1 && (
          <Text
            className={xClassNames(
              c2xStyles.description,
              topTips[0]?.type === 1 && c2xStyles.free,
            )}
            fontWeight="medium"
          >
            {topTips[0]?.description}
          </Text>
        )}
        {topTips?.length > 1 &&
          topTips.map((item, idx) => (
            <CancelRulesTimeLineItem
              key={item.key}
              cancelRuleInfo={item}
              isActive={idx === 0}
              hasDash={idx !== topTips.length - 1}
            />
          ))}
        <UnderText
          className={c2xStyles.cancelPolicyText}
          onClick={showCancelPolicyModal}
          testID="ta-cancellationPolicy"
          fontWeight="medium"
        >
          {Shark.getShark('key.cars.book.cancelPolicy')}
        </UnderText>
      </View>
      <CancelPolicyModalOSD
        cancelRuleInfo={getCancelRules(cancelRuleInfo)}
        visible={cancelPolicyModalVisible}
        onClose={onPolicyModalClose}
      />
    </>
  );
};

export default CancelPolicy;
