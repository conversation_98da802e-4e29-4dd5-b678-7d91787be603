/* eslint-disable react/jsx-props-no-spreading */
import React, { useCallback, useMemo } from 'react';
import orderDetailStore from '@pages/orderdetail/state/orderdetail';
import serviceStore from '@pages/orderdetail/state/service';
import { XLinearGradient, xRouter, XView } from '@ctrip/xtaro';
import Touchable from '@pages/components/Touchable';
import Text from '@pages/components/Text';
import UnderText from '@pages/components/UnderText';
import useMemoizedFn from '@pages/hooks';
import Shark from '@pages/shark';
import SpecialTextTip from './SpecialTextTip';
import ToPay, { ToPayProps } from '../ToPay';
import Service, { ServiceProps } from '../Service';
import StoreMessageEntry from '../StoreMessageEntry';
import ReplenishPay from '../ReplenishPay';
import { OrderStatusCtrip } from '../../../../Constants/OrderDetail';
import { Platform } from '../../../../Constants/Index';
import styles from './index.module.scss';
import { CarLog } from '../../../../Util/Index';

interface OrderStatusProps extends ServiceProps, ToPayProps {}

const OrderStatus = ({
  showConsultProgressModal,
  onTimeOut,
}: OrderStatusProps) => {
  const orderInfo = orderDetailStore(state => state.orderBaseInfo);
  const addPayments = orderDetailStore(state => state.additionPaymentInfo);
  const queryOrderAllDataSuccess = orderDetailStore(
    state => state.queryOrderAllDataSuccess,
  );
  const { orderStatus, orderTip, osdOriginOrderId, orignalNewOrderDetail } =
    orderInfo ?? {};

  const openOriginOrder = useCallback(() => {
    if (osdOriginOrderId) {
      xRouter.navigateTo({
        url: orignalNewOrderDetail
          ? `${Platform.CAR_CROSS_URL.ORDERDETAIL.NEWOSD2}&orderId=${osdOriginOrderId}`
          : `${Platform.CAR_CROSS_URL.ORDERDETAIL.NEWOSD}&orderId=${osdOriginOrderId}`,
      });
    }
  }, [orignalNewOrderDetail, osdOriginOrderId]);

  const { linearGradientColors, textColor, tip, showToPay } = useMemo(() => {
    const plainTextTip = !!orderTip?.tipContentArray?.length && (
      <Text>
        {orderTip.tipContentArray.map(text => (
          <Text className={styles.tipText}>{text}</Text>
        ))}
      </Text>
    );
    switch (orderStatus?.orderStatusCtrip) {
      case OrderStatusCtrip.WAITING_PAY:
        return {
          linearGradientColors: ['#FEF8F3', '#FEF2E9'],
          textColor: '#FF5500',
          tip: osdOriginOrderId ? (
            <Text className={styles.tipView}>
              {plainTextTip}
              <UnderText
                className={styles.originOrderText}
                fontWeight="medium"
                style={{ marginTop: 3 }}
                onClick={openOriginOrder}
              >
                {Shark.getShark('key.cars.order.viewOriginalOrder')}
              </UnderText>
            </Text>
          ) : (
            <XView className={styles.tipView}>
              <SpecialTextTip
                orderStatusCtrip={orderStatus?.orderStatusCtrip}
                tipContentsWithTag={orderTip?.tipContentsWithTag}
              />
            </XView>
          ),
          showToPay: true,
        };
      case OrderStatusCtrip.CONFIRMED:
        return {
          linearGradientColors: ['#EFFBF7', '#E3F6F0'],
          textColor: '#06875A',
          tip: orderTip?.tipContentsWithTag && (
            <XView className={styles.tipView}>
              <Touchable
                testID="ta-rentalVoucher"
                debounceTime={300}
                onClick={() =>
                  orderTip?.tipContentsWithTag?.url &&
                  xRouter.navigateTo({ url: orderTip.tipContentsWithTag.url })
                }
              >
                <SpecialTextTip
                  orderStatusCtrip={orderStatus?.orderStatusCtrip}
                  tipContentsWithTag={orderTip.tipContentsWithTag}
                />
              </Touchable>
            </XView>
          ),
        };
      default:
        return {
          linearGradientColors: ['#F3F9FF', '#EBF4FF'],
          textColor: '#111111',
          tip: plainTextTip && (
            <XView className={styles.tipView}>{plainTextTip}</XView>
          ),
        };
    }
  }, [
    openOriginOrder,
    orderStatus?.orderStatusCtrip,
    orderTip?.tipContentArray,
    orderTip?.tipContentsWithTag,
    osdOriginOrderId,
  ]);
  const didNoticeData = orderDetailStore(state => state.didNoticeData);
  const serviceTitle = serviceStore(state => state.serviceTitle);
  const filterArr = useMemoizedFn(list => {
    if (!list || !list.length) return [];
    const arr = list.filter(item => item.payStatus === 0);
    return arr;
  });
  const waitPayArr = filterArr(addPayments?.additionalPaymentList) || [];
  const isHasReplenish = waitPayArr.length > 0;
  const isHasStoreMessage = didNoticeData?.noticeList?.length > 0;
  const isHasService = !!serviceTitle;

  return (
    <XLinearGradient
      useAngle={true}
      angle={180}
      locations={[0, 1]}
      colors={linearGradientColors}
    >
      <XView className={styles.container}>
        <Text
          className={styles.statusText}
          style={{ color: textColor }}
          fontWeight="semibold"
        >
          {orderStatus?.orderStatusDesc}
        </Text>
        {!!queryOrderAllDataSuccess && (
          <>
            {tip}
            {(showToPay ||
              isHasReplenish ||
              isHasStoreMessage ||
              isHasService) && (
              <XView className={styles.entryWrap}>
                {showToPay && <ToPay onTimeOut={onTimeOut} />}
                <ReplenishPay
                  isPayBlock={true}
                  testID={CarLog.LogExposure({
                    name: '曝光_订单详情页_补款去支付模块',
                    info: {
                      orderId: orderInfo?.orderId,
                      orderStatus,
                    },
                  })}
                />
                <StoreMessageEntry />
                <Service showConsultProgressModal={showConsultProgressModal} />
              </XView>
            )}
          </>
        )}
      </XView>
    </XLinearGradient>
  );
};

export default OrderStatus;
