import React, { memo } from 'react';
import { XView } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import UnderText from '@pages/components/UnderText';
import { OrderStatusCtrip } from '../../../../../Constants/OrderDetail';
import Style from './index.module.scss';

interface ISpecialTextTip {
  orderStatusCtrip: OrderStatusCtrip;
  tipContentsWithTag: any;
}

const SpecialTextTip = memo(
  ({ orderStatusCtrip, tipContentsWithTag }: ISpecialTextTip) => {
    if (!tipContentsWithTag) {
      return null;
    }
    const { tagDesc, tagContent } = tipContentsWithTag;
    if (!tagDesc) {
      return null;
    }
    if (tagDesc.indexOf('{tag}') === -1) {
      return tagDesc;
    }
    const [first, second] = tagDesc.split('{tag}');
    const isConfirmed =
      orderStatusCtrip === OrderStatusCtrip.CONFIRMED && !!tagContent;
    const Wrap = isConfirmed ? XView : Text;
    return (
      <Wrap className={isConfirmed && Style.wrap}>
        {!!first && (
          <Text fontWeight="normal" className={Style.normalText}>
            {first}
          </Text>
        )}
        {orderStatusCtrip === OrderStatusCtrip.WAITING_PAY && !!tagContent && (
          <Text fontWeight="medium" className={Style.textStyle}>
            {tagContent}
          </Text>
        )}
        {isConfirmed && (
          <UnderText
            fontWeight="medium"
            className={Style.underText}
            style={{ marginLeft: 2 }}
          >
            {tagContent}
          </UnderText>
        )}
        {!!second && (
          <Text fontWeight="normal" className={Style.normalText}>
            {second}
          </Text>
        )}
      </Wrap>
    );
  },
);

export default SpecialTextTip;
