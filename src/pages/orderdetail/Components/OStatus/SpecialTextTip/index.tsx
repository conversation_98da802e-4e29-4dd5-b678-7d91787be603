import React, { memo } from 'react';
import Text from '@pages/components/Text';
import UnderText from '@pages/components/UnderText';
import { OrderStatusCtrip } from '../../../../../Constants/OrderDetail';
import Style from './index.module.scss';

interface ISpecialTextTip {
  orderStatusCtrip: OrderStatusCtrip;
  tipContentsWithTag: any;
}

const SpecialTextTip = memo(
  ({ orderStatusCtrip, tipContentsWithTag }: ISpecialTextTip) => {
    if (!tipContentsWithTag) {
      return null;
    }
    const { tagDesc, tagContent } = tipContentsWithTag;
    if (!tagDesc) {
      return null;
    }
    if (tagDesc.indexOf('{tag}') === -1) {
      return tagDesc;
    }
    const [first, second] = tagDesc.split('{tag}');
    return (
      <Text>
        {!!first && (
          <Text fontWeight="normal" className={Style.normalText}>
            {first}
          </Text>
        )}
        {orderStatusCtrip === OrderStatusCtrip.WAITING_PAY && !!tagContent && (
          <Text fontWeight="medium" className={Style.textStyle}>
            {tagContent}
          </Text>
        )}
        {orderStatusCtrip === OrderStatusCtrip.CONFIRMED && !!tagContent && (
          <UnderText
            fontWeight="medium"
            className={Style.normalText}
            style={{ marginLeft: 2, marginTop: 3 }}
          >
            {tagContent}
          </UnderText>
        )}
        {!!second && (
          <Text fontWeight="normal" className={Style.normalText}>
            {second}
          </Text>
        )}
      </Text>
    );
  },
);

export default SpecialTextTip;
