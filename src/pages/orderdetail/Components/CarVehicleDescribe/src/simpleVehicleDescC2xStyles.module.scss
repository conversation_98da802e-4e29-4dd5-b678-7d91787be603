@import '../../../../scss/color.scss';

.verticalSplitLine {
  background-color: $aaaaaa;
  width: 1px;
  height: 24px;
  position: absolute;
}
.desc {
  color: $C_808080;
  font-size: 28px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-left: 14px;
  margin-right: 14px;
  margin-bottom: 8px;
}
.desc2 {
  color: $C_111;
}
.rightIcon {
  color: $C_888888;
  font-size: 28px;
  line-height: 28px;
  position: absolute;
  right: 10px;
}
.touchableWrapper {
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  padding-right: 28px;
}
.flexRowWrap {
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
}
