import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useState, CSSProperties } from 'react';
import { XView as View, xClassNames, xMergeStyles } from '@ctrip/xtaro';
import {
  ensureFunctionCall,
  getPixel,
  useMemoizedFn,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@pages/components/Touchable';
import Text from '@pages/components/Text';
import Icon from '@pages/components/Icon';
import c2xStyles from './simpleVehicleDescC2xStyles.module.scss';
import VehicleBootModal from '../../VehicleBootModal';
import Texts from './Texts';
import { UITestID } from '../../../../../Constants/Index';
import { FUELTYPE } from '../../../../../Constants/OrderDetail';
import { VehicleDescType } from '../../../enum';

interface Item {
  text: string;
  rightIcon?: { iconContent: string };
  type?: string;
  count?: number;
  taTestID?: string;
}
interface Props {
  data: Item[];
  lastIsBlock?: boolean;
  textStyle?: CSSProperties;
  wrapStyle?: CSSProperties;
  showBootBtn?: boolean;
  showFuelDesc?: boolean;
  pressHandle?: (data: any) => void;
  fuelDescOnPress?: () => void;
  isOrderDetail?: boolean;
  isDarkColor?: boolean;
  isInModal?: boolean;
}
const styles = StyleSheet.create({
  ml0: {
    marginLeft: getPixel(0),
  },
});

const SimpleVehicleDesc: React.FC<Props> = ({
  data,
  lastIsBlock,
  textStyle,
  wrapStyle,
  showBootBtn,
  showFuelDesc,
  pressHandle,
  fuelDescOnPress,
  isOrderDetail,
  isDarkColor,
  isInModal,
}) => {
  const [bootVisible, setBootVisible] = useState(false);
  const showIconFules: string[] = isOrderDetail
    ? [FUELTYPE.fuel, FUELTYPE.unKnownFuel, FUELTYPE.diesel, FUELTYPE.gasoline]
    : [VehicleDescType.fuel, VehicleDescType.unKnownFuel];
  const onPress = useMemoizedFn((luggageNum, type) => {
    if (type === VehicleDescType.luggage) {
      if (!bootVisible) {
        setBootVisible(true);
      }
      ensureFunctionCall(pressHandle(luggageNum));
    } else if (showIconFules.includes(type)) {
      fuelDescOnPress?.();
    }
  });
  const hideVehicleBootModal = useMemoizedFn(() => {
    setBootVisible(false);
  });
  if (!data) return null;

  return (
    <>
      <View
        testID={UITestID.car_testid_comp_vehicle_desc}
        style={xMergeStyles([layout.flexRowWrap, wrapStyle])}
      >
        {data?.map((item, index) => {
          const showFuelDescIcon =
            showIconFules.includes(item?.type) && showFuelDesc;
          const isTouchable =
            (item?.type === VehicleDescType.luggage && showBootBtn) ||
            showFuelDescIcon;
          const showRightIcon =
            item?.rightIcon &&
            showBootBtn &&
            item?.type === VehicleDescType.luggage;
          const Wrap = isTouchable && !isInModal ? Touchable : View;
          let showSplitLine = !!index;
          const isBlock =
            item?.type === VehicleDescType.luggage &&
            lastIsBlock &&
            index === data.length - 1;
          if (isBlock) {
            // 最后一个标签是行李箱则独占一行
            showSplitLine = false;
          }
          const isFuelUnknown =
            isOrderDetail && item?.type === FUELTYPE.unKnownFuel;
          if (isFuelUnknown) {
            showSplitLine = false;
          }
          return (
            <Wrap
              debounceTime={300}
              onClick={() => onPress(item?.count, item?.type)}
              className={
                isTouchable ? c2xStyles.touchableWrapper : c2xStyles.flexRowWrap
              }
              testID={item?.taTestID}
              key={item?.text}
            >
              {showSplitLine && (
                <View className={c2xStyles.verticalSplitLine} />
              )}
              <Text
                className={xClassNames(
                  c2xStyles.desc,
                  isDarkColor && c2xStyles.desc2,
                )}
                style={xMergeStyles([!showSplitLine && styles.ml0, textStyle])}
              >
                {item?.text}
              </Text>
              {!isInModal && showFuelDescIcon && (
                <Icon className={c2xStyles.rightIcon}>&#xe6d5;</Icon>
              )}
              {!isInModal && showRightIcon && (
                <Icon className={c2xStyles.rightIcon}>&#xe6d5;</Icon>
              )}
            </Wrap>
          );
        })}
      </View>
      {!isInModal && !!showBootBtn && (
        <VehicleBootModal
          visible={bootVisible}
          title={Texts.bootModalTitle}
          onCancel={hideVehicleBootModal}
        />
      )}
    </>
  );
};

SimpleVehicleDesc.defaultProps = {
  lastIsBlock: false,
  textStyle: null,
  wrapStyle: null,
  showBootBtn: true,
  showFuelDesc: false,
  pressHandle: () => {},
  fuelDescOnPress: () => {},
};

export default memo(SimpleVehicleDesc);
