@import '../../../scss/color.scss';

.container {
  padding: 40px 32px;
  background: $white;
}

.title {
  font-size: 36px;
  color: $C_111;
  margin-bottom: 8px;
  font-family: PingFangSC-Semibold;
}

.tip {
  margin-top: 16px;
  padding: 24px 20px;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  border-radius: 8px;
  background: $fff8f2;
}

.tipIcon {
  font-size: 30px;
  color: $C_ff5500;
}

.tipText {
  margin-left: 20px;
  line-height: 32px;
  font-size: 28px;
  color: $C_ff5500;
}

.name {
  margin-top: 24px;
  margin-bottom: 12px;
  font-size: 32px;
  color: $C_111;
  font-family: PingFangSC-Semibold;
}

.insurance {
  margin-top: 12px;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;

  font-size: 28px;
  color: $C_111;
}

.insuranceIcon {
  margin-right: 26px;
  font-size: 40px;
  color: $C_111;
}
.electronicPolicyWrap {
  top: -2px;
}
.insuranceText {
  font-size: 28px;
  line-height: 36px;
  font-family: PingFangSC-Regular;
  color: $C_111;
}

.reminderLine {
  margin-top: 24px;
}

.reminderText {
  line-height: 36px;
  font-size: 24px;
  font-family: PingFangSC-Regular;
  color: $C_888888;
}

.reminderTextTag {
  line-height: 36px;
  font-size: 24px;
  font-family: PingFangSC-Regular;
  color: $C_111;
}

.detailButton {
  margin-top: 32px;
  line-height: 36px;
  font-size: 28px;
  font-family: PingFangSC-Medium;
  color: $C_111;
}
