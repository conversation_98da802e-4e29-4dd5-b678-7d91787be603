import React, { useMemo } from 'react';
import { xRouter, XView } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import Icon from '@pages/components/Icon';
import Texts from '@pages/orderdetail/Texts';
import UnderText from '@pages/components/UnderText';
import orderDetailStore from '@pages/orderdetail/state/orderdetail';
import { InsuranceStatus } from '@pages/orderdetail/enum';
import Shark from '@pages/shark';
import { OrderStatusCtrip } from '../../../../Constants/OrderDetail';
import styles from './index.module.scss';

interface InsuranceProps {
  showDetail: (data) => void;
  showEnglish: (data) => void;
}

const Insurance = ({ showDetail, showEnglish }: InsuranceProps) => {
  const orderBaseInfo = orderDetailStore(state => state.orderBaseInfo);
  const { orderStatus } = orderBaseInfo ?? {};
  const platformInsurance = orderDetailStore(state => state.platformInsurance);

  const {
    packageInfos,
    briefInsuranceItems: insuranceItems,
    reminder,
  } = platformInsurance ?? {};
  const packageInfo = packageInfos?.[0] ?? {};

  const insuranceNode = useMemo(() => {
    return insuranceItems?.map(insurance => {
      const { insuranceDetail, name, insuranceStatus, itemUrl } = insurance;
      const v = insuranceDetail?.[0];
      const { minExcess, maxExcess } = v ?? {};
      // 是否是0起赔额
      const isZeroExcess = minExcess === 0 && maxExcess === 0;

      return (
        <XView className={styles.insurance}>
          <Icon className={styles.insuranceIcon}>&#xf21a4;</Icon>
          <Text className={styles.insuranceText}>{name} </Text>
          <Text className={styles.insuranceText}>
            {isZeroExcess ? Texts.zeroExcessDesc : ''}
          </Text>
          {insuranceStatus === InsuranceStatus.ComfirmIns && (
            <XView className={styles.electronicPolicyWrap}>
              <UnderText
                className={styles.insuranceText}
                fontWeight="medium"
                onClick={() =>
                  itemUrl &&
                  xRouter.navigateTo({ url: decodeURIComponent(itemUrl) })
                }
              >
                {Shark.getShark('key.cars.order.electronicPolicy')}
              </UnderText>
            </XView>
          )}
        </XView>
      );
    });
  }, [insuranceItems]);

  const reminderNode = useMemo(() => {
    return (
      <XView>
        {reminder?.map(({ text, subText, link }) => {
          if (!text) {
            return null;
          }
          if (text.indexOf('{tag}') === -1) {
            return (
              <Text className={styles.reminderLine}>
                <Text className={styles.reminderText} fontWeight="bold">
                  {text}
                </Text>
                {subText && (
                  <Text className={styles.reminderText}>{subText}</Text>
                )}
              </Text>
            );
          }
          const [a, b] = text.split('{tag}');
          return (
            <Text className={styles.reminderLine}>
              <Text className={styles.reminderText}>{a}</Text>
              {link?.text && (
                <UnderText
                  className={styles.reminderTextTag}
                  fontWeight="medium"
                  onClick={() => link?.urlDesc && showEnglish(link?.urlDesc)}
                >
                  {link.text}
                </UnderText>
              )}
              <Text className={styles.reminderText}>{b}</Text>
            </Text>
          );
        })}
      </XView>
    );
  }, [reminder, showEnglish]);

  return (
    <XView className={styles.container}>
      <Text className={styles.title} fontWeight="semibold">
        {Shark.getShark('key.cars.order.insuranceService')}
      </Text>
      {orderStatus?.orderStatusCtrip === OrderStatusCtrip.CANCELLED && (
        <XView className={styles.tip}>
          <Icon className={styles.tipIcon}>&#xe92f;</Icon>
          <Text className={styles.tipText}>
            {Shark.getShark('key.cars.order.insuranceCancel')}
          </Text>
        </XView>
      )}
      {packageInfo.packageName && (
        <Text className={styles.name} fontWeight="semibold">
          {packageInfo.packageName}
        </Text>
      )}
      {insuranceNode}
      {reminderNode}
      <UnderText
        className={styles.detailButton}
        fontWeight="medium"
        onClick={showDetail}
      >
        {Shark.getShark('key.cars.order.insuranceDetailProcess')}
      </UnderText>
    </XView>
  );
};

export default Insurance;
