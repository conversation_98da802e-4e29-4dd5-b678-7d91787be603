/* eslint-disable react/jsx-props-no-spreading */
import StyleSheet from '@c2x/apis/StyleSheet';
import Platform from '@c2x/apis/Platform';
import ScrollView from '@c2x/components/ScrollView';
import React, { ReactNode, CSSProperties } from 'react';
import { XView as View } from '@ctrip/xtaro';
import { color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { ITEM_DECORATION } from '@ctrip/rn_com_car/dist/src/Components/Basic/TextItem';
import Text from '@pages/components/Text';
import c2xStyles from './vehicleModalC2xStyles.module.scss';
import { HeaderCarouselImmerseItem } from '../../BouncesScrollView/index';
import { IVehicleName } from '../../CarVehicleName';
import SimilarVehicleTable, {
  SimilarVehicleTableProps,
} from './SimilarVehicleTable';
import WrapBbkTextItem from './WrapBbkTextItem';
import { TitleAndItem } from './Types';
import PossibleVehicleList, {
  PossibleVehicleListProps,
} from './PossibleVehicleList';
import Texts from './Texts';
import VehicleBaseInfo from '../../Vehicle';

const { getPixel, vw, fixIOSOffsetBottom } = BbkUtils;

interface VehicleModalProp {
  onCancel?: (item, index) => void;
  children?: ReactNode | ReactNode[];
  footChildren?: ReactNode | ReactNode[];
  /**
   * 段落标题及内容
   */
  section?: Section;
  /**
   * 同组车型表格
   */
  similarVehicleTableProps?: SimilarVehicleTableProps;
  /**
   * 车型名称
   */
  vehicleNameProps?: IVehicleName;
  /**
   * 可能取到的车型
   */
  possibleVehicleList?: PossibleVehicleListProps[];
  isModal?: boolean;
  style?: CSSProperties;
  modalHeaderStyle?: CSSProperties;
  vehicleNameStyle?: CSSProperties;
  descWrap?: CSSProperties;
  isNewEnergy?: boolean; // 是否是新能源弹窗
  onPressHelp?: (fuelType) => void;
  onPressShowLessModal?: () => void; // 打开可持续旅行出行倡议弹层
}
interface Section {
  introduce?: TitleAndItem;
  carProtection?: TitleAndItem;
  baseInfo?: TitleAndItem;
  possibleVehicles?: TitleAndItem;
}
const styles = StyleSheet.create({
  scrollWrap: {
    backgroundColor: color.white,
  },
  similarWrap: {
    marginTop: getPixel(16),
  },
  mt33: {
    marginTop: getPixel(33),
  },
  headerTitleStyle: {
    color: color.C_111111,
    ...font.title2MediumStyle,
  },
  itemStyle: {
    color: color.C_555555,
    ...font.F_26_12_regular,
  },
});

const VehicleModal: React.FC<VehicleModalProp> = ({
  section,
  similarVehicleTableProps,
  vehicleNameProps,
  possibleVehicleList,
  children,
}) => {
  const { introduce, carProtection, baseInfo, possibleVehicles } =
    section || {};
  const { isSimilar } = vehicleNameProps || {};

  const VideoStyles = {
    videoStyle: {
      width: vw(100) - getPixel(64),
      height: getPixel(386),
      overflow: 'hidden',
      borderRadius: getPixel(16),
    },
  };

  return (
    <>
      <VehicleBaseInfo isInModal={true} />
      <View className={c2xStyles.tipWrap}>
        <Text className={c2xStyles.tipText}>{Texts.image_tip}</Text>
      </View>
      <View
        style={{
          backgroundColor: color.white,
          paddingBottom: getPixel(fixIOSOffsetBottom(48)),
        }}
      >
        {/* 车型名称 */}
        <View className={c2xStyles.paddingWarp}>
          {/* 同组车型表格 */}
          {!!introduce && (
            <>
              <WrapBbkTextItem
                isFirst={true}
                itemDecoration={ITEM_DECORATION.NONE}
                contentStyle={{ paddingBottom: 0 }}
                headerTitleStyle={styles.headerTitleStyle}
                itemStyle={styles.itemStyle}
                {...introduce}
                style={styles.mt33}
              />

              <SimilarVehicleTable
                {...similarVehicleTableProps}
                wrapStyle={styles.similarWrap}
              />

              {introduce?.totalPhotos?.length > 0 && (
                <View className={c2xStyles.videoWrap}>
                  <HeaderCarouselImmerseItem
                    media={introduce?.totalPhotos?.[0]}
                    totalPhotos={introduce?.totalPhotos}
                    index={0}
                    isNotAutoPlay={false}
                    videoStyle={VideoStyles.videoStyle}
                    isShowProgress={false}
                  />
                </View>
              )}
            </>
          )}

          {!!carProtection && (
            <WrapBbkTextItem
              {...carProtection}
              itemStyle={{
                marginRight:
                  // @ts-ignore
                  Platform.OS === 'android' || Platform.OS === 'harmony'
                    ? BbkUtils.getPixel(11)
                    : 0,
              }}
              headerTitleStyle={styles.headerTitleStyle}
            />
          )}
        </View>

        {/* 车型基本信息 */}

        {/* 可能取到的车型 */}

        {!!baseInfo && !!possibleVehicleList?.length && (
          <>
            <View className={c2xStyles.paddingWarp}>
              <WrapBbkTextItem
                itemDecoration={ITEM_DECORATION.NONE}
                headerTitleStyle={styles.headerTitleStyle}
                {...possibleVehicles}
              />

              {possibleVehicles && isSimilar && (
                <Text className={c2xStyles.subTitle}>
                  {Texts.listInclude}
                  <Text className={c2xStyles.subTitleLight}>
                    {Texts.butNotLimitedTo}
                  </Text>
                  {Texts.followingCarModels}
                </Text>
              )}
            </View>
            <View>
              {possibleVehicleList.map(possibleVehicleListProps => (
                <PossibleVehicleList {...possibleVehicleListProps} />
              ))}
            </View>
            {children}
          </>
        )}
      </View>
    </>
  );
};

export default VehicleModal;
