import React, { memo } from 'react';
import orderDetailStore from '@pages/orderdetail/state/orderdetail';
import commonStore from '@pages/orderdetail/state/common';
import PickReturnTab from './PickReturnTab';

interface IOrderDetailPickReturnTab {
  orderId: any;
  isStoreInfoSurvey: boolean;
  onPolicyPress: (policySelectedId, labName) => void;
  onPressPhone: (flid?) => void;
}

const OrderDetailPickReturnTab = memo(
  ({
    orderId,
    isStoreInfoSurvey,
    onPolicyPress,
    onPressPhone,
  }: IOrderDetailPickReturnTab) => {
    const config = commonStore(state => state.qConfigResponse);
    const pickupStore = orderDetailStore(state => state.pickupStore);
    const returnStore = orderDetailStore(state => state.returnStore);
    const vendorInfo = orderDetailStore(state => state.vendorInfo);
    const orderBaseInfo = orderDetailStore(state => state.orderBaseInfo);
    const flight = orderDetailStore(state => state.flight);
    const finalQueryIsFinish = orderDetailStore(
      state => state.queryOrderAllDataSuccess,
    );
    const logBaseInfo = {
      orderId,
      pStoreId: pickupStore?.storeID,
      rStoreId: returnStore?.storeID,
      vendorId: vendorInfo.bizVendorCode,
    };
    const carAssistantSummary = orderDetailStore(
      state => state.carAssistantSummary,
    );
    const storeSurveyCommit = commonStore(state => state.storeSurveyCommit);
    const { setOrderModalsVisible, setFlightDelayRulesModalVisible } =
      orderDetailStore.getState();
    const { setStoreSurveyCommit } = commonStore.getState();
    return (
      <PickReturnTab
        orderId={orderId}
        isStoreInfoSurvey={isStoreInfoSurvey}
        onPolicyPress={onPolicyPress}
        onPressPhone={onPressPhone}
        config={config}
        pickupStore={pickupStore}
        returnStore={returnStore}
        orderBaseInfo={orderBaseInfo}
        flight={flight}
        finalQueryIsFinish={finalQueryIsFinish}
        storeSurveyCommit={storeSurveyCommit}
        logBaseInfo={logBaseInfo}
        vendorInfo={vendorInfo}
        carAssistantSummary={carAssistantSummary}
        setOrderModalsVisible={setOrderModalsVisible}
        setStoreSurveyCommit={setStoreSurveyCommit}
        setFlightDelayRulesModalVisible={setFlightDelayRulesModalVisible}
      />
    );
  },
);

export default OrderDetailPickReturnTab;
