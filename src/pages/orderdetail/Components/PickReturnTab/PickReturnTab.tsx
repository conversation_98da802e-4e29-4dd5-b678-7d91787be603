/* eslint-disable class-methods-use-this */
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import Clipboard from '@c2x/apis/Clipboard';
import React, { PureComponent } from 'react';
import {
  XView as View,
  XViewExposure,
  xRouter,
  xShowToast,
} from '@ctrip/xtaro';
import { BbkUtils, DateFormatter } from '@ctrip/rn_com_car/dist/src/Utils';
import Text from '@pages/components/Text';
import UnderText from '@pages/components/UnderText';
import Shark from '@pages/shark';
import commonStore from '@pages/orderdetail/state/common';
import { getGuidePageParam } from '@pages/orderdetail/state/orderdetail';
import c2xStyles from './pickReturnTabC2xStyles.module.scss';
import DiffGap from './components/DiffGap';
import SurveyEntry from '../SurveyEntry';
import { CarLog, Utils, CarStorage, GetABCache } from '../../../../Util/Index';
import { StorageKey, Platform } from '../../../../Constants/Index';
import UITestId from '../../../../Constants/UITestID';
import {
  GuideTabType,
  OrderStatusCtrip,
} from '../../../../Constants/OrderDetail';
import { OrderModalsVisible } from '../../Types';
import { CarAssistantType } from '../../enum';
import { validateIsInDoor } from '../../method';
import FlightInfo from './components/FlightInfo';
import PickReturnInfo from './components/PickReturnInfo';

const { ensureFunctionCall, getDayGap } = BbkUtils;
const [PICKUP, RETURN] = ['car-pick', 'car-return'];
interface Props {
  pickupStore: any;
  returnStore: any;
  orderId: number;
  orderBaseInfo?: any;
  flight?: any;
  logBaseInfo?: any;
  onPolicyPress?: (data?: any, data2?: any) => void;
  setFlightDelayRulesModalVisible?: (data) => void;
  onPressPhone: (data: any) => void;
  setOrderModalsVisible?: (data?: OrderModalsVisible) => void;
  finalQueryIsFinish?: boolean;
  isStoreInfoSurvey?: boolean;
  storeSurveyCommit?: boolean;
  config?: any;
  isPickUpCollapse?: boolean;
  carAssistantSummary?: any;
  setStoreSurveyCommit?: (data) => void;
}

enum SurveyStatus {
  /**
   * 初始化
   */
  Initial = -1,
  /**
   * 隐藏
   */
  Hide = 0,
  /**
   * 展示
   */
  Show = 1,
}
interface IStates {
  selectedId: string;
  pickupSurveyStatus?: SurveyStatus;
  dropoffSurveyStatus?: SurveyStatus;
  surveyStore?: any;
  isPickUpCollapse?: boolean; // 取车信息是否折叠， true为取车信息折叠， false为取车信息展开
}

export default class PickReturnTab extends PureComponent<Props, IStates> {
  constructor(props) {
    super(props);
    this.state = {
      selectedId: PICKUP,
      pickupSurveyStatus: SurveyStatus.Initial,
      dropoffSurveyStatus: SurveyStatus.Initial,
      surveyStore: null,
      isPickUpCollapse: props.isPickUpCollapse,
    };
  }

  componentDidMount() {
    if (GetABCache.isStoreInfoSurvey()) {
      // 初始化门店信息反馈缓存，判断是否提交过反馈
      this.initialStoreSurvey();
    }
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(nextProps) {
    const { storeSurveyCommit, setStoreSurveyCommit = Utils.noop } = this.props;

    if (
      GetABCache.isStoreInfoSurvey() &&
      !storeSurveyCommit &&
      nextProps.storeSurveyCommit
    ) {
      setStoreSurveyCommit(false);
      this.onSurveyCommit();
    }
  }

  // 服务费标准弹层
  handleBusinessTimePolicyModal = () => {
    const { setOrderModalsVisible = Utils.noop } = this.props;
    const { selectedId } = this.state;
    setOrderModalsVisible({
      businessTimePolicyModal: {
        visible: true,
        data: {
          type:
            selectedId === PICKUP ? GuideTabType.Pickup : GuideTabType.Dropoff,
        },
      },
    });
    CarLog.LogCode({ name: '点击_订单详情页_营业外收费政策' });
  };

  // 营业时间弹层
  handleBusinessTimeModal = () => {
    const { setOrderModalsVisible = Utils.noop } = this.props;
    const { selectedId } = this.state;
    setOrderModalsVisible({
      businessTimeModal: {
        visible: true,
        data: {
          type:
            selectedId === PICKUP ? GuideTabType.Pickup : GuideTabType.Dropoff,
        },
      },
    });
    CarLog.LogCode({ name: '点击_订单详情页_营业时间详情' });
  };

  initialStoreSurvey = async () => {
    try {
      const { orderId } = this.props;
      const res =
        (await CarStorage.loadAsync(StorageKey.CAR_STORE_SURVEY)) || '{}';
      const surveyStore = JSON.parse(res);
      const storeKey = `${orderId}_store`;
      const { pickup = SurveyStatus.Show, dropoff = SurveyStatus.Show } =
        surveyStore[storeKey] || {};
      this.setState({
        pickupSurveyStatus: pickup,
        dropoffSurveyStatus: dropoff,
        surveyStore,
      });
      // eslint-disable-next-line no-empty
    } catch (e) {}
  };

  setStoreSurvey = (pickup, dropoff) => {
    try {
      const { orderId } = this.props;
      const { surveyStore } = this.state;
      const storeKey = `${orderId}_store`;
      const result = {
        ...surveyStore,
        [storeKey]: {
          pickup,
          dropoff,
        },
      };
      CarStorage.save(StorageKey.CAR_STORE_SURVEY, result, '365d');
      // eslint-disable-next-line no-empty
    } catch (e) {}
  };

  setClipboardContent = async text => {
    Clipboard.setString(text);
    try {
      xShowToast({ title: '已复制', duration: 1000 });
    } catch (e) {
      // console.log(e)
    }
  };

  onPhonePress = id => {
    const { orderId, orderBaseInfo, finalQueryIsFinish, onPressPhone } =
      this.props;
    if (!finalQueryIsFinish) return;
    CarLog.LogCode({
      name: '点击_订单详情页_供应商电话入口',

      info: {
        orderId,
        orderStatus: orderBaseInfo?.orderStatus,
      },
    });
    ensureFunctionCall(onPressPhone(id));
  };

  gotoGuidePage = guideTabId => {
    const { finalQueryIsFinish } = this.props;
    if (!finalQueryIsFinish) return;
    const param = getGuidePageParam(guideTabId);
    CarLog.LogCode({
      name: '点击_订单详情页_取车指引',

      data: param,
    });
    const {
      phoneSurveyShowCount,
      phoneSurveyShowPage,
      qConfigResponse,
      phoneSurveyNumber,
    } = commonStore.getState();
    const crossParam = {
      urlParams: {
        pageParam: {
          ...param,
          isFromOrderDetail: true,
        },
      },
      storeParams: [
        {
          type: 'COMMON_CROSS_PARAMS',
          data: {
            phoneSurveyShowCount,
            phoneSurveyShowPage,
            qConfigResponse,
            phoneSurveyNumber,
          },
        },
      ],
    };
    // 保存Store数据，用于传参
    CarStorage.save(
      StorageKey.CAR_CROSS_PARAMS,
      JSON.stringify(crossParam),
      '1m',
    );
    const url = Platform.CAR_CROSS_URL.Guide.OSD;
    xRouter.navigateTo({ url });
  };

  goPickUpMap = () => {
    const { orderId, orderBaseInfo } = this.props;
    CarLog.LogCode({
      name: '点击_订单详情页_地图',

      info: {
        orderId,
        orderStatus: orderBaseInfo?.orderStatus,
      },
    });
    this.gotoGuidePage(GuideTabType.Pickup);
  };

  goDropOffMap = () => {
    const { orderId, orderBaseInfo } = this.props;
    CarLog.LogCode({
      name: '点击_订单详情页_地图',

      info: {
        orderId,
        orderStatus: orderBaseInfo?.orderStatus,
      },
    });
    this.gotoGuidePage(GuideTabType.Dropoff);
  };

  callPickUpPhoneNumber = () => {
    this.onPhonePress(0);
  };

  callDropOffPhoneNumber = () => {
    this.onPhonePress(1);
  };

  onPolicyPress = () => {
    const { onPolicyPress = Utils.noop } = this.props;
    onPolicyPress('all', '全部');
  };

  onSurveyCommit = () => {
    const { selectedId, pickupSurveyStatus, dropoffSurveyStatus } = this.state;
    xShowToast({ title: '感谢您的反馈，我们将努力做得更好！', duration: 1000 });
    const curPickupSurveyStatus =
      selectedId === PICKUP ? SurveyStatus.Hide : pickupSurveyStatus;
    const curDropoffSurveyStatus =
      selectedId === PICKUP ? dropoffSurveyStatus : SurveyStatus.Hide;
    this.setState({
      pickupSurveyStatus: curPickupSurveyStatus,
      dropoffSurveyStatus: curDropoffSurveyStatus,
    });
    this.setStoreSurvey(curPickupSurveyStatus, curDropoffSurveyStatus);
  };

  onPressYes = () => {
    const { orderId, orderBaseInfo, pickupStore, returnStore } = this.props;
    const { selectedId } = this.state;
    const currentData = selectedId === PICKUP ? pickupStore : returnStore;
    this.onSurveyCommit();
    CarLog.LogCode({
      name:
        selectedId === PICKUP
          ? '点击_门店信息反馈模块_取车tab_是'
          : '点击_门店信息反馈模块_还车tab_是',
      info: {
        orderId,
        orderStatus: orderBaseInfo?.orderStatus,
        storeId: currentData?.storeID,
        pStoreId: pickupStore.storeID,
        rStoreId: returnStore.storeID,
      },
    });
  };

  onPressNo = () => {
    const { orderId, orderBaseInfo, pickupStore, returnStore } = this.props;
    const { selectedId } = this.state;
    const currentData = selectedId === PICKUP ? pickupStore : returnStore;
    const { orderStatus } = orderBaseInfo;
    const url = `/rn_xtaro_car_osd/main.js?CRNType=1&CRNModuleName=rn_xtaro_car_osd&initialPage=operateSurvey&orderId=${orderId}&orderStatus=${orderStatus}&storeId=${currentData?.storeID}`;
    CarLog.LogCode({
      name:
        selectedId === PICKUP
          ? '点击_门店信息反馈模块_取车tab_否'
          : '点击_门店信息反馈模块_还车tab_否',
      info: {
        orderId,
        orderStatus: orderBaseInfo?.orderStatus,
        storeId: currentData?.storeID,
        pStoreId: pickupStore.storeID,
        rStoreId: returnStore.storeID,
      },
    });
    xRouter.navigateTo({ url });
  };

  getIsShowSurvey = () => {
    let isShowSurvey = false;
    const { orderBaseInfo, pickupStore, isStoreInfoSurvey } = this.props;
    if (!isStoreInfoSurvey) {
      return false;
    }
    const { localDateTime } = pickupStore || {};
    const { orderStatusCtrip } = orderBaseInfo || {};
    const { selectedId, pickupSurveyStatus, dropoffSurveyStatus } = this.state;
    const pickupTime = dayjs(localDateTime);
    const now = dayjs();
    if (
      orderStatusCtrip === OrderStatusCtrip.CONFIRMED &&
      pickupTime.isValid() &&
      now.add(5, 'day').diff(pickupTime) > 0
    ) {
      if (selectedId === PICKUP) {
        isShowSurvey = pickupSurveyStatus === SurveyStatus.Show;
      } else {
        isShowSurvey = dropoffSurveyStatus === SurveyStatus.Show;
      }

      return isShowSurvey;
    }
    return false;
  };

  showFlightDelayRulesModal = () => {
    const { setFlightDelayRulesModalVisible = Utils.noop } = this.props;
    setFlightDelayRulesModalVisible(true);
  };

  formatDate = localDateTime => {
    const { formatter } = DateFormatter;
    const dateFormatter = formatter(localDateTime);
    let dateYmdString = dateFormatter.ymdShortString();
    let dateYearString = dateFormatter.hmString();

    dateYmdString = dayjs(localDateTime).format('M月D日 HH:mm');
    dateYearString = dateFormatter.yString();

    return {
      dateYearString,
      dateYmdString,
    };
  };

  getContractTranslation = () => {
    const { carAssistantSummary = [] } = this.props;
    const contractTranslation = carAssistantSummary?.find(
      item => item.type === CarAssistantType.CONTRACT_TRANSLATION,
    );
    return contractTranslation;
  };

  getdiffVal = () => {
    const {
      pickupStore: { localDateTime: ptime },
      returnStore: { localDateTime: rtime },
    } = this.props;
    if (!ptime || !rtime) return '';
    const iptime = ptime?.replace(/-/g, '/');
    const irtime = rtime?.replace(/-/g, '/');
    const diff = getDayGap(iptime, irtime);

    const diffVal = Shark.getShark('key.cars.orderDetail.pickReturn.diffDay', [
      `${diff}`,
    ]);
    return diffVal;
  };

  getTimeLine = () => {
    const {
      pickupStore: { localDateTime: pickupDate },
      returnStore: { localDateTime: dropoffDate },
    } = this.props;
    if (!pickupDate || !dropoffDate) {
      return {};
    }
    const pDateStrObj = this.formatDate(pickupDate);
    const rDateStrObj = this.formatDate(dropoffDate);
    const curYear = dayjs().year();
    const isShowYear =
      Number(pDateStrObj.dateYearString) - curYear ||
      Number(rDateStrObj.dateYearString) - curYear;
    const diffGap = this.getdiffVal();
    return {
      pickUpTime: `${
        isShowYear
          ? `${pDateStrObj.dateYearString}${Shark.getShark(
              'key.cars.orderDetail.pickReturn.year',
            )} `
          : ''
      }${pDateStrObj.dateYmdString} (${Shark.getShark(
        'key.cars.orderDetail.pickReturn.localTime',
      )})`,
      dropOffTime: `${
        isShowYear
          ? `${rDateStrObj.dateYearString}${Shark.getShark(
              'key.cars.orderDetail.pickReturn.year',
            )} `
          : ''
      }${rDateStrObj.dateYmdString} (${Shark.getShark(
        'key.cars.orderDetail.pickReturn.localTime',
      )})`,
      isShowYear: !!isShowYear,
      diffGap,
    };
  };

  getGuideStepImages = guideStep => {
    const stepImages: Array<any> = [];
    guideStep?.forEach(item => {
      if (item?.image) {
        stepImages.push({
          url: item.image,
          imageDescription: item.content?.replace(/\s+/g, ''),
        });
      }
    });
    return stepImages;
  };

  render() {
    const {
      pickupStore,
      returnStore,
      orderId,
      orderBaseInfo,
      config,
      flight,
      logBaseInfo,
    } = this.props;
    if (!pickupStore) return null;
    const { selectedId, isPickUpCollapse } = this.state;
    const { orderStatus } = orderBaseInfo || {};
    const isShowSurvey = this.getIsShowSurvey();
    const currentData = selectedId === PICKUP ? pickupStore : returnStore;
    const { flightNumber, flightDelayRule } = flight || {};
    const { diffGap, pickUpTime, dropOffTime } = this.getTimeLine();
    const isPickUpInDoor = validateIsInDoor(Number(pickupStore?.serviceType));
    const isDropOffInDoor = validateIsInDoor(Number(returnStore?.serviceType));
    const pickUpAddress = isPickUpInDoor
      ? pickupStore?.userSearchLocation
      : pickupStore?.storeAddress;
    const dropOffAddress = isDropOffInDoor
      ? returnStore?.userSearchLocation
      : returnStore?.storeAddress;
    const pickUpGuideStepImages = this.getGuideStepImages(
      pickupStore?.guideStep,
    );
    const dropOffGuideStepImages = this.getGuideStepImages(
      returnStore?.guideStep,
    );
    const contractTranslation = this.getContractTranslation();
    const pickupPhoneList = Utils.getPhoneList(pickupStore?.storeTel);
    const dropOffPhoneList = Utils.getPhoneList(returnStore?.storeTel);
    return (
      <XViewExposure
        testID={CarLog.LogExposure({
          name: '曝光_订单详情页_门店及取还车信息模块',
        })}
        className={c2xStyles.Wrap}
      >
        <View
          testID={UITestId.car_osd_pick_return_content}
          className={c2xStyles.Container}
        >
          {/** 航班信息 */}
          {!!flightNumber && (
            <FlightInfo
              flightNumber={flightNumber}
              logBaseInfo={logBaseInfo}
              flightDelayRuleTitle={flightDelayRule?.title}
              showFlightDelayRulesModal={this.showFlightDelayRulesModal}
            />
          )}
          {/** 取车信息 */}
          <PickReturnInfo
            title={Shark.getShark('key.cars.orderDetail.pickReturn.pickTitle')}
            pickUpTime={pickUpTime}
            storeName={isPickUpInDoor ? pickUpAddress : pickupStore?.storeName}
            isPick={true}
            storeTel={pickupPhoneList?.[0]}
            address={pickUpAddress}
            storeWay={pickupStore?.storeWay}
            isShowDashed={true}
            isInDoor={isPickUpInDoor}
            guideStepImages={pickUpGuideStepImages}
            contractTranslation={contractTranslation}
            storeBusinessTime={pickupStore?.storeBusinessTime}
            businessTimePolicy={pickupStore?.businessTimePolicy}
            copyText={this.setClipboardContent}
            callPhoneNumber={this.callPickUpPhoneNumber}
            handleBusinessTimePolicyModal={this.handleBusinessTimePolicyModal}
            handleBusinessTimeModal={this.handleBusinessTimeModal}
            goMap={this.goPickUpMap}
            onPolicyPress={this.onPolicyPress}
          />
          {/** 时间间隔 */}
          <DiffGap title={diffGap} />
          {/** 还车信息 */}
          <PickReturnInfo
            title={Shark.getShark('key.cars.orderDetail.pickReturn.dropTitle')}
            pickUpTime={dropOffTime}
            storeName={
              isDropOffInDoor ? dropOffAddress : returnStore?.storeName
            }
            storeTel={dropOffPhoneList?.[0]}
            address={dropOffAddress}
            storeWay={returnStore?.storeWay}
            isInDoor={isDropOffInDoor}
            guideStepImages={dropOffGuideStepImages}
            contractTranslation={contractTranslation}
            storeBusinessTime={returnStore?.storeBusinessTime}
            businessTimePolicy={returnStore?.businessTimePolicy}
            copyText={this.setClipboardContent}
            callPhoneNumber={this.callDropOffPhoneNumber}
            handleBusinessTimePolicyModal={this.handleBusinessTimePolicyModal}
            handleBusinessTimeModal={this.handleBusinessTimeModal}
            goMap={this.goDropOffMap}
            onPolicyPress={this.onPolicyPress}
          />
          <View>
            {/** 取还车去除了当前选中的取还车Tab，导致反馈模块展示屏蔽 */}
            {/* {config?.isSurvey && isShowSurvey && (
              <SurveyEntry
                orderId={orderId}
                orderStatus={orderStatus}
                onPressYes={this.onPressYes}
                onPressNo={this.onPressNo}
                storeId={currentData?.storeID}
              />
            )} */}
            {/** 如果没有航班号，则展示航班延误政策的温馨提示 */}
            {!flightNumber && !!flightDelayRule && (
              <View className={c2xStyles.flightTipsWrap}>
                <Text fontWeight="medium" className={c2xStyles.tipTitleText}>
                  {Shark.getShark(
                    'key.cars.orderDetail.pickReturn.flightTipsTitle',
                  )}
                </Text>
                <Text fontWeight="normal" className={c2xStyles.tipText}>
                  {Shark.getShark(
                    'key.cars.orderDetail.pickReturn.flightTipsDesc',
                  )}
                </Text>
                <UnderText
                  onClick={this.showFlightDelayRulesModal}
                  fontWeight="normal"
                  className={c2xStyles.tipBtnText}
                  underColor="#888888"
                >
                  {flightDelayRule?.title}
                </UnderText>
              </View>
            )}
          </View>
        </View>
      </XViewExposure>
    );
  }
}
