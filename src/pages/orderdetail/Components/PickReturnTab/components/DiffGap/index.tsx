/* eslint-disable react-native/no-color-literals */
import React, { memo, useState } from 'react';
import { XView as View } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import DashedLine from '@pages/components/DashedLine';
import { useMemoizedFn } from '@pages/hooks';
import getPixel from '@pages/utils/getPixel';
import Style from './index.module.scss';

interface IDiffGap {
  title?: string;
}

const DiffGap: React.FC<IDiffGap> = memo(({ title }: IDiffGap) => {
  const [repeat, setRepeat] = useState(0);
  const infoLayout = useMemoizedFn(e => {
    const { height } = e.nativeEvent.layout;
    const calRepeat = Math.ceil(height / getPixel(8)) - 1;
    if (calRepeat !== repeat) {
      setRepeat(calRepeat);
    }
  });
  return (
    <View className={Style.wrap}>
      <View className={Style.titleWrap}>
        <View className={Style.circleIcon} />
        <View>
          <Text fontWeight="normal" className={Style.titleText}>
            {title}
          </Text>
        </View>
      </View>
      <View className={Style.row}>
        <View className={Style.dashedLineWrap}>
          <DashedLine
            direction="vertical"
            repeat={repeat}
            dashStyle={{
              backgroundColor: '#d5d5d5',
              width: getPixel(3),
            }}
          />
        </View>
        <View onLayout={infoLayout} className={Style.content} />
      </View>
    </View>
  );
});

export default DiffGap;
