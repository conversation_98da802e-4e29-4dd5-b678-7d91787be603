@import '../../../../../scss/color.scss';

.wrap {
  z-index: 100;
  background-color: $white;
}
.titleWrap {
  flex-direction: row;
  height: 40px;
  align-items: center;
}
.circleIcon {
  width: 20px;
  height: 20px;
  border-radius: 10px;
  border-width: 3px;
  border-color: $C_111;
  border-style: solid;
  margin-left: 10px;
  margin-right: 32px;
}
.titleText {
  font-size: 32px;
  line-height: 42px;
  font-family: PingFangSC-Regular;
  color: $C_111;
}
.row {
  flex-direction: row;
}
.dashedLineWrap {
  padding-bottom: 8px;
  width: 40px;
  align-items: center;
  margin-right: 24px;
}
.content {
  height: 56px;
}
