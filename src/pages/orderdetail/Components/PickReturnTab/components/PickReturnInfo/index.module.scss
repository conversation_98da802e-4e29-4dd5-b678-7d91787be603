@import '../../../../../scss/color.scss';

.titleWrap {
  flex-direction: row;
  height: 40px;
  align-items: center;
}
.titleContent {
  flex-direction: row;
  flex: 1;
  align-items: center;
  justify-content: space-between;
}
.collapseIcon {
  font-size: 36px;
  color: $C_111;
}
.titleLeftWrap {
  width: 40px;
  align-items: center;
  margin-right: 24px;
}
.titleIcon {
  font-size: 40px;
  color: $C_111;
}
.titleText {
  font-size: 32px;
  line-height: 42px;
  font-family: PingFangSC-Semibold;
  color: $C_111;
}
.row {
  flex-direction: row;
}
.dashedLineWrap {
  width: 40px;
  align-items: center;
  justify-items: center;
  margin-right: 24px;
}
.lineWrap {
  top: 12px;
  position: absolute;
  align-items: center;
  flex-direction: row;
}
.pickUpRightWrap {
  padding-top: 16px;
  padding-bottom: 56px;
  flex: 1;
}
.dropUpRightWrap {
  padding-top: 16px;
  padding-bottom: 32px;
  flex: 1;
}
.normalText {
  font-size: 28px;
  line-height: 36px;
  font-family: PingFangTC-Regular;
  color: $C_111;
}
.storeNameWrap {
  margin-top: 8px;
}
.businessHourWrap {
  margin-top: 16px;
  align-items: start;
  flex-direction: row;
}
.smallIconWrap {
  height: 36px;
  justify-content: center;
}
.smallIcon {
  font-size: 30px;
  margin-right: 20px;
  text-align: center;
  color: $C_111;
}
.copyIconWrap {
  padding-left: 8px;
  padding-right: 8px;
}
.copyIcon {
  top: 6px;
  font-size: 28px;
  color: $C_111;
  line-height: 36px;
}
.storeTelWrap {
  flex-direction: row;
  align-items: start;
  margin-top: 16px;
}
.storeTelTextWrap {
  flex-direction: row;
  flex: 1;
}
.phoneWrap {
  flex-direction: row;
}
.phoneText {
  font-size: 28px;
  line-height: 36px;
  font-family: PingFangTC-Medium;
  color: $C_111;
}
.pickUpAddressWrap {
  flex-direction: row;
  align-items: start;
  margin-top: 16px;
}
.flex1 {
  flex: 1;
}
.storeWayWrap {
  flex-direction: row;
  align-items: start;
  margin-top: 16px;
}
.pickUpGuideWrap {
  margin-top: 24px;
}
.stepImage{
  height: 208px;
  width: 364px;
  border-radius: 6px;
}
.ml8 {
  margin-left: 8px;
}
.stepCorner {
  position: absolute;
  background-color: $C_111;
  padding-left: 16px;
  padding-right: 16px;
  border-top-left-radius: 6px;
  border-bottom-right-radius: 6px;
  justify-content: center;
  left: 0px;
  top: 0px;
  z-index: 10;
  height: 48px;
}
.howToArriveText {
  font-size: 24px;
  line-height: 36px;
  font-family: PingFangSC-Medium;
  color: $white;
}
.spaceView {
  width: 104px;
}
.shadowView {
  position: absolute;
  right: -32px;
  width: 241px;
  height: 208px;
  z-index: 9;
}
.guideWrap {
  position: absolute;
  width: 104px;
  height: 208px;
  right: 0px;
  z-index: 10;
  align-items: center;
  justify-content: center;
  background-color: $C_F0F2F5;
  border-radius: 8px;
}
.singleGuideWrap {
  right: 144px;
}
.guideIcon {
  font-size: 36px;
  margin-bottom: 4px;
  color: $C_111;
}
.guideText {
  font-size: 28px;
  line-height: 36px;
  font-family: PingFangSC-Medium;
  color: $C_111;
}
.mapBg {
  width: 622px;
  height: 88px;
  border-radius: 8px;
  margin-top: 24px;
}
.mapContentWrap {
  padding-left: 24px;
  padding-right: 24px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex: 1;
}
.mapText {
  font-size: 28px;
  line-height: 36px;
  font-family: PingFangSC-Regular;
  color: $C_111;
}
.mapIcon {
  font-size: 38px;
  color: $C_111;
}
.storePolicyWrap {
  flex-direction: row;
  align-items: center;
  margin-top: 24px;
}
.storePolicyText {
  font-size: 28px;
  line-height: 36px;
  font-family: PingFangSC-Medium;
  color: $C_111;
}
.spiltLine {
  margin-left: 24px;
  margin-right: 24px;
  width: 2px;
  height: 24px;
  background-color: $grayc5;
}
