/* eslint-disable react-native/no-color-literals */
import React, { memo, useState } from 'react';
import {
  XView as View,
  XScrollView,
  XImage,
  XImageBackground,
  xClassNames as classN<PERSON>s,
  XLinearGradient,
  xPhoto<PERSON><PERSON>er,
  xRouter,
} from '@ctrip/xtaro';
import UnderText from '@pages/components/UnderText';
import Text from '@pages/components/Text';
import Icon from '@pages/components/Icon';
import DashedLine from '@pages/components/DashedLine';
import Touchable from '@pages/components/Touchable';
import { useMemoizedFn } from '@pages/hooks';
import Shark from '@pages/shark';
import getPixel from '@pages/utils/getPixel';
import { noop } from '@pages/utils/util';
import { Utils, CarLog } from '../../../../../../Util/Index';
import Style from './index.module.scss';
import BusinessTimeInfo from '../BusinessTimeInfo';
import TelSetClipboard from '../TelSetClipboard';

interface IPickReturnInfo {
  title?: string;
  pickUpTime?: string;
  storeName?: string;
  storeTel?: string;
  address?: string;
  storeWay?: string;
  guideStepImages?: Array<any>;
  contractTranslation?: any;
  storeBusinessTime?: Array<any>;
  businessTimePolicy?: any;
  isShowDashed?: boolean;
  isPick?: boolean;
  isInDoor?: boolean;
  copyText?: (text) => void;
  callPhoneNumber?: () => void;
  handleBusinessTimePolicyModal?: () => void;
  handleBusinessTimeModal?: () => void;
  goMap?: () => void;
  onPolicyPress?: () => void;
}

const PickReturnInfo: React.FC<IPickReturnInfo> = memo(
  ({
    title,
    pickUpTime,
    storeName,
    storeTel,
    address,
    storeWay,
    isShowDashed,
    guideStepImages = [],
    contractTranslation,
    storeBusinessTime = [],
    businessTimePolicy,
    isPick,
    isInDoor,
    copyText = Utils.noop,
    callPhoneNumber = Utils.noop,
    handleBusinessTimePolicyModal = Utils.noop,
    handleBusinessTimeModal = Utils.noop,
    goMap = Utils.noop,
    onPolicyPress = Utils.noop,
  }: IPickReturnInfo) => {
    const [isCollapse, setIsCollapse] = useState(!isPick);
    const onPressCollapse = useMemoizedFn(() => {
      setIsCollapse(!isCollapse);
    });
    const copyStoreName = useMemoizedFn(() => {
      copyText(storeName);
    });
    const copyAddress = useMemoizedFn(() => {
      copyText(address);
    });
    const [repeat, setRepeat] = useState({
      rows: 0,
      height: 0,
    });
    const [expendRepeat, setExpendRepeat] = useState({
      rows: 0,
      height: 0,
    });
    const policyData = businessTimePolicy?.table?.find(
      item => item?.type === 400,
    );
    const infoLayout = useMemoizedFn(e => {
      const { height } = e.nativeEvent.layout;
      const calRepeat = Math.ceil(height / getPixel(8));
      if (isCollapse) {
        if (calRepeat !== expendRepeat.rows) {
          setExpendRepeat({
            rows: calRepeat,
            height,
          });
        }
      } else if (calRepeat !== repeat.rows) {
        setRepeat({
          rows: calRepeat,
          height,
        });
      }
    });
    return (
      <View>
        {/** Icon+标题 */}
        {!!title && (
          <View className={Style.titleWrap}>
            <View className={Style.titleLeftWrap}>
              <Icon className={Style.titleIcon}>
                {isPick ? '&#xf21a8;' : '&#xf21a6;'}
              </Icon>
            </View>
            <Touchable
              debounceTime={300}
              onClick={onPressCollapse}
              className={Style.titleContent}
            >
              <Text fontWeight="bold" className={Style.titleText}>
                {title}
              </Text>
              <Icon className={Style.collapseIcon}>
                {isCollapse ? '&#xe0b2;' : '&#xe0b3;'}
              </Icon>
            </Touchable>
          </View>
        )}
        <View className={Style.row}>
          {/** 虚线 */}
          <View className={Style.dashedLineWrap}>
            {isShowDashed && (
              <View
                style={{
                  height: isCollapse ? expendRepeat.height : repeat.height,
                }}
                className={Style.lineWrap}
              >
                <DashedLine
                  direction="vertical"
                  repeat={isCollapse ? expendRepeat.rows : repeat.rows}
                  dashStyle={{
                    backgroundColor: '#d5d5d5',
                    width: getPixel(3),
                  }}
                />
              </View>
            )}
          </View>
          <View
            className={isPick ? Style.pickUpRightWrap : Style.dropUpRightWrap}
            onLayout={isPick ? infoLayout : noop}
          >
            {/** 取车时间 */}
            {!!pickUpTime && (
              <View>
                <Text fontWeight="normal" className={Style.normalText}>
                  {pickUpTime}
                </Text>
              </View>
            )}
            {!isCollapse && (
              <>
                {/** 取车门店|送车上门地址 */}
                {!!storeName && (
                  <View className={Style.storeNameWrap}>
                    <Text
                      onLongPress={copyStoreName}
                      fontWeight="normal"
                      className={Style.normalText}
                    >
                      {storeName}
                    </Text>
                  </View>
                )}
                {/** 取车营业时间 */}
                {!isInDoor && storeBusinessTime?.length > 0 && (
                  <View className={Style.businessHourWrap}>
                    <View className={Style.smallIconWrap}>
                      <Icon className={Style.smallIcon}>&#xf01f9;</Icon>
                    </View>
                    {/* 营业正常+收费时间展示 */}
                    <BusinessTimeInfo
                      storeBusinessTime={storeBusinessTime}
                      isHasTimePolicy={!!policyData}
                      isChargeTimePolicy={
                        !!businessTimePolicy?.summaryContent?.[0]
                      }
                      handleBusinessTimePolicyModal={
                        handleBusinessTimePolicyModal
                      }
                      handleBusinessTimeModal={handleBusinessTimeModal}
                    />
                  </View>
                )}
                {/** 取车门店电话 */}
                {(!!storeTel || isInDoor) && (
                  <View className={Style.storeTelWrap}>
                    <View className={Style.smallIconWrap}>
                      <Icon className={Style.smallIcon}>&#xf219e;</Icon>
                    </View>
                    <View className={Style.storeTelTextWrap}>
                      {isInDoor ? (
                        <Text fontWeight="normal" className={Style.normalText}>
                          {Shark.getShark('key.cars.order.contractSender')}
                        </Text>
                      ) : (
                        <Text fontWeight="normal" className={Style.normalText}>
                          <TelSetClipboard telephone={storeTel} />
                          &nbsp;
                          <UnderText
                            onClick={callPhoneNumber}
                            className={Style.phoneText}
                            fontWeight="medium"
                          >
                            {Shark.getShark(
                              'key.cars.orderDetail.pickReturn.callPhone',
                            )}
                          </UnderText>
                        </Text>
                      )}
                    </View>
                  </View>
                )}
                {/** 取车地址 */}
                {!!address && !isInDoor && (
                  <View className={Style.pickUpAddressWrap}>
                    <View className={Style.smallIconWrap}>
                      <Icon className={Style.smallIcon}>&#xf218e;</Icon>
                    </View>
                    <View className={Style.flex1}>
                      <Text
                        onLongPress={copyAddress}
                        fontWeight="normal"
                        className={Style.normalText}
                      >
                        {address}
                        <Touchable
                          onClick={copyAddress}
                          className={Style.copyIconWrap}
                        >
                          <Icon className={Style.copyIcon}>&#xf439;</Icon>
                        </Touchable>
                      </Text>
                    </View>
                  </View>
                )}
                {/** 到达方式(海外) */}
                {(!!storeWay || isInDoor) && (
                  <View className={Style.storeWayWrap}>
                    <View className={Style.smallIconWrap}>
                      <Icon className={Style.smallIcon}>&#xf219f;</Icon>
                    </View>
                    <View className={Style.flex1}>
                      <Text fontWeight="normal" className={Style.normalText}>
                        {isInDoor
                          ? Shark.getShark('key.cars.order.senderWay')
                          : storeWay}
                      </Text>
                    </View>
                  </View>
                )}
                {/** 取还指引信息 */}
                {guideStepImages?.length > 0 ? (
                  <View className={Style.pickUpGuideWrap}>
                    <XScrollView
                      bounces={false}
                      scrollX={true}
                      showScrollbar={false}
                    >
                      {guideStepImages?.map((item: any, index) => {
                        return (
                          <Touchable
                            onClick={() => {
                              xPhotoBrowser.show({
                                items: guideStepImages,
                                showPhotoIndex: index,
                              });
                            }}
                            className={Style.stepImage}
                            key={`step_image_${String(index)}`}
                          >
                            {index === 0 && (
                              <View className={Style.stepCorner}>
                                <Text
                                  fontWeight="normal"
                                  className={Style.howToArriveText}
                                >
                                  {Shark.getShark(
                                    'key.cars.orderDetail.pickReturn.howToArrive',
                                  )}
                                </Text>
                              </View>
                            )}
                            {!!item?.url && (
                              <XImage
                                src={item?.url}
                                mode="aspectFill"
                                className={classNames(
                                  Style.stepImage,
                                  index > 0 && Style.ml8,
                                )}
                              />
                            )}
                          </Touchable>
                        );
                      })}
                      {guideStepImages?.length > 1 && (
                        <View className={Style.spaceView} />
                      )}
                    </XScrollView>
                    {/** 阴影 */}
                    {guideStepImages?.length > 1 && (
                      <XLinearGradient
                        start={{ x: 1, y: 0.5 }}
                        end={{ x: 0, y: 0.5 }}
                        colors={[
                          '#ffffff',
                          'rgba(255,255,255,0.29)',
                          'rgba(54,54,54,0)',
                          'rgba(37,37,37,0)',
                        ]}
                        locations={[0.59, 0.7242, 0.8946, 0.8946]}
                        useAngle={true}
                        angle={-90}
                        className={Style.shadowView}
                      />
                    )}
                    <Touchable
                      testID={CarLog.LogExposure({
                        name: '曝光_订单详情页_地图入口',
                      })}
                      onClick={goMap}
                      className={classNames(
                        Style.guideWrap,
                        guideStepImages?.length === 1 && Style.singleGuideWrap,
                      )}
                    >
                      <Icon className={Style.guideIcon}>&#xf218e;</Icon>
                      <UnderText
                        fontWeight="medium"
                        className={Style.guideText}
                      >
                        {Shark.getShark(
                          'key.cars.orderDetail.pickReturn.guide',
                        )}
                      </UnderText>
                    </Touchable>
                  </View>
                ) : (
                  <XImageBackground
                    mode="aspectFill"
                    className={Style.mapBg}
                    src="https://dimg04.c-ctrip.com/images/1tg3g12000l5tj12l9500.png"
                  >
                    <Touchable className={Style.mapContentWrap} onClick={goMap}>
                      <Text fontWeight="normal" className={Style.mapText}>
                        {Shark.getShark(
                          isPick
                            ? 'key.cars.orderDetail.pickReturn.howToArrivePickUpAddress'
                            : 'key.cars.orderDetail.pickReturn.howToArriveDropOffAddress',
                        )}
                      </Text>
                      <Icon className={Style.mapIcon}>&#xf21b0;</Icon>
                    </Touchable>
                  </XImageBackground>
                )}
                {/** 门店政策& 门店合同翻译件参照 */}
                <View className={Style.storePolicyWrap}>
                  <Touchable onClick={onPolicyPress}>
                    <UnderText
                      fontWeight="medium"
                      className={Style.storePolicyText}
                    >
                      {Shark.getShark(
                        'key.cars.orderDetail.pickReturn.storePolicy',
                      )}
                    </UnderText>
                  </Touchable>
                  {isPick &&
                    !!contractTranslation?.title &&
                    !!contractTranslation?.urls?.[0]?.url && (
                      <>
                        <View className={Style.spiltLine} />
                        <UnderText
                          onClick={() => {
                            xRouter.navigateTo({
                              url: contractTranslation?.urls?.[0]?.url,
                            });
                          }}
                          fontWeight="medium"
                          className={Style.storePolicyText}
                        >
                          {contractTranslation?.title}
                        </UnderText>
                      </>
                    )}
                </View>
              </>
            )}
          </View>
        </View>
      </View>
    );
  },
);

export default PickReturnInfo;
