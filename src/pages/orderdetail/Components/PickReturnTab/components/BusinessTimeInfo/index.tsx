import React from 'react';
import { XView as View, xClassNames as classNames } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import UnderText from '@pages/components/UnderText';
import Shark from '@pages/shark';
import getPixel from '@pages/utils/getPixel';
import Style from './index.module.scss';
import { Utils, CarLog } from '../../../../../../Util/Index';

interface IBusinessTimeInfo {
  storeBusinessTime?: Array<any>;
  isHasTimePolicy?: boolean;
  isChargeTimePolicy?: boolean;
  handleBusinessTimePolicyModal?: () => void;
  handleBusinessTimeModal?: () => void;
}

const BusinessTimeInfo = ({
  storeBusinessTime = [],
  isHasTimePolicy,
  isChargeTimePolicy,
  handleBusinessTimePolicyModal = Utils.noop,
  handleBusinessTimeModal = Utils.noop,
}: IBusinessTimeInfo) => {
  if (!(storeBusinessTime?.length > 0)) {
    return null;
  }
  const isMoreBusinessTime = storeBusinessTime?.length > 1;
  return (
    <View className={Style.wrap}>
      {storeBusinessTime?.map((item: any, index) => {
        const { timeStr, free } = item || {};
        const isTimeStrMore =
          isMoreBusinessTime && isHasTimePolicy && timeStr.length > 20;
        const isHasBussnessText =
          timeStr?.indexOf(Shark.getShark('key.cars.orderDetail.business')) >
          -1;
        return (
          <View
            className={classNames(
              Style.timeDescItemWrap,
              storeBusinessTime.length - 1 === index &&
                Style.lastTimeDescItemWrap,
            )}
            key={`storeBusinessTime_${String(index)}`}
          >
            {!!timeStr && (
              <View className={isTimeStrMore && Style.timeDescTextWrap}>
                <Text
                  numberOfLines={1}
                  fontWeight="normal"
                  className={Style.timeDescText}
                >
                  {timeStr}
                  {isHasBussnessText
                    ? ''
                    : ` ${Shark.getShark('key.cars.orderDetail.business')}`}
                </Text>
              </View>
            )}
            {isMoreBusinessTime && (
              <Text
                fontWeight="normal"
                className={classNames(
                  Style.timeDescText,
                  Style.timeDescTipText,
                )}
              >
                {free
                  ? Shark.getShark('key.cars.orderDetail.pickReturn.freeTip')
                  : Shark.getShark('key.cars.orderDetail.pickReturn.chargeTip')}
              </Text>
            )}
            {isHasTimePolicy && index === storeBusinessTime.length - 1 && (
              <View
                testID={CarLog.LogExposure({
                  name: '曝光_订单详情页_营业外收费政策',
                })}
                style={{ top: getPixel(-2) }}
              >
                <UnderText
                  onClick={
                    isChargeTimePolicy
                      ? handleBusinessTimePolicyModal
                      : handleBusinessTimeModal
                  }
                  fontWeight="medium"
                  className={Style.timePolicyText}
                >
                  {Shark.getShark('key.cars.orderDetail.pickReturn.timePolicy')}
                </UnderText>
              </View>
            )}
          </View>
        );
      })}
    </View>
  );
};

export default React.memo(BusinessTimeInfo);
