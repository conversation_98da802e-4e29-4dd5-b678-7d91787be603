@import '../../../../../scss/color.scss';

.wrap {
  flex: 1;
}
.timeDescItemWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.lastTimeDescItemWrap {
  margin-bottom: 0px;
}
.timeDescTextWrap {
  flex: 1;
}
.timeDescText {
  color: $C_111;
  font-size: 28px;
  line-height: 36px;
  font-family: PingFangSC-Regular;
}
.moreTimeDescText {
  margin-top: 4px;
}
.timeDescTipText {
  margin-right: 8px;
}
.timePolicyText {
  color: $C_111;
  font-size: 28px;
  line-height: 36px;
  font-family: PingFangSC-Medium;
  margin-right: 18px;
}
