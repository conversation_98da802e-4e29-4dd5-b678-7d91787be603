/* eslint-disable react-native/no-color-literals */
import React, { memo, useState } from 'react';
import { XView as View } from '@ctrip/xtaro';
import Touchable from '@pages/components/Touchable';
import UnderText from '@pages/components/UnderText';
import Text from '@pages/components/Text';
import Icon from '@pages/components/Icon';
import DashedLine from '@pages/components/DashedLine';
import { useMemoizedFn } from '@pages/hooks';
import Shark from '@pages/shark';
import getPixel from '@pages/utils/getPixel';
import { CarLog, Utils } from '../../../../../../Util/Index';
import Style from './index.module.scss';

interface IFlightInfo {
  flightNumber?: string;
  flightDelayRuleTitle?: string;
  logBaseInfo?: any;
  showFlightDelayRulesModal?: () => void;
}

const FlightInfo: React.FC<IFlightInfo> = memo(
  ({
    flightDelayRuleTitle,
    logBaseInfo,
    flightNumber = '',
    showFlightDelayRulesModal = Utils.noop,
  }: IFlightInfo) => {
    const [repeat, setRepeat] = useState(0);
    const infoLayout = useMemoizedFn(e => {
      const { height } = e.nativeEvent.layout;
      const calRepeat = Math.ceil(height / getPixel(8)) - 1;
      if (calRepeat !== repeat) {
        setRepeat(calRepeat);
      }
    });
    return (
      <View>
        <View className={Style.titleWrap}>
          <View className={Style.titleLeftWrap}>
            <Icon className={Style.titleIcon}>&#xf2191;</Icon>
          </View>
          <View>
            <Text fontWeight="bold" className={Style.titleText}>
              {Shark.getShark('key.cars.orderDetail.pickReturn.flightTitle', [
                flightNumber,
              ])}
            </Text>
          </View>
        </View>
        <View className={Style.row}>
          <View className={Style.dashedLineWrap}>
            <DashedLine
              direction="vertical"
              repeat={repeat}
              dashStyle={{
                backgroundColor: '#d5d5d5',
                width: getPixel(3),
              }}
            />
          </View>
          <Touchable
            onClick={showFlightDelayRulesModal}
            onLayout={infoLayout}
            debounceTime={300}
            testID="ta-flightDelay"
            className={Style.content}
          >
            {!!flightDelayRuleTitle && (
              <UnderText
                testID={CarLog.LogExposure({
                  name: '曝光_订单详情页_航班延误政策',

                  info: logBaseInfo,
                })}
                fontWeight="medium"
                className={Style.delayRuleText}
              >
                {flightDelayRuleTitle}
              </UnderText>
            )}
          </Touchable>
        </View>
      </View>
    );
  },
);

export default FlightInfo;
