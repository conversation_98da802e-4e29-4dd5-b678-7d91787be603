import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import Dimensions from '@c2x/apis/Dimensions';
import React from 'react';
import {
  XView as View,
  xMakePhoneCall,
  xRouter,
  xMergeStyles,
  xClassNames as classNames,
} from '@ctrip/xtaro';
import orderDetailStore, {
  getPhoneMenus,
} from '@pages/orderdetail/state/orderdetail';
import { getImAddress } from '@pages/orderdetail/method';

import BbkModal from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import BbkComponentHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils, BbkConstants } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, icon } from '@ctrip/rn_com_car/dist/src/Tokens';

import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import c2xStyles from './customerPhoneModalC2xStyles.module.scss';
import { CarLog, Channel } from '../../../../Util/Index';
import { UITestID } from '../../../../Constants/Index';
import { CustomerPhoneModalType } from '../../../../Constants/OrderDetail';
import OrderCancelConfirmModal from '../OrderConfirmModal/Index';
import Texts, { pickUpPersonText, dropOffPersonText } from '../../Texts';
import { IStoreAttendantType, CustomerPhoneModalProps } from '../../Types';
import VirtualNumberStoreModal from '../OrderVirtualNumberStoreModal';

const { getPixel, selector, getPixelWithIphoneXBottom } = BbkUtils;
const headerHeight = 89;
const { height } = Dimensions.get('window');
const styles = StyleSheet.create({
  bgGray: {
    backgroundColor: color.grayBg,
  },
  contentContainerStyle: {
    paddingBottom: getPixelWithIphoneXBottom(62, 22),
    backgroundColor: color.grayBg,
  },
  mbcur: {
    minHeight: getPixel(BbkConstants.DEFAULT_HEADER_HEIGHT),
  },
  leftIconStyle: {
    fontSize: getPixel(42),
    left: getPixel(-10),
  },
  actionItem: {
    flexDirection: 'column',
    borderBottomWidth: StyleSheet.hairlineWidth,
    minHeight: getPixel(120),
    justifyContent: 'center',
  },
  headerContent: {
    alignItems: 'center',
    backgroundColor: color.white,
    borderTopLeftRadius: getPixel(24),
    borderTopRightRadius: getPixel(24),
    height: getPixel(headerHeight),
  },
  topLine: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: color.fontSubLight,
  },
  phoneText: {
    ...font.rcFont,
  },
});

const CustomerPhoneModal = (props: CustomerPhoneModalProps) => {
  const { type, modalVisible, onRequestClose: onCloseModal, title } = props;
  const orderId = orderDetailStore(state => state.reqOrderParams.orderId);
  const orderStatusInfo = orderDetailStore(
    state => state.orderBaseInfo?.orderStatus,
  );
  const { orderStatus } = orderStatusInfo || {};
  const pickupStore = orderDetailStore(state => state.pickupStore);
  const returnStore = orderDetailStore(state => state.returnStore);
  const phoneModalType = orderDetailStore(state => state.phoneModalType);
  const menuList =
    getPhoneMenus(pickupStore, returnStore, phoneModalType) || [];
  const storeAttendant = orderDetailStore(
    state => state.orderBaseInfo?.attr?.storeAttendant,
  );
  const vendorImUrl = orderDetailStore(state => state.response?.vendorImUrl);
  const customerServiceUrl = orderDetailStore(
    state => state.customerServiceUrl,
  );
  const tourImJumpUrl = getImAddress({
    pageId: Channel.getPageId().Order.ID,
    isPreSale: 0,
    orderId,
    extendInfo: {
      oldChatUrl: customerServiceUrl,
    },
  });
  let headerTitle = '';
  const isPhoneType =
    type === CustomerPhoneModalType.Phone ||
    type === CustomerPhoneModalType.FooterBar ||
    type === CustomerPhoneModalType.HomeOrderCard;
  const isCustomer = type === CustomerPhoneModalType.Customer;
  const isViolationPhone = type === CustomerPhoneModalType.ViolationPhone;
  const isSingleNumber =
    !isCustomer && menuList.length === 1 && menuList[0].tels.length === 1;

  if (isPhoneType) {
    headerTitle = '拨打门店电话';
  } else if (isCustomer) {
    headerTitle = '联系客服';
  } else if (isViolationPhone) {
    headerTitle = '咨询车行';
  }

  const callPhone = phone => {
    if (!phone) return;
    const phone1 = `${phone}`.replace(/\D*$/, '').replace(/\s/g, '');
    xMakePhoneCall({ phoneNumber: phone1 });
    CarLog.LogCode({
      name: '点击_订详_门店电话',

      onPressFrom: type,
    });
  };

  const onCsPress = () => {
    CarLog.LogCode({
      name: '点击_订详_弹层携程客服',

      orderID: orderId,
    });
    xRouter.navigateTo({ url: tourImJumpUrl });
  };

  const sameStore = menuList.length === 1;

  const onPressPersonPhone = phoneNumber => {
    callPhone(phoneNumber);
    CarLog.LogCode({
      name: isCustomer
        ? '点击_订单详情页_客服浮层联系送车员'
        : '点击_订单详情页_门店浮层联系送车员',

      info: {
        orderId,
        orderStatus,
      },
    });
  };
  const onRequestClose = useMemoizedFn(() => {
    // @ts-ignore 升级072
    onCloseModal();
  });

  const fetchCallBack = useMemoizedFn(() => {});

  return (
    <>
      <VirtualNumberStoreModal
        visible={false}
        onClose={onRequestClose}
        type={type}
        orderId={orderId}
        orderStatus={orderStatus}
        storeAttendant={storeAttendant}
        vendorImUrl={vendorImUrl}
        tourImJumpUrl={tourImJumpUrl}
        fetchCallBack={fetchCallBack}
      />

      {!isSingleNumber ? (
        <BbkModal
          closeModalBtnTestID={
            UITestID.car_testid_page_order_customerphone_modal_closemask
          }
          modalVisible={modalVisible}
          onRequestClose={onRequestClose}
        >
          <View className={c2xStyles.w100}>
            <BbkComponentHeader
              isBottomBorder={false}
              onPressLeft={onRequestClose}
              title={title || headerTitle}
              isLeftIconCross={true}
              leftIconStyle={styles.leftIconStyle}
              contentStyle={styles.headerContent}
              styleInner={styles.mbcur}
              leftIconTestID={
                UITestID.car_testid_page_order_customerphone_modal_header_lefticon
              }
            />

            <View
              style={{
                maxHeight: height * 0.85 - getPixel(headerHeight),
                marginTop: -getPixel(1),
              }}
            >
              <ScrollView
                contentContainerStyle={styles.contentContainerStyle}
                style={styles.bgGray}
              >
                {selector(
                  false,
                  <>
                    <View className={c2xStyles.secTipWrap}>
                      <View className={c2xStyles.dot} />
                      <BbkText>{Texts.orderCallPersonText}</BbkText>
                    </View>
                    {!!storeAttendant?.title && (
                      <BbkText
                        className={classNames(
                          c2xStyles.itemTitle,
                          c2xStyles.paddingWrap,
                        )}
                        fontWeight="medium"
                      >
                        {storeAttendant?.type === IStoreAttendantType.DropOff
                          ? dropOffPersonText(storeAttendant?.title)
                          : pickUpPersonText(storeAttendant?.title)}
                      </BbkText>
                    )}
                    {!!storeAttendant?.actionUrl && (
                      <BbkTouchable
                        className={c2xStyles.paddingWrap}
                        onPress={() =>
                          onPressPersonPhone(storeAttendant?.actionUrl)
                        }
                        testID={
                          UITestID.car_testid_page_order_customerphone_modal_personphone
                        }
                      >
                        <View className={c2xStyles.telBox}>
                          <BbkText
                            className={c2xStyles.itemText}
                            style={xMergeStyles([
                              !isCustomer &&
                                sameStore && { ...font.title4MediumStyle },
                              styles.phoneText,
                            ])}
                          >
                            {storeAttendant?.actionUrl}
                          </BbkText>
                          <BbkText type="icon" className={c2xStyles.phoneIcon}>
                            {icon.phone}
                          </BbkText>
                        </View>
                      </BbkTouchable>
                    )}
                  </>,
                )}

                {selector(
                  isCustomer,
                  <View className={c2xStyles.secTipWrap}>
                    <BbkText>
                      车辆信息、门店信息、取/还车等问题请咨询门店
                    </BbkText>
                  </View>,
                )}

                {menuList &&
                  menuList.map(
                    (item, index) =>
                      item &&
                      item.tels.length > 0 && (
                        <View
                          key={item.name}
                          style={xMergeStyles([
                            styles.actionItem,
                            {
                              borderBottomColor: color.grayBorder,
                              marginTop: isPhoneType ? getPixel(20) : 0,
                            },
                          ])}
                        >
                          {index === 1 && <View style={styles.topLine} />}
                          {/* 拨打模式并且同门店，隐藏标题 */}
                          {selector(
                            !isCustomer && sameStore,
                            null,
                            <BbkText
                              className={classNames(
                                c2xStyles.itemTitle,
                                c2xStyles.paddingWrap,
                              )}
                              fontWeight="medium"
                            >
                              {item.name}
                            </BbkText>,
                          )}
                          {item.tels &&
                            item.tels.map((tel, telIndex) => (
                              <BbkTouchable
                                key={tel}
                                className={c2xStyles.paddingWrap}
                                testID={`${UITestID.car_testid_page_order_customerphone_modal_tell_item}_${telIndex}`}
                                onPress={() => callPhone(tel)}
                              >
                                {telIndex > 0 && (
                                  <View style={styles.topLine} />
                                )}
                                <View className={c2xStyles.telBox}>
                                  <BbkText
                                    className={c2xStyles.itemText}
                                    style={xMergeStyles([
                                      !isCustomer &&
                                        sameStore && {
                                          ...font.title4MediumStyle,
                                        },
                                      false,
                                    ])}
                                  >
                                    {tel}
                                  </BbkText>
                                  <BbkText
                                    type="icon"
                                    className={c2xStyles.phoneIcon}
                                  >
                                    {icon.phone}
                                  </BbkText>
                                </View>
                              </BbkTouchable>
                            ))}
                        </View>
                      ),
                  )}

                {selector(
                  isCustomer,
                  <>
                    <View className={c2xStyles.secTipWrap}>
                      <BbkText>{Texts.orderCallCustomerText}</BbkText>
                    </View>

                    <View
                      style={xMergeStyles([
                        styles.actionItem,
                        {
                          borderBottomColor: color.grayBorder,
                        },
                      ])}
                    >
                      <BbkTouchable
                        onPress={onCsPress}
                        testID={
                          UITestID.car_testid_page_order_customerphone_modal_ctripservice
                        }
                        className={c2xStyles.paddingWrap}
                      >
                        <View className={c2xStyles.telBox}>
                          <BbkText
                            className={c2xStyles.itemSingleTitle}
                            fontWeight="medium"
                          >
                            携程在线客服
                          </BbkText>
                          <BbkText type="icon" className={c2xStyles.phoneIcon}>
                            {icon.service}
                          </BbkText>
                        </View>
                      </BbkTouchable>
                    </View>
                  </>,
                )}
              </ScrollView>
            </View>
          </View>
        </BbkModal>
      ) : (
        <OrderCancelConfirmModal
          visible={modalVisible}
          title={menuList[0].tels[0]}
          btns={[
            {
              get name() {
                return '取消';
              },
              onPress: () => {
                onRequestClose();
              },
            },
            {
              get name() {
                return '拨打';
              },
              isPrimary: true,
              onPress: () => {
                onRequestClose();
                callPhone(menuList[0].tels[0]);
              },
            },
          ]}
        />
      )}
    </>
  );
};

export default CustomerPhoneModal;
