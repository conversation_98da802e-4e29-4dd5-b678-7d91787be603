import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { xC<PERSON><PERSON><PERSON><PERSON>, xRouter, XView } from '@ctrip/xtaro';
import Touchable from '@pages/components/Touchable';
import Text from '@pages/components/Text';
import Icon from '@pages/components/Icon';
import UnderText from '@pages/components/UnderText';
import orderDetailStore from '@pages/orderdetail/state/orderdetail';
import { OrderStatusCtrip } from '../../../../Constants/OrderDetail';
import { goOrderPolicy } from '../CalendarPrice/helpers';
import { MustReadTypeEnum } from '../../../../Types/Dto/OrderDetailRespaonseType';
import styles from './index.module.scss';

const testIDMap = {
  [MustReadTypeEnum.UNKNOWN_CHARGES_CONSULTATION]: 'ta-consultationDecuction',
  [MustReadTypeEnum.RECEIPT]: 'ta-requestVoucher',
};

const MustRead = () => {
  const rentalEssential = orderDetailStore(state => state.rentalEssential);
  const { mustRead: mustReadList, displayMustRead } = rentalEssential ?? {};
  const defaultIsFold = !displayMustRead;
  const mustRead = mustReadList?.[0];

  const orderBaseInfo = orderDetailStore(state => state.orderBaseInfo);
  const { orderStatus } = orderBaseInfo ?? {};

  const { isShow, supportFold } = useMemo(() => {
    switch (orderStatus?.orderStatusCtrip) {
      case OrderStatusCtrip.PROCESSING:
      case OrderStatusCtrip.CONFIRMED:
        return {
          isShow: true,
          supportFold: true,
        };
      case OrderStatusCtrip.IN_SERVICE:
        return {
          isShow: true,
          supportFold: true,
        };
      case OrderStatusCtrip.COMPLETED:
        return {
          isShow: true,
        };
      default:
        return {
          isShow: false,
        };
    }
  }, [orderStatus?.orderStatusCtrip]);

  const [isFold, setIsFold] = useState<boolean>();
  useEffect(() => {
    setIsFold(defaultIsFold);
  }, [defaultIsFold]);

  const handleFoldChange = useCallback(() => {
    if (!supportFold) {
      return;
    }
    setIsFold(!isFold);
  }, [isFold, supportFold]);

  const goTo = useCallback(
    ({ type, url }: { type?: MustReadTypeEnum; url?: string }) => {
      if (type === MustReadTypeEnum.PICKUP_RETURN_INSTRUCTION) {
        goOrderPolicy();
        return;
      }
      if (url) {
        xRouter.navigateTo({ url });
      }
    },
    [],
  );

  return isShow ? (
    <XView className={styles.container}>
      <XView className={styles.topBorder} />
      <Touchable debounceTime={300} onClick={handleFoldChange}>
        <XView className={styles.titleLine}>
          <Text className={styles.title} fontWeight="semibold">
            {mustRead?.title}
          </Text>
          {supportFold && (
            <Icon className={styles.iconArrow}>
              {isFold ? '&#xe0b2;' : '&#xe0b3;'}
            </Icon>
          )}
        </XView>
      </Touchable>
      {!isFold && (
        <XView className={styles.textLineGroup}>
          {mustRead?.subObject?.map(({ note, title, url, type }, index) => (
            <XView
              className={xClassNames(
                styles.textLine,
                index === 0 && styles.firstTextLine,
              )}
            >
              <XView className={styles.dotBox}>
                <XView className={styles.dot} />
              </XView>
              <XView className={styles.textGroup}>
                <Text className={styles.text} fontWeight="normal">
                  {note}
                </Text>
                {!!title && (
                  <UnderText
                    testID={type ? testIDMap[type] : ''}
                    className={styles.underText}
                    fontWeight="medium"
                    onClick={() => goTo({ type, url })}
                  >
                    {title}
                  </UnderText>
                )}
              </XView>
            </XView>
          ))}
        </XView>
      )}
    </XView>
  ) : null;
};

export default MustRead;
