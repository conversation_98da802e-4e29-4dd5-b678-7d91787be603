@import '../../../scss/color.scss';

.container {
  padding: 32px;
  display: flex;
  flex-direction: column;
  background: $white;
}
.topBorder {
  height: 1px;
  background-color: $C_F0F0F0;
  position: absolute;
  top: 0px;
  width: 100%;
  left: 32px;
}
.titleLine {
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
}

.title {
  font-size: 36px;
  letter-spacing: 0px;
  line-height: 44px;
  color: $C_111;
  font-family: PingFangSC-Semibold;
}

.iconArrow {
  margin-left: auto;
  font-size: 36px;
}

.textLineGroup {
  display: flex;
  flex-direction: column;
}

.textLine {
  margin-top: 32px;
  display: flex;
  flex-direction: row;
  align-items: start;
}

.firstTextLine {
  margin-top: 18px;
}

.dotBox {
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: $C_555555;
}

.textGroup {
  margin-left: 18px;
}

.text {
  font-size: 28px;
  line-height: 36px;
  color: $C_111;
  font-family: PingFangSC-Regular;
}

.underText {
  margin-top: 8px;
  font-size: 28px;
  line-height: 36px;
  color: $C_111;
  font-family: PingFangSC-Medium;
}
