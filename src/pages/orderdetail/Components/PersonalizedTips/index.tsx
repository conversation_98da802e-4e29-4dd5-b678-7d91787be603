import React, { useCallback } from 'react';
import { XView as View, xEnv, xRouter } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import Touchable from '@pages/components/Touchable';
import shark from '@pages/shark';
import Styles from './index.module.scss';
import { OrderDetail as OrderDetailCons } from '../../../../Constants/Index';
import { AppContext } from '../../../../Util/Index';

const PersonalizedTips = props => {
  const { orderStatusCtrip, orderId } = props;

  const getQuestionList = useCallback(orderStatusCtrip => {
    switch (orderStatusCtrip) {
      case OrderDetailCons.OrderStatusCtrip.COMPLETED:
        return [
          {
            key: 'key.cars.order.voucher_mustread_dropoff_time',
            name: shark.getShark('key.cars.order.voucher_mustread_dropoff_time'),
          },
          {
            key: 'key.cars.order.voucher_mustread_dropoff_keep_clean',
            name: shark.getShark('key.cars.order.voucher_mustread_dropoff_keep_clean'),
          },
          {
            key: 'key.cars.order.voucher_mustread_dropoff_check',
            name: shark.getShark('key.cars.order.voucher_mustread_dropoff_check'),
          },
          {
            key: 'key.cars.order.voucher_mustread_dropoff_fee',
            name: shark.getShark('key.cars.order.voucher_mustread_dropoff_fee'),
          },
          {
            key: 'key.cars.order.youMayWantToKnowAll',
            name: shark.getShark('key.cars.order.youMayWantToKnowAll'),
          },
        ];
      case OrderDetailCons.OrderStatusCtrip.IN_SERVICE:
        return [
          {
            key: 'key.cars.order.voucher_mustread_pick_save_doc',
            name: shark.getShark('key.cars.order.voucher_mustread_pick_save_doc'),
          },
          {
            key: 'key.cars.order.voucher_mustread_driving_ext_fess',
            name: shark.getShark('key.cars.order.voucher_mustread_driving_ext_fess'),
          },
          {
            key: 'key.cars.order.youMayWantToKnowAll',
            name: shark.getShark('key.cars.order.youMayWantToKnowAll'),
          },
        ];
      default:
        return [
          {
            key: 'key.cars.order.voucher_mustread_updateTime',
            name: shark.getShark('key.cars.order.voucher_mustread_updateTime'),
          },
          {
            key: 'key.cars.order.voucher_mustread_how_check',
            name: shark.getShark('key.cars.order.voucher_mustread_how_check'),
          },
          {
            key: 'key.cars.order.voucher_mustread_fuel_type',
            name: shark.getShark('key.cars.order.voucher_mustread_fuel_type'),
          },
          {
            key: 'key.cars.order.youMayWantToKnowAll',
            name: shark.getShark('key.cars.order.youMayWantToKnowAll'),
          },
        ];
    }
  }, []);

  const getQuestionUrl = useCallback(orderStatusCtrip => {
    const fatUrl =
      'https://www.fat10668.qa.nt.ctripcorp.com/carhire/materialsMustRead';
    const prodUrl = 'https://m.ctrip.com/carhire/materialsMustRead';
    const baseUrl = `${
      xEnv.getDevEnv() === 'FAT' ? fatUrl : prodUrl
    }?orderId=${orderId}&locale=${
      AppContext.LanguageInfo.locale
    }&orderStatus=${orderStatusCtrip}&h5View=1&isHideNavBar=YES&transparentbar=1&tab=2&activeTo=3`;

    switch (orderStatusCtrip) {
      case OrderDetailCons.OrderStatusCtrip.COMPLETED:
        return `${baseUrl}&activeTo=3`;
      case OrderDetailCons.OrderStatusCtrip.IN_SERVICE:
        return `${baseUrl}&activeTo=2`;
      default:
        return `${baseUrl}&activeTo=1`;
    }
  }, []);

  return (
    <View>
      <Text className={Styles.title} fontWeight="bold">
        {shark.getShark('key.cars.order.youMayWantToKnow')}
      </Text>
      <Touchable
        className={Styles.tagContainer}
        onClick={() => {
          xRouter.navigateTo({ url: getQuestionUrl(orderStatusCtrip) });
        }}
      >
        {getQuestionList(orderStatusCtrip)?.map(item => (
          <View
            className={Styles.tagItem}
            key={item.key}
            testID="ta-storePolicy"
          >
            <Text style={Styles.tagText}>{item.name}</Text>
          </View>
        ))}
      </Touchable>
    </View>
  );
};

export default PersonalizedTips;
