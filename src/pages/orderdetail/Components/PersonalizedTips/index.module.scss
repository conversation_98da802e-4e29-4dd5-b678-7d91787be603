@import '../../../scss/color.scss';

.title {
  font-size: 36px;
  line-height: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: $C_111;
  font-family: PingFangSC-Semibold;
}
.tagItem {
  flex-direction: row;
  align-items: center;
  padding-top: 16px;
  padding-bottom: 16px;
  padding-left: 24px;
  padding-right: 24px;
  border-radius: 8px;
  margin-top: 16px;
  margin-right: 16px;
  background-color: $f5f7fA;
  min-height: 70px;
}
.tagText {
  margin-right: 8px;
  color: $C_111;
  font-size: 28px;
  line-height: 38px;
}
.tagContainer {
  flex-direction: row;
  flex-wrap: wrap;
}
