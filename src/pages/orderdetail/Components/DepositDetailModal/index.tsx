/* eslint-disable react-native/no-color-literals */
/* eslint-disable react/jsx-props-no-spreading */
import React, { memo, useState } from 'react';
import {
  XView as View,
  XBoxShadow,
  xClassNames as classNames,
  XScrollView,
  XPoplayer,
} from '@ctrip/xtaro';
import ModalHeader from '@pages/components/ModalHeader';
import Icon from '@pages/components/Icon';
import Text from '@pages/components/Text';
import getPixel from '@pages/utils/getPixel';
import { vw, vh } from '@pages/utils/util';
import Touchable from '@pages/components/Touchable';
import orderDetailStore, {
  getDepositInfo,
  getOrderDetailDepositInfo,
} from '@pages/orderdetail/state/orderdetail';
import sesameStore from '@pages/orderdetail/state/sesame';
import CreditRentLogo from '../DepositBox/CreditRentLogo';
import c2xStyles from './index.module.scss';
import DepositIntroduceModal from '../DepositIntroduceModal/Index';
import DepositRateIntroduceModal from '../DepositRateIntroduceModal/Index';
import {
  ShowDepositType,
  DepositStatus,
  FreeDepositType,
} from '../../../../Constants/OrderDetail';
import { DepositLabelType } from '../../enum';
import { UITestID } from '../../../../Constants/Index';
import {
  DepositTip,
  PayOnlineDTO,
} from '../../../../Types/Dto/OrderDetailRespaonseType';
import DepositProgress from '../DepositProgress';
import { DepositTableOSD } from '../DepositBox';
import { DepositItem } from '../../../../Types/Dto/OSDQueryOrderType';
import Texts from '../../Texts';

interface IpreAuthDescTips {
  title: string;
  btnText: string;
  url: string;
}

const headerHeight = 100;

export const isHasCreditBothFree = freeDepositData => {
  const { freeDepositType, depositStatus } = freeDepositData || {};
  // 双免订单并且已经通过程信分或者芝麻验证
  return (
    (depositStatus === DepositStatus.CreditRent ||
      depositStatus === DepositStatus.Zhima) &&
    freeDepositType === FreeDepositType.Both
  );
};

export interface IDepositInfo {
  depositDescTable: any;
  showCreditRent: boolean;
  showPreAuthRent: boolean;
  showDepositType: ShowDepositType;
  preAuthDescTips: IpreAuthDescTips[];
  depositTableTitle: string;
  zhimaBtnText: string;
  isBeforeNow: boolean;
  preAuthWarning: string;
  preAuthDescTime: string;
  preAuthDescFeature: string;
  hasCreditFreeLabelType: DepositLabelType;
  realPayItems: any;
  creaditRentOutDesc: string;
  depositCreditTitle: string;
  depositCreditSubTitle: string;
  isMax?: boolean;
  tips?: DepositTip[];
  payOnlineInfo?: PayOnlineDTO;
  tipsExplainDesc?: string;
  btnColorIsGray?: boolean;
  depositItems: DepositItem[];
}

export interface IDepositDetailModal {
  modalVisible?: boolean;
  orderId?: string;
  autoRefresh?: () => void;
  vendorPreAuthInfo?: any;
  setDepositDetailModalVisible?: (data: any) => void;
  onAuthentication?: (data?: any) => void;
  depositInfo?: IDepositInfo;
  authStatus?: number;
  result?: any;
  hasCreditBothFree?: boolean;
  onAuthOrderConfirm?: (data?: any) => void;
  setOrderEhiFreeDepositModalVisible?: (data?: any) => void;
  freezeDepositExplain?: Array<any>;
  showFreezeDeposit?: boolean;
  isOrderDataByPhone?: boolean;
  freeAuthLogData?: any;
  depositPayOnline?: () => void;
  depositData?: any;
  zhimaTraceInfo?: any;
}

export enum RealPayItemType {
  Title = 0,
  Content = 1,
}

const DepositDetailModal: React.FC<IDepositDetailModal> = memo(() => {
  const modalVisible = orderDetailStore(
    state => state.depositDetailModalVisible,
  );
  const freeDeposit = orderDetailStore(state => state.freeDeposit);
  const freezeDepositExplain = orderDetailStore(
    state => state.freezeDepositExplain,
  );
  const orderBaseInfo = orderDetailStore(state => state.orderBaseInfo);
  const authStatus = sesameStore(state => state.authStatus);
  const userName = sesameStore(state => state.userName);
  const depositInfo = getOrderDetailDepositInfo(
    authStatus,
    freeDeposit,
    userName,
    orderBaseInfo,
  );
  const depositData = getDepositInfo(freeDeposit);
  const setDepositDetailModalVisible = orderDetailStore(
    state => state.setDepositDetailModalVisible,
  );

  const {
    payOnlineInfo,
    depositItems,
    depositTableTitle,
    hasCreditFreeLabelType,
    depositDescTable,
    realPayItems,
    showFreezeDeposit,
  } = depositInfo || {};
  const { payingDesc } = payOnlineInfo || {};

  const [isShowIntroModal, setIntroModal] = useState(false);
  const [isShowRateIntroduceModal, setIsShowRateIntroduceModal] =
    useState(false);

  const onClose = () => {
    setDepositDetailModalVisible({ visible: false });
  };

  const {
    depositItemName = Texts.depositPolice,
    depositItemTitle,
    depositStatus,
    freeDepositType,
  } = depositData;
  const isHasRateIntroduce = !!depositItemTitle?.feeContrast?.desc;
  const isShowBothFreeDeposit =
    [DepositStatus.CreditRent, DepositStatus.Zhima].includes(depositStatus) &&
    [FreeDepositType.Both, FreeDepositType.Auth].includes(freeDepositType);
  const hasCreditBothFree = isHasCreditBothFree(depositInfo);
  const getDepositContent = () => {
    // 国内：到店付押金场景/非双免场景｜境外：到店付押金场景
    if (!isShowBothFreeDeposit) {
      return (
        <View className={c2xStyles.depositDetail}>
          <View className={c2xStyles.secTitleWrap}>
            <View className={c2xStyles.titleWrap}>
              <Text className={c2xStyles.secTitleOSD} fontWeight="bold">
                {depositTableTitle}
              </Text>
              <View className={c2xStyles.creditRentWrap}>
                <CreditRentLogo type={hasCreditFreeLabelType} />
              </View>
            </View>
          </View>

          {!!depositDescTable && (
            <View className={c2xStyles.depositBoxWrap}>
              {/* @ts-ignore */}
              <DepositTableOSD depositItems={depositItems} />

              {showFreezeDeposit &&
                freezeDepositExplain?.length > 0 &&
                !hasCreditBothFree && (
                  <Touchable
                    className={c2xStyles.depositQues}
                    testID={
                      UITestID.car_testid_page_order_depositdetail_modal_intro
                    }
                    debounceTime={300}
                    onClick={() => setIntroModal(true)}
                  >
                    <Text
                      fontWeight="normal"
                      className={c2xStyles.depositQuesText}
                    >
                      什么是冻结押金
                    </Text>
                    <Icon className={c2xStyles.depositQuesIcon}>&#xe931;</Icon>
                  </Touchable>
                )}
            </View>
          )}

          {!!realPayItems && realPayItems.length > 0 && (
            <View className={c2xStyles.realPayWrap}>
              {realPayItems.map(({ description, type }, index) => (
                <>
                  {type === RealPayItemType.Title && (
                    <Text
                      className={c2xStyles.realPayTitle}
                      fontWeight="medium"
                    >
                      {description}
                    </Text>
                  )}
                  {type === RealPayItemType.Content && (
                    <View
                      className={c2xStyles.realPayDotwrap}
                      key={`realPayItems_${String(index)}`}
                    >
                      <Text
                        fontWeight="normal"
                        className={c2xStyles.realPayDot}
                      >
                        ·
                      </Text>
                      <Text
                        fontWeight="normal"
                        className={c2xStyles.realPayContent}
                      >
                        {description}
                      </Text>
                    </View>
                  )}
                </>
              ))}
            </View>
          )}
        </View>
      );
    }
    return (
      <XBoxShadow
        className={c2xStyles.freeDepositContainer}
        coordinate={{ x: 0, y: getPixel(2) }}
        color="rgba(0, 0, 0, 0.08)"
        opacity={1}
        blurRadius={getPixel(6)}
        elevation={2}
      >
        <Text className={c2xStyles.freeDepositTitleText} fontWeight="bold">
          {depositItemTitle?.title || Texts.freeDepositOrder}
        </Text>
        {!!depositItemTitle?.subTitle && (
          <View className={c2xStyles.osdSubTitleWrap}>
            <Text fontWeight="normal" className={c2xStyles.osdTitle}>
              {Texts.freeDepositText}
            </Text>
            <Text
              fontWeight="normal"
              className={classNames(c2xStyles.osdTitle, c2xStyles.osdSubTitle)}
            >
              {depositItemTitle?.subTitle}
            </Text>
            {isHasRateIntroduce && (
              <Touchable
                debounceTime={300}
                onClick={() => setIsShowRateIntroduceModal(true)}
                testID={
                  UITestID.c_testid_orderDetail_deposit_detail_modal_help_icon
                }
              >
                <Icon className={c2xStyles.freeDepositIcon}>&#xe931;</Icon>
              </Touchable>
            )}
          </View>
        )}
      </XBoxShadow>
    );
  };

  return (
    <>
      <DepositIntroduceModal
        freezeDepositExplain={freezeDepositExplain}
        visible={isShowIntroModal}
        onClose={() => setIntroModal(false)}
      />

      {isHasRateIntroduce && (
        <DepositRateIntroduceModal
          desc={depositItemTitle?.feeContrast?.desc}
          title={depositItemTitle?.feeContrast?.title}
          visible={isShowRateIntroduceModal}
          onClose={() => setIsShowRateIntroduceModal(false)}
        />
      )}
      <XPoplayer visible={modalVisible} onClose={onClose}>
        <View style={{ maxHeight: vh(85) }}>
          <ModalHeader
            title={depositItemName}
            onPressLeft={onClose}
            testID={
              UITestID.car_testid_page_order_detail_deposit_detail_modal_header
            }
            leftIconTestID={
              UITestID.car_testid_page_order_depositdetail_modal_header_lefticon
            }
          />
          {!!payingDesc && (
            <View className={c2xStyles.payingDescWrap}>
              <Text className={c2xStyles.payingDesc}>{payingDesc}</Text>
            </View>
          )}
          <XScrollView
            style={{
              width: vw(100),
              maxHeight: vh(100) * 0.85 - getPixel(headerHeight),
              backgroundColor: '#fff',
            }}
          >
            <View
              testID={
                UITestID.car_testid_page_order_detail_deposit_detail_modal
              }
              className={c2xStyles.depositContenWrap}
            >
              {getDepositContent()}
            </View>
            <DepositProgress />
            <View className={c2xStyles.spaceView} />
          </XScrollView>
        </View>
      </XPoplayer>
    </>
  );
});

export default DepositDetailModal;
