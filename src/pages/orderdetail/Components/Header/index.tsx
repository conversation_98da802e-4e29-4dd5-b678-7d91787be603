/* eslint-disable react/jsx-props-no-spreading */
import React, { memo, useCallback, useMemo } from 'react';
import {
  XView,
  XLinearGradient,
  xRouter,
  XHeaderView,
  xSetClipboardData,
} from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import Touchable from '@pages/components/Touchable';
import Icon from '@pages/components/Icon';
import orderDetailStore from '@pages/orderdetail/state/orderdetail';
import Shark from '@pages/shark';
import {
  CustomerPhoneModalType,
  OrderStatusCtrip,
} from '../../../../Constants/OrderDetail';
import { getImAddress } from '../../method';
import Channel from '../../../../Util/Channel';
import styles from './index.module.scss';

interface Props {
  goback?: () => void;
  onHeaderLayout?: (e: any) => void;
}

const OdHeader: React.FC<Props> = ({ goback, onHeaderLayout }) => {
  const orderInfo = orderDetailStore(state => state.orderBaseInfo);

  const showCustomerCallModal = orderInfo?.attr?.showCustomerCallModal;
  const isdCarMgImUrl = orderDetailStore(state => state.isdCarMgImUrl);
  const customerServiceUrl = orderDetailStore(
    state => state.customerServiceUrl,
  );
  const serviceUrl = isdCarMgImUrl || customerServiceUrl;
  const orderId = orderDetailStore(state => state.reqOrderParams?.orderId);

  const setPhoneModalVisible = orderDetailStore(
    state => state.setPhoneModalVisible,
  );

  const linearGradientColors = useMemo(() => {
    switch (orderInfo?.orderStatus?.orderStatusCtrip) {
      case OrderStatusCtrip.WAITING_PAY:
        return ['#FFFBF8', '#FEF8F3'];
      case OrderStatusCtrip.CONFIRMED:
        return ['#E3F6F0', '#EFFBF7'];
      default:
        return ['#F9FCFF', '#F3F9FF'];
    }
  }, [orderInfo?.orderStatus]);

  const handleCopy = useCallback(() => {
    xSetClipboardData({
      data: `${orderInfo?.orderId}`,
    });
  }, [orderInfo?.orderId]);

  const tourImJumpUrl = useMemo(
    () =>
      getImAddress({
        pageId: Channel.getPageId().Order.ID,
        isPreSale: 0,
        orderId,
        extendInfo: {
          oldChatUrl: serviceUrl,
        },
      }),
    [orderId, serviceUrl],
  );

  const onIMChat = () => {
    if (showCustomerCallModal) {
      setPhoneModalVisible({
        visible: true,
        phoneModalType: CustomerPhoneModalType.Customer,
      });
    } else {
      xRouter.navigateTo({ url: tourImJumpUrl });
    }
  };

  return (
    <XLinearGradient
      useAngle={true}
      angle={180}
      locations={[0, 1]}
      colors={linearGradientColors}
      className={styles.linearGradientWrap}
      onLayout={onHeaderLayout}
    >
      <XHeaderView
        statusBarProps={{
          translucent: true,
        }}
        translucent={true}
        backgroundColor="transparent"
        renderLeftView={() => (
          <Touchable testID="ta-back" onClick={goback}>
            <XView className={styles.back}>
              <Icon className={styles.backIcon}>&#xe015;</Icon>
            </XView>
          </Touchable>
        )}
        renderCenterView={() => (
          <Touchable onClick={handleCopy}>
            <Text
              className={styles.titleText}
              fontWeight="medium"
              numberOfLines={1}
            >
              {Shark.getShark('key.cars.order.orderId')}{' '}
              {orderInfo?.orderId ?? ''}
            </Text>
          </Touchable>
        )}
        renderRightView={() =>
          orderInfo?.orderId && (
            <XView className={styles.iconGroup}>
              <Touchable testID="ta-service" onClick={onIMChat}>
                <XView className={styles.iconBox}>
                  <Icon className={styles.icon}>&#xf00d6;</Icon>
                  <Text className={styles.iconText} numberOfLines={1}>
                    {Shark.getShark('key.cars.order.customerService')}
                  </Text>
                </XView>
              </Touchable>
            </XView>
          )
        }
      />
    </XLinearGradient>
  );
};

export default memo(OdHeader);
