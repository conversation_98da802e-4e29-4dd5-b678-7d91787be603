import React, { memo } from 'react';
import { XView as View, xClassNames as classNames } from '@ctrip/xtaro';
import Shark from '@pages/shark';
import Text from '@pages/components/Text';
import orderDetailStore from '@pages/orderdetail/state/orderdetail';
import c2xStyles from './additionalProductC2xStyles.module.scss';

const AdditionalProduct = memo(() => {
  const equipmentInfo = orderDetailStore(state => state.equipmentInfo);
  const extraInfos = equipmentInfo?.equipmentInfo || [];
  if (!(extraInfos?.length > 0)) return null;
  return (
    <View className={c2xStyles.Container}>
      <Text className={c2xStyles.PTitle} fontWeight="bold">
        {Shark.getShark('key.cars.orderDetail.AdditionalProduct')}
      </Text>
      {extraInfos.map((item, idx) => (
        <View
          key={`extraInfos_item_${String(idx)}`}
          className={classNames(c2xStyles.Content, idx === 0 && c2xStyles.mt32)}
        >
          <View className={c2xStyles.ProListItem}>
            <Text fontWeight="normal" className={c2xStyles.SubTitle}>
              {item.name}
            </Text>
            {item.quant && (
              <View className={c2xStyles.quantWrap}>
                <Text
                  fontWeight="normal"
                  className={c2xStyles.SubTitle}
                >{`x${item.quant}`}</Text>
              </View>
            )}
          </View>
        </View>
      ))}
    </View>
  );
});
export default AdditionalProduct;
