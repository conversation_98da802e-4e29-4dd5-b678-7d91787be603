@import '../../../../Common/src/Tokens/tokens/color.scss';
@import '../../../scss/color.scss';

.Container {
  background-color: $white;
  padding-top: 40px;
  padding-left: 32px;
  padding-right: 32px;
  margin-top: 16px;
  padding-bottom: 40px;
}
.quantWrap {
  margin-left: 24px;
}
.ProListItem {
  flex-direction: row;
  margin-top: 16px;
}
.PTitle {
  font-size: 36px;
  line-height: 48px;
  height: 48px;
  color: $C_111;
  font-family: PingFangSC-Semibold;
}
.mt32 {
  margin-top: 32px;
}
.Content {
  flex-direction: row;
}
.icon {
  font-size: 30px;
  margin-right: -6px;
  margin-top: 2px;
  color: $C_00b87a;
}
.SubTitle {
  font-size: 28px;
  line-height: 38px;
  font-family: PingFangSC-Regular;
  color: $C_111;
}
