/* eslint-disable react/jsx-props-no-spreading */
import React, { memo } from 'react';
import { XPoplayer, XScrollView } from '@ctrip/xtaro';
import ModalHeader from '@pages/components/ModalHeader';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { vh } from '@pages/utils/util';
import orderDetailStore from '@pages/orderdetail/state/orderdetail';
import VehicleModal from '../VehicleModal';

const { getPixel } = BbkUtils;

export interface PropsType {
  section?: any;
  visible: boolean;
  vehicleCode: string;
  vehicleNameProps: any;
  setVehPopData: (data: Object) => void;
  recommendTip?: string;
  recommendVendorList?: Array<any>;
  klbVersion?: number;
}

const VehModal = memo((props: PropsType) => {
  const { visible, section } = props;
  const { setOrderModalsVisible } = orderDetailStore.getState();
  const onCancel = () => {
    setOrderModalsVisible({
      vehicleInfoModal: {
        visible: false,
      },
    });
  };
  return (
    <XPoplayer visible={visible} onClose={onCancel}>
      <ModalHeader onPressLeft={onCancel} title={section?.modalTitle} />
      <XScrollView style={{ maxHeight: vh(80) - getPixel(256) }}>
        <VehicleModal {...props} onCancel={onCancel} />
      </XScrollView>
    </XPoplayer>
  );
});

export default VehModal;
