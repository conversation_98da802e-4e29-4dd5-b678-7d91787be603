import React, { PureComponent } from 'react';
import memoize from 'memoize-one';
import DeviceEventEmitter from '@pages/components/DeviceEventEmitter';
import Dimensions from '@pages/components/Dimensions';
import Event from '@c2x/apis/Event';
import {
  XView as View,
  xRouter,
  XViewExposure,
  xClassNames as classNames,
} from '@ctrip/xtaro';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import Text from '@pages/components/Text';
import getPixel from '@pages/utils/getPixel';
import { ensureFunctionCall } from '@pages/utils/util';
import shark from '@pages/shark';
import Styles from './index.module.scss';
import {
  ORDER_BUTTON,
  ReviewButton,
  DirectOpen,
  CustomerPhoneModalType,
} from '../../../../../../Constants/OrderDetail';
import {
  <PERSON><PERSON><PERSON>,
  Utils,
  EventHelper,
  GetAB,
} from '../../../../../../Util/Index';
import {
  Platform,
  EventName,
  UITestID,
  Url,
} from '../../../../../../Constants/Index';
import ConfirmModal from '../../../OrderConfirmModal/Index';
import { IConfirmModal, OptionProps } from '../../types';
import CnButton from '../CnButton';

const { width } = Dimensions.get('window');

const getDefaultTestId = id => {
  let testId = '';
  let taTestID = '';
  switch (id) {
    case ORDER_BUTTON.BookAgain:
      testId = CarLog.LogExposure({
        name: '曝光_订单详情页_再次预订',
      });
      taTestID = 'ta-bookAgain';
      break;
    case ORDER_BUTTON.PrintInvoice:
      testId = CarLog.LogExposure({ name: '曝光_订单详情页_报销凭证' });
      break;
    default:
      testId = '';
  }
  return { testId, taTestID };
};

class OptionButtons extends PureComponent<
  OptionProps & any,
  {
    confirmModal: IConfirmModal;
  }
> {
  constructor(props) {
    super(props);
    this.state = {
      confirmModal: {
        visible: false,
      },
    };
  }

  componentDidMount() {
    DeviceEventEmitter.addListener(EventName.orderToRebookNotice, value => {
      if (value === 'rebook') {
        this.toRebook();
      } else if (value === 'cancel') {
        this.toCancel();
      }
    });
    this.onDirectOpen();
  }

  componentWillUnmount() {
    Event.removeEventListener(EventName.orderToRebookNotice);
  }

  onDirectOpen = () => {
    const { directOpen, orderBaseInfo } = this.props;
    const directOpenId = Number(directOpen);

    const { allOperations = [] } = orderBaseInfo;
    let operaterBtn: any = null;
    switch (directOpenId) {
      case DirectOpen.CancelBooking:
        operaterBtn = allOperations.find(
          item => item.operationId === ORDER_BUTTON.CancelBooking,
        );
        break;
      case DirectOpen.ModifyOrder:
        operaterBtn = allOperations.find(
          item => item.operationId === ORDER_BUTTON.ModifyOrder,
        );
        break;
      default:
        break;
    }
    if (operaterBtn?.enable) {
      this.onBtnPress(operaterBtn.operationId);
    }
  };

  getOptionBtnExposureData = memoize((id: number) => {
    let enName = '';
    switch (id) {
      case ORDER_BUTTON.CancelBooking:
        enName = '曝光_订单详情页_取消订单';
        break;
      default:
        break;
    }
    if (!enName) return '';
    return CarLog.LogExposure({
      name: enName,
    });
  });

  showPhoneModal = () => {
    const { setPhoneModalVisible, finalQueryIsFinish } = this.props;
    if (!finalQueryIsFinish) return;
    CarLog.LogCode({
      name: '点击_订单详情页_联系门店',
    });
    setPhoneModalVisible({
      visible: true,
      phoneModalType: CustomerPhoneModalType.FooterBar,
    });
  };

  getCNButtons = memoize((operationButtons, orderBaseInfo) => {
    // 按钮更新时，storage 可能发生变化，需要重新获取
    const { orderId, buttonWidth } = this.props;

    const dom = operationButtons?.map(item => {
      const id = item.operationId;
      const btnUnable = !item.enable;
      switch (id) {
        case ORDER_BUTTON.CancelBooking:
          return (
            <CnButton
              key={`oporateBtn_${id}`}
              text={item.buttonName}
              isGray={btnUnable}
              disabled={btnUnable}
              onPress={() => {
                this.goToCancelPage(item, id);
              }}
              id={id}
              buttonWidth={buttonWidth}
              testID={this.getOptionBtnExposureData(id)}
              taTestID="ta-cancelOrder"
            />
          );

        case ORDER_BUTTON.ModifyOrder:
          return (
            <XViewExposure
              key={`oporateBtn_${id}`}
              testID={CarLog.LogExposure({
                name: '曝光_订单详情页_修改订单',

                status: item.enable ? 1 : 2, // 1 有效露出，2 有效置灰
                orderStatus: orderBaseInfo?.orderStatusDesc || '',
                orderStatusCode: orderBaseInfo?.orderStatus || '',
                orderId,
              })}
            >
              <CnButton
                text={item.buttonName}
                // 出境修改订单按钮置灰逻辑
                isGray={btnUnable}
                disabled={btnUnable} // 等价格接口回调
                onPress={this.modifyOrderFn}
                id={id}
                buttonWidth={buttonWidth}
              />
            </XViewExposure>
          );

        case ORDER_BUTTON.Comments:
          return (
            <CnButton
              key={`oporateBtn_${id}`}
              text={item.buttonName}
              isGray={!item.enable}
              disabled={!item.enable}
              onPress={() => this.onBtnPress(id, item?.code, item?.url)}
              id={id}
              testID={CarLog.LogExposure({
                name: '曝光_订单详情页_自主操作栏_点评',

                info: {
                  commentType: item.code,
                },
              })}
              buttonWidth={buttonWidth}
              taTestID="ta-toReview"
            >
              {!!item.label && (
                <View className={Styles.returnTipWrap}>
                  <Text className={Styles.returnTipText}>{item.label}</Text>
                </View>
              )}
            </CnButton>
          );

        default:
          return (
            <CnButton
              key={`oporateBtn_${id}`}
              text={item.buttonName}
              onPress={() => this.onBtnPress(id, item?.code, item?.url)}
              id={id}
              isGray={!item.enable}
              disabled={!item.enable}
              buttonWidth={buttonWidth}
              testID={getDefaultTestId(id)?.testId}
              taTestID={getDefaultTestId(id)?.taTestID}
            />
          );
      }
    });

    // 联系门店【必出】
    dom.push(
      <CnButton
        key={`orderFees_btn_${14}`}
        text={shark.getShark('key.cars.order.contactStore')}
        onPress={this.showPhoneModal}
        id={14}
        buttonWidth={buttonWidth}
        isLast={true}
        taTestID="ta-contactStore"
      />,
    );

    const buttons = dom.filter(item => item !== null);

    return buttons;
  });

  goToCancelPage = (item, id) => {
    const { fetchQueryCancelFee, isKlbVersion } = this.props;
    // 存在取消截止时间 且 已过期

    CarLog.LogCode({ name: '点击_订单详情页_取消订单' });
    if (
      GetAB.isOSDNewOrderCancel(isKlbVersion) &&
      item?.contents?.[0]?.description
    ) {
      this.showCancelConfirmModal({
        title: shark.getShark('key.cars.order.cancelConfirmModal.warmTips'),
        cancelBtnName: shark.getShark(
          'key.cars.order.cancelConfirmModal.thinkTwice',
        ),
        submitBtnName: shark.getShark(
          'key.cars.order.cancelConfirmModal.continueToCancel',
        ),
        submitFn: fetchQueryCancelFee,
        contentText: item?.contents?.[0]?.description,
        exposeTestID: CarLog.LogExposure({
          name: '曝光_订单详情页_取消挽留弹窗',
        }),
      });
    } else {
      this.onBtnPress(id);
    }
  };

  toCancel = () => {
    const { fetchQueryCancelFee } = this.props;
    fetchQueryCancelFee();
  };

  toRebook = () => {
    let url;

    const { goNewHomeParams, setLocationInfo, setDateInfo, setLatestDateInfo } =
      this.props;
    setLocationInfo(goNewHomeParams.rentalLocation);
    setDateInfo(goNewHomeParams.rentalDate);
    setLatestDateInfo({
      time: dayjs(goNewHomeParams.rentalDate.pickup),
      showToast: false,
      callback: rentalLocationAndDate => {
        const data = { ...rentalLocationAndDate };
        const baseUrl = Platform.CAR_CROSS_URL.CTQHOME.OSD;
        url = `${baseUrl}&data=${encodeURIComponent(JSON.stringify(data))}`;
        xRouter.navigateTo({ url });
      },
    });

    xRouter.navigateTo({ url });
  };

  showReviewModal = () => {
    const { orderId } = this.props;
    this.goOsdComment(orderId);
  };

  goOsdComment = orderId => {
    const param = {
      oid: orderId,
      isHideNavBar: 'YES',
      isFromCRNWebDetail: false,
      clienttype: Utils.getClientType(),
    };
    const url = `${Utils.getHost()}/webapp/cars/osd/osd/osdcomment?${Utils.toParams(
      param,
    )}`;
    CarLog.LogCode({
      name: '点击_订单详情页_按钮_点评',

      from: 'osd-orderdetail',
    });
    xRouter.navigateTo({ url });
  };

  modifyOrder = () => {
    // 出境修改订单
    const {
      isNewOsdModifyOrder,
      osdModifyOrderInit,
      setLocationAndDatePopIsShow,
    } = this.props;
    if (isNewOsdModifyOrder) {
      osdModifyOrderInit();
      setLocationAndDatePopIsShow({ visible: true });
    }
  };

  showCancelConfirmModal = ({
    title,
    contentText,
    cancelBtnName,
    submitBtnName,
    submitFn,
    exposeTestID,
  }: {
    title: string;
    contentText?: string;
    cancelBtnName: string;
    submitBtnName: string;
    submitFn: () => void;
    exposeTestID?: string;
  }) => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      confirmModal: {
        visible: true,
        data: {
          title,
          contentText: contentText || '',
          exposeTestID,
          btns: [
            {
              name: cancelBtnName,
              onPress: () => {
                setOrderModalsVisible({ confirmModal: { visible: false } });
                CarLog.LogCode({
                  name: '点击_订单详情页_取消挽留弹窗我再想想',
                });
              },
            },
            {
              name: submitBtnName,
              isPrimary: true,
              onPress: () => {
                setOrderModalsVisible({ confirmModal: { visible: false } });
                ensureFunctionCall(submitFn);
                CarLog.LogCode({
                  name: '点击_订单详情页_取消挽留弹窗继续取消',
                });
              },
            },
          ],
        },
      },
    });
  };

  showReviewUnopenedModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ reviewUnopenedModal: { visible: true } });
  };

  // 点击Button的处理函数
  onBtnPress = (id: number | string, code?: ReviewButton, link?: string) => {
    const { orderBaseInfo, isOrderDataByPhone, extendedInfo } = this.props;
    if (
      isOrderDataByPhone &&
      [
        ORDER_BUTTON.CancelBooking,
        ORDER_BUTTON.ModifyOrder,
        ORDER_BUTTON.Comments,
      ].includes(Number(id))
    ) {
      EventHelper.sendEvent(EventName.orderBtnsClickFromPhoneCheck, {});
      return;
    }
    const { orderId } = orderBaseInfo;

    let invoiceUrl;
    switch (id) {
      case ORDER_BUTTON.PrintVoucher:
        const isLicenseApprove = extendedInfo?.osdDetailVersion === 'B';
        CarLog.LogCode({ name: '点击_订单详情页_按钮_提车凭证' });
        const pvUrl = isLicenseApprove
          ? link
          : `${Url.OSD_CRN_URL}&initialPage=OsdRentalCarMaterial&orderid=${orderId}`;
        xRouter.navigateTo({ url: pvUrl || '' });
        break;
      case ORDER_BUTTON.PrintInvoice:
        CarLog.LogCode({ name: '点击_订单详情页_按钮_电子发票' });
        invoiceUrl = `${Utils.getUrlHost()}/webapp/carhire/xsd/osdinvoice?id=${orderId}`;
        xRouter.navigateTo({ url: link || invoiceUrl });
        break;
      case ORDER_BUTTON.CancelBooking:
        CarLog.LogCode({
          name: '点击_订单详情页_取消订单',
        });
        this.toCancel();
        break;
      case ORDER_BUTTON.BookAgain:
        CarLog.LogCode({
          name: '点击_订单详情页_再次预订',
        });
        this.toRebook();
        break;
      case 'priceDetail':
        CarLog.LogCode({ name: '点击_订单详情页_按钮_费用明细' });
        break;
      case ORDER_BUTTON.Comments:
        CarLog.LogCode({
          name: '点击_订单详情页_按钮_点评',

          info: {
            commentType: code,
          },
        });
        if (code !== undefined) {
          if (code === ReviewButton.Unenable) {
            this.showReviewUnopenedModal();
            break;
          } else if (link) {
            xRouter.navigateTo({ url: link });
            break;
          }
        }
        this.showReviewModal();
        break;
      case ORDER_BUTTON.ModifyOrder:
        CarLog.LogCode({
          name: '点击_订单详情页_按钮_修改订单',

          status: 1,
          orderStatus: orderBaseInfo?.orderStatusDesc || '',
          orderStatusCode: orderBaseInfo?.orderStatus || '',
          orderId,
        });
        this.modifyOrder();
        break;
      default:
        break;
    }
  };

  mySetState = (name, value) => {
    const obj = {};
    obj[name] = value;
    this.setState(obj);
  };

  hideConfirmModal = () => {
    this.mySetState('confirmModal', {
      visible: false,
    });
  };

  showConfirmModal = (config: IConfirmModal) => {
    this.mySetState('confirmModal', config);
  };

  modifyOrderFn = () => {
    this.onBtnPress(ORDER_BUTTON.ModifyOrder);
  };

  render() {
    const { confirmModal } = this.state;
    const { orderBaseInfo = {}, operationButtons } = this.props;
    const cnBtns = this.getCNButtons(operationButtons, orderBaseInfo);
    const firstLine = cnBtns?.slice(0, 4);
    const restLine = cnBtns?.slice(4);
    return (
      <>
        <View
          testID={UITestID.car_testid_comp_orderDetail_optionButtons}
          className={Styles.cnBtnsWarp}
        >
          <View
            className={classNames(Styles.wrap, Styles.cnBtnFirstWrapStyle)}
            style={{ width: width - getPixel(40) }}
          >
            {firstLine}
          </View>
          {restLine.length > 0 && (
            <View className={Styles.wrapNotFirstLine} style={{ width }}>
              {restLine}
            </View>
          )}
        </View>
        {confirmModal?.visible && (
          <ConfirmModal
            visible={confirmModal?.visible}
            title={confirmModal?.title}
            contentText={confirmModal?.desc}
            btns={[
              {
                get name() {
                  return shark.getShark('key.cars.order.hasKnow');
                },
                isPrimary: true,
                onPress: this.hideConfirmModal,
              },
            ]}
          />
        )}
      </>
    );
  }
}

export default OptionButtons;
