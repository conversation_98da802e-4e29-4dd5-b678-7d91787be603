@import '../../../../../scss/color.scss';

.returnTipWrap {
  position: absolute;
  background-color: $C_FF7529;
  border-top-start-radius: 13px;
  border-top-end-radius: 12px;
  border-bottom-end-radius: 0px;
  border-bottom-start-radius: 13px;
  right: 15px;
  top: -20px;
  height: 28px;
  padding-left: 12px;
  padding-right: 12px;
  border-width: 1px;
  border-color: $white;
}
.returnTipText {
  margin-top: 1px;
  font-size: 18px;
  line-height: 24px;
  color: $white;
}
.selfServiceTipWrap {
  position: absolute;
  background-color: $C_FF7529;
  border-top-start-radius: 13px;
  border-top-end-radius: 12px;
  border-bottom-end-radius: 0px;
  border-bottom-start-radius: 13px;
  right: 18px;
  top: -16px;
  height: 28px;
  padding-left: 12px;
  padding-right: 12px;
  border-width: 1px;
  border-color: $white;
}
.cnBtnsWarp {
  margin-top: 24px;
  margin-bottom: 24px;
}
.wrap {
  justify-content: flex-end;
  flex-direction: row;
  flex-wrap: wrap;
  padding-right: 13px;
  left: -28px;
}
.wrapNotFirstLine {
  justify-content: flex-start;
  flex-direction: row;
  flex-wrap: wrap;
  padding-right: 16px;
}
.selfServiceTipText {
  font-size: 18px;
  line-height: 24px;
  color: $white;
}
.cnBtnFirstWrapStyle {
  justify-content: flex-start;
  padding-right: 0;
  left: 0;
}


