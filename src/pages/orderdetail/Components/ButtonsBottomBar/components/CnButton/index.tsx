import React, { memo, useCallback } from 'react';
import { CnButtonTypes } from '@pages/orderdetail/Types';
import { xClassNames as classNames, XView as View } from '@ctrip/xtaro';

import Touchable from '@pages/components/Touchable';
import Text from '@pages/components/Text';
import getPixel from '@pages/utils/getPixel';
import CnButtonContainer from '../CnButtonContainer';
import Styles from './index.module.scss';
import UITestId from '../../../../../../Constants/UITestID';

const CnButton: React.FC<CnButtonTypes> = memo(
  ({
    text,
    onPress,
    disabled,
    isGray,
    id,
    testID,
    children,
    buttonWidth = 160,
    isLast = false,
    taTestID,
  }: CnButtonTypes) => {
    const itemOnPress = useCallback(() => onPress(id), [onPress, id]);
    return (
      <CnButtonContainer testID={testID}>
        <View testID={taTestID}>
          <Touchable
            className={classNames(
              Styles.cnbutton,
              isGray && Styles.cnbuttonDisable,
              isLast && Styles.cnbuttonLast,
            )}
            style={{ width: getPixel(buttonWidth) }}
            onClick={disabled ? () => {} : itemOnPress}
            testID={`${UITestId.car_testid_page_order_op_btn}_${text}`}
          >
            <Text
              className={classNames(
                Styles.buttonTxt,
                isGray && Styles.buttonTxtDisable,
                text?.length > 5 && Styles.buttonTxtFont,
              )}
              fontWeight="medium"
            >
              {text}
            </Text>
          </Touchable>
          {children}
        </View>
      </CnButtonContainer>
    );
  },
);

export default CnButton;
