/**
 * 订详底部按钮组
 */
import React from 'react';
import { XBoxShadow, xClassNames as classNames, XView } from '@ctrip/xtaro';
import { isAndroid } from '@pages/utils/util';
import CarAdapter from '@pages/components/CarAdapter';
import OrderOptionButtons from './components/OrderOptionButtons';
import Styles from './index.module.scss';
import { IButtonsBottomBar } from './types';

// 根据按钮个数获取底部按钮宽度
const getButtonsWidth = (count: number) => {
  let width = 0;
  switch (count) {
    case 1:
      width = 686;
      break;
    case 2:
      width = 335;
      break;
    case 3:
      width = 218;
      break;
    case 4:
      width = 160;
      break;
    default:
      width = 160;
      break;
  }
  return width;
};

const ButtonsBottomBar: React.FC<IButtonsBottomBar> = ({
  operationButtons,
  isFulfillmentOSD = false,
  isPositionAbsolute,
  setPhoneModalVisible,
  finalQueryIsFinish,
}) => {
  if (!operationButtons?.length) return null;
  const buttonNum = operationButtons?.length || 3;
  // 宽度计算时提前考虑联系门店按钮
  const buttonWidth = getButtonsWidth(buttonNum + 1);
  return (
    <XBoxShadow
      className={classNames(
        Styles.wrap,
        isPositionAbsolute && Styles.wrapAbsolute,
      )}
      coordinate={{ x: 0, y: 1 }}
      color={isAndroid ? 'rgba(0, 0, 0, 0.5)' : 'rgba(196, 196, 196, 0.5)'}
      opacity={1}
      blurRadius={7}
      elevation={isAndroid ? 4 : 0}
    >
      <XView>
        {/* 按钮组 */}
        <OrderOptionButtons
          operationButtons={operationButtons}
          buttonWidth={buttonWidth}
          needRenewTip={false}
          isFulfillmentOSD={isFulfillmentOSD}
          setPhoneModalVisible={setPhoneModalVisible}
          finalQueryIsFinish={finalQueryIsFinish}
        />
        <CarAdapter.Bottom />
      </XView>
    </XBoxShadow>
  );
};

export default React.memo(ButtonsBottomBar);
