import React, { memo } from 'react';
import { XBoxShadow, XView } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import Touchable from '@pages/components/Touchable';
import Icon from '@pages/components/Icon';
import serviceStore from '@pages/orderdetail/state/service';
import Utils from '../../../../Util/Utils';
import styles from './index.module.scss';

export interface ServiceProps {
  showConsultProgressModal?: () => void;
}

const Service = ({ showConsultProgressModal = Utils.noop }: ServiceProps) => {
  const serviceTitle = serviceStore(state => state.serviceTitle);
  const serviceLatestTimeDisplay = serviceStore(
    state => state.serviceLatestTime,
  );
  const serviceDesc = serviceStore(state => state.serviceDesc);
  if (!serviceTitle) {
    return null;
  }

  return (
    <XBoxShadow
      className={styles.container}
      coordinate={{ x: 0, y: 1 }}
      color="rgba(0, 0, 0, 0.06)"
      opacity={1}
      elevation={1}
      blurRadius={4}
    >
      <Touchable
        debounceTime={300}
        testID="ta-consultation"
        onClick={showConsultProgressModal}
      >
        <XView className={styles.row}>
          <XView className={styles.content}>
            <XView className={styles.textRow}>
              <Text fontWeight="medium" className={styles.title}>
                {serviceTitle}
              </Text>
              {!!serviceLatestTimeDisplay && (
                <Text
                  fontWeight="normal"
                  numberOfLines={1}
                  className={styles.time}
                >
                  {serviceLatestTimeDisplay}
                </Text>
              )}
            </XView>
            {!!serviceDesc && (
              <Text fontWeight="normal" className={styles.desc}>
                {serviceDesc}
              </Text>
            )}
          </XView>
          <Icon className={styles.iconArrow}>&#xe944;</Icon>
        </XView>
      </Touchable>
    </XBoxShadow>
  );
};

export default memo(Service);
