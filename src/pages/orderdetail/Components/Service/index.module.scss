@import '../../../scss/color.scss';

.container {
  margin-top: 32px;
  border-radius: 16px;
  background: $white;
  margin-top: 24px;
}

.row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 28px 24px;
}

.content {
  flex: 1 1 0;
}

.textRow {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.title {
  font-size: 28px;
  letter-spacing: -0.17px;
  line-height: 36px;
  color: $C_111;
}

.time {
  margin-left: 16px;
  color: $C_555555;
  font-size: 24px;
  letter-spacing: -0.15px;
  line-height: 32px;
}

.desc {
  margin-top: 2px;
  font-size: 24px;
  line-height: 34px;
  color: $white;
}

.iconArrow {
  font-size: 36px;
  color: $C_111;
}
