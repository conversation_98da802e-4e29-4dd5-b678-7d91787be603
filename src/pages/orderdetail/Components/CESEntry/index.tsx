import React, { useCallback } from 'react';
import {
  XView as View,
  xRouter,
  xClassNames as classNames,
  XLinearGradient,
} from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import Touchable from '@pages/components/Touchable';
import Icon from '@pages/components/Icon';
import shark from '@pages/shark';
import Styles from './index.module.scss';

const CESEntry = ({ ces }) => {
  const { title, subTitle, url } = ces || {};

  const onPress = useCallback(() => {
    xRouter.navigateTo({ url });
  }, [url]);

  if (!title || !url) return null;

  // 仅【是否方便】文案标红（已确认仅一种文案，前端兼容）
  const parts = title.split('{tag}');

  return (
    <XLinearGradient
      className={Styles.wrapper}
      useAngle={true}
      angle={0}
      locations={[0.2, 1]}
      colors={['rgba(247,251,255,1)', 'rgba(247,251,255,0)']}
    >
      <View className={Styles.container}>
        <Text className={Styles.title} fontWeight="semibold">
          {parts[0]}
          <Text
            className={classNames(Styles.title, Styles.titleHighLight)}
            fontWeight="semibold"
          >
            {subTitle}
          </Text>
          {parts[1]}
        </Text>

        <Touchable
          onClick={onPress}
          className={Styles.btn}
          testID="ta-feedback"
        >
          <Icon className={Styles.btnIcon}>&#xf2192;</Icon>
          <Text className={Styles.btnText} fontWeight="medium">
            {shark.getShark('key.cars.order.feedbackNow')}
          </Text>
        </Touchable>
      </View>
    </XLinearGradient>
  );
};

export default CESEntry;
