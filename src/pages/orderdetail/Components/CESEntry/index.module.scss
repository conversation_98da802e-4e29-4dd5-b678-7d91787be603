@import '../../../scss/color.scss';

.wrapper {
  margin-top: 16px;
  // background: $white;
  border: 3px solid $white;
}
.container {
  flex: 1;
  flex-direction: row;
  align-items: center;
  padding: 46px 32px;
}
.title {
  font-size: 32px;
  line-height: 46px;
  flex: 1;
  color: $C_111;
}
.titleHighLight {
  color: $ff7700;
}
.btn {
  border-radius: 8px;
  border-width: 1px;
  border-color: $C_888888;
  background-color: $white;
  margin-left: 24px;
  padding-left: 16px;
  padding-right: 16px;
  padding-top: 14px;
  padding-bottom: 14px;
  height: 64px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.btnText {
  font-size: 28px;
  line-height: 36px;
  color: $C_111;
}
.btnIcon {
  font-size: 28px;
  color: $C_111;
  margin-right: 8px;
}
