@import '../../../scss/color.scss';

.header {
  flex-direction: row;
  justify-content: space-between;
}
.title {
  font-size: 36px;
  line-height: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: $C_111;
  font-family: PingFangSC-Semibold;
}
.infoItem {
  flex-direction: row;
  align-items: center;
  margin-top: 14px;
}
.infoIcon {
  font-size: 40px;
  line-height: 40px;
  margin-right: 26px;
  color: $C_111;
}
.infoValue {
  color: $C_111;
  font-size: 28px;
  line-height: 36px;
}
.truncatedValue {
  max-width: 290px;
}
.actionText {
  font-size: 28px;
  line-height: 36px;
}
.container {
  background-color: $white;
  padding-top: 40px;
  padding-left: 32px;
  padding-right: 32px;
  padding-bottom: 40px;
  margin-top: 16px;
}
.infoContainer {
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.actionContainer {
  margin-left: 26px;
}
