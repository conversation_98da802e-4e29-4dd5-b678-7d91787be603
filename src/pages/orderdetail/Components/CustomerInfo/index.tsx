import React, { memo, useMemo, CSSProperties, useCallback } from 'react';
import { XView as View, xClassNames as classNames } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import Touchable from '@pages/components/Touchable';
import Icon from '@pages/components/Icon';
import UnderText from '@pages/components/UnderText';
import shark from '@pages/shark';
import { DriverInfo } from '../../Types';
import { ModifyDriverInfoType } from '../../enum';
import Styles from './index.module.scss';

const canEditInfos = ['contactWayList', 'flightNo'];

interface CustomerInfoProps {
  data: DriverInfo;
  testID?: string;
  style?: CSSProperties;
  titleStyle?: CSSProperties;
  showModifyDriverInfoModal?: (type?: string) => void;
}

const CustomerInfo = ({
  data: customerInfo,
  testID,
  style,
  titleStyle,
  showModifyDriverInfoModal,
}: CustomerInfoProps) => {
  const { isChangeContact } = customerInfo || {};

  // 联系方式类型（1-WeChat 2-Line 3-kakao talk 4-whatsapp 5-qq 6-当地电话）
  const getContactIcon = useCallback(contactWayType => {
    switch (contactWayType) {
      case '1':
        return '&#xf21a9;';
      case '2':
        return '&#xf21b2;';
      case '3':
        return '&#xf21b1;';
      case '4':
        return '&#xf21b4;';
      case '5':
        return '&#xf21b3;';
      case '6':
        return '&#xf21a5;';
      default:
        return '&#xf21a9;';
    }
  }, []);

  const driverInfo = useMemo(() => {
    const { contactWayList, name, email, telphone, flightNo, age } =
      customerInfo || {};
    const { contactWayValue, contactWayType } = contactWayList?.[0] || {};
    return [
      {
        value: name,
        icon: '&#xf21ab;',
      },
      {
        value: age ? `${age}${shark.getShark('key.cars.common.age')}` : '',
        icon: '&#xf21a7;',
      },
      {
        value: email,
        icon: '&#xf21ac;',
      },
      {
        value: telphone,
        icon: '&#xf21a5;',
      },
      {
        value: contactWayValue || (isChangeContact ? '- -' : ''),
        type: ModifyDriverInfoType.otherContactWays,
        icon: getContactIcon(contactWayType),
      },
      {
        value: flightNo || (isChangeContact ? '- -' : ''),
        type: ModifyDriverInfoType.flightNo,
        icon: '&#xf2191;',
      },
    ].filter(v => !!v.value);
  }, [customerInfo, isChangeContact]);

  const getCanEdit = useCallback(
    item => !!isChangeContact && canEditInfos.includes(item.type),
    [isChangeContact, canEditInfos],
  );

  if (!customerInfo) return null;

  return (
    <View testID={testID} className={Styles.container} style={style}>
      <View className={Styles.header}>
        <Text className={Styles.title} style={titleStyle} fontWeight="semibold">
          {shark.getShark('key.cars.order.bookingInfo')}
        </Text>
      </View>

      {driverInfo.map((item, index) => (
        <View
          key={
            item.type
              ? `${item.type}-${item.value}-${index}`
              : `${item.value}-${index}`
          }
          className={Styles.infoItem}
        >
          <Icon className={Styles.infoIcon}>{item.icon}</Icon>
          <View className={Styles.infoContainer}>
            <Text className={classNames(Styles.infoValue)}>{item.value}</Text>
            {getCanEdit(item) && (
              <Touchable
                debounceTime={300}
                onClick={() => {
                  showModifyDriverInfoModal?.(item.type);
                }}
                className={Styles.actionContainer}
              >
                <UnderText className={Styles.actionText} fontWeight="medium">
                  {item.value === '- -'
                    ? shark.getShark('key.cars.order.driverAdd')
                    : shark.getShark('key.cars.order.driverEdit')}
                </UnderText>
              </Touchable>
            )}
          </View>
        </View>
      ))}
    </View>
  );
};

export default memo(CustomerInfo);
