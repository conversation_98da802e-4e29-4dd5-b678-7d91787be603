import React from 'react';
import { XView as View, xRouter } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import Touchable from '@pages/components/Touchable';
import UnderText from '@pages/components/UnderText';
import shark from '@pages/shark';
import Styles from './index.module.scss';

const ReceiptModule = ({ receipt, orderId }) => {
  const { desc: receiptDesc, url: receiptUrl } = receipt || {};
  const onPressBtn = (url: string) => {
    const spiltUrl = url.split('%s');
    xRouter.navigateTo({ url: `${spiltUrl[0]}${orderId}${spiltUrl[1]}` });
  };

  if (!receiptUrl || !receiptDesc) return null;

  return (
    <View className={Styles.wrap}>
      <Text className={Styles.title} fontWeight="bold">
        {shark.getShark('key.cars.order.reimbursement')}
      </Text>
      <View className={Styles.descWrap}>
        <Text className={Styles.desc}>{receiptDesc}</Text>
      </View>
      <Touchable debounceTime={300} onClick={() => onPressBtn(receiptUrl)}>
        <UnderText className={Styles.urlText} fontWeight="medium">
          {shark.getShark('key.cars.order.downloadReimbursement')}
        </UnderText>
      </Touchable>
    </View>
  );
};

export default ReceiptModule;
