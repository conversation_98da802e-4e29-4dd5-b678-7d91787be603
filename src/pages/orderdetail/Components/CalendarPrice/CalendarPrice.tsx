import StyleSheet from '@c2x/apis/StyleSheet';
import React, {
  useEffect,
  useMemo,
  useCallback,
  useContext,
  useState,
} from 'react';
import {
  XView as View,
  XViewExposure,
  xClassNames as classNames,
} from '@ctrip/xtaro';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import {
  getPixel,
  lazySelector,
  useMemoizedFn,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xSTYLES from './calendarPriceC2xSTYLES.module.scss';
import { useRequest } from '../../../../Hooks/useRequest';
import CalendarPriceGrid from './CalendarPriceGrid';
import {
  QueryPriceCalendarRequestType,
  QueryPriceCalendarResponseType,
  PriceDaily,
  GRID_COLLAPSE_COUNT,
} from './types';
import { queryPriceCalendarRequest, isPriceCalendarResultValid } from './agent';
import { parsePriceDaily } from './helpers';
import { CalendarPriceModalContext } from './CalendarPriceContext';
import { INVOKE_FROM } from '../../enum';
import usePrevious from '../../../../Hooks/usePrevious';
import Texts from './Texts';
import { UITestID } from '../../../../Constants/Index';

const STYLES = StyleSheet.create({
  gridwrapper: {
    padding: 0,
    marginBottom: getPixel(16),
  },
});

const MAX_RETRY_TIMES = 2;

const Row: React.FC = ({ children }) => {
  return <View className={c2xSTYLES.row}>{children}</View>;
};

const useCalendarPrice = (
  initialData?: PriceDaily[],
  initialParams?: any,
  invokeFrom?: INVOKE_FROM,
) => {
  const { request, result, isLoading, isPending, error } = useRequest(
    queryPriceCalendarRequest,
    {
      initialResult: initialData,
      isResultValid: isPriceCalendarResultValid,
    },
  );

  const [requestCount, setRequestCount] = useState(0);
  const prevPending = usePrevious(isPending);

  const requestProxy = useCallback((params?: any) => {
    const data = initialParams;
    if (invokeFrom) data.invokeFrom = invokeFrom;
    return request({
      ...data,
      ...(params || {}),
    });
  }, []);

  useEffect(() => {
    if (prevPending === false && isPending === true) {
      setRequestCount(c => c + 1);
    }
  }, [prevPending, isPending, setRequestCount]);

  useEffect(() => {
    let cancel;
    // 没有初始数据且请求参数有值时，才能发送请求，否则请求会一直失败
    if (!initialData && initialParams) {
      cancel = requestProxy();
    }
    return () => {
      cancel && cancel();
    };
  }, [initialData]);

  return {
    requestCount,
    request: requestProxy,
    isLoading,
    isPending,
    result: initialData || result,
    error,
  };
};

const isQueryPriceCalendarResponse = (
  result,
): result is QueryPriceCalendarResponseType => !Array.isArray(result);

const useCalendarPriceVisible = (visible?: boolean) => {
  const visibleFlag = visible === undefined ? null : visible;
  const flag = useMemo(
    () => (visibleFlag === null ? true : visibleFlag),
    [visibleFlag],
  );
  return flag;
};

export interface CalendarPriceProps {
  requestQuery?: QueryPriceCalendarRequestType;
  sources?: Array<PriceDaily>;
  collapse?: boolean;
  description?: string;
  tips?: string;
  visible?: boolean;
  invokeFrom?: INVOKE_FROM;
  tipsTestID?: string;
  containerStyle?: any;
  texts?: {
    [key: string]: string;
  };
  isFullScreen?: boolean;
}

/**
 * 1. 加载中
 * 2. 加载失败 - 超时或者失败则重试
 * 3. 加载成功 - 展示日历价 或者 无日历价
 * 4. 直接展示日历价
 */
const CalendarPrice: React.FC<CalendarPriceProps> = props => {
  const {
    requestQuery,
    sources,
    collapse,
    visible,
    invokeFrom,
    tipsTestID,
    containerStyle,
    isFullScreen,
  } = props;
  let { tips, description } = props;
  const seeAll = Texts.allCalendarPrices;
  const calendarVisible = useCalendarPriceVisible(visible);
  if (!calendarVisible) return null;

  const { isLoading, isPending, error, result, request, requestCount } =
    useCalendarPrice(sources, requestQuery, invokeFrom);

  const overMaxRetryTimes = requestCount >= MAX_RETRY_TIMES;
  let allCalendarPrices = sources || [];
  if (result && isQueryPriceCalendarResponse(result)) {
    allCalendarPrices = result.priceDailys || [];
    tips = result.hourDesc;
    description = result.dPriceDesc;
  } else if (error && error.dPriceDesc) {
    description = error.dPriceDesc;
  }
  if (
    invokeFrom === INVOKE_FROM.LIST ||
    invokeFrom === INVOKE_FROM.VENDORLIST
  ) {
    tips = '';
  }
  const calendarPrices = useMemo(() => {
    if (!allCalendarPrices || !allCalendarPrices.length) return null;
    return parsePriceDaily(
      collapse
        ? allCalendarPrices.slice(0, GRID_COLLAPSE_COUNT)
        : allCalendarPrices,
    );
  }, [allCalendarPrices, collapse]);

  const { setCalendarPriceModal, onTipsPress } = useContext(
    CalendarPriceModalContext,
  );

  const handleRetryPress = useCallback(() => {
    request();
  }, [request]);

  const handleSeeAllPress = useCallback(() => {
    setCalendarPriceModal(true, { sources: allCalendarPrices, tips });
  }, [setCalendarPriceModal, allCalendarPrices, tips]);

  const showAllButtonVisible = collapse
    ? allCalendarPrices.length > GRID_COLLAPSE_COUNT
    : false;
  const showAllButtonEl = useMemo(
    () =>
      lazySelector(showAllButtonVisible, () => (
        <Touchable
          testID={UITestID.car_testid_calendarprice_modal_showall}
          onPress={handleSeeAllPress}
        >
          <Row>
            <BbkText className={classNames(c2xSTYLES.text, c2xSTYLES.linkText)}>
              {seeAll}
            </BbkText>
            <BbkText
              type="icon"
              className={classNames(
                c2xSTYLES.linkText,
                c2xSTYLES.iconArrowDown,
              )}
            >
              {icon.arrowDown}
            </BbkText>
          </Row>
        </Touchable>
      )),
    [showAllButtonVisible, handleSeeAllPress, seeAll],
  );

  const tipsEl = useMemo(
    () =>
      lazySelector(tips, () => (
        <XViewExposure testID={tipsTestID}>
          <Row>
            <Touchable
              testID={UITestID.car_testid_comp_order_priceDetail_tip}
              className={c2xSTYLES.row}
              onPress={onTipsPress}
            >
              <BbkText
                className={classNames(c2xSTYLES.text, c2xSTYLES.tipText)}
              >
                {tips}
              </BbkText>
              <BbkText type="icon" className={c2xSTYLES.iconHelp}>
                {icon.help}
              </BbkText>
            </Touchable>
          </Row>
        </XViewExposure>
      )),
    [tips],
  );

  const loadingEl = useMemo(
    () =>
      lazySelector(isLoading, () => (
        <Row>
          <BbkText type="icon" className={c2xSTYLES.iconLoading}>
            {icon.loading}
          </BbkText>
          <BbkText className={c2xSTYLES.text}>{Texts.loadingText}</BbkText>
        </Row>
      )),
    [isLoading],
  );

  const showLoadingFailedEl = !isLoading && error && !overMaxRetryTimes;
  const showFallbackEl = error && !isPending && overMaxRetryTimes;
  const showCalendarPriceEl = !isLoading && calendarPrices;

  const loadingFailedEl = useMemo(
    () =>
      lazySelector(showLoadingFailedEl, () => (
        <Row>
          <BbkText className={c2xSTYLES.text}>
            {Texts.loadingFailedText}
          </BbkText>
          <Touchable
            testID={UITestID.car_testid_calendarprice_modal_retry}
            onPress={handleRetryPress}
          >
            <BbkText className={classNames(c2xSTYLES.text, c2xSTYLES.linkText)}>
              {Texts.retryText}
            </BbkText>
          </Touchable>
        </Row>
      )),
    [showLoadingFailedEl, handleRetryPress],
  );

  const fallbackEl = useMemo(
    () =>
      lazySelector(showFallbackEl, () => (
        <Row>
          <BbkText
            className={classNames(c2xSTYLES.text, c2xSTYLES.fallbackText)}
          >
            {description || Texts.fallbackText}
          </BbkText>
        </Row>
      )),
    [showFallbackEl, description],
  );

  const calendarPriceEl = useMemoizedFn(() =>
    lazySelector(showCalendarPriceEl, () => (
      <>
        <CalendarPriceGrid
          sources={calendarPrices}
          isFullScreen={isFullScreen}
          containerStyle={STYLES.gridwrapper}
        />

        <View className={c2xSTYLES.content}>
          <View style={{ flex: 1 }}>
            {!!description && (
              <BbkText className={c2xSTYLES.text}>{description}</BbkText>
            )}
            {tipsEl}
          </View>
          {showAllButtonEl}
        </View>
      </>
    )),
  );

  return (
    <View className={c2xSTYLES.container} style={containerStyle}>
      {loadingEl}
      {loadingFailedEl}
      {fallbackEl}
      {calendarPriceEl()}
    </View>
  );
};

CalendarPrice.defaultProps = {
  texts: Texts,
  collapse: true,
  // description: '¥205/日均',
  // tips: '小时费收费规则',
  invokeFrom: INVOKE_FROM.OTHER,
  // sources: mockdata2,
};

export default CalendarPrice;
