/* eslint-disable react-native/no-color-literals */
import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';
import Text from '@pages/components/Text';

import Style from './index.module.scss';

interface IDepositExplain {
  explain?: string;
  isPoint?: boolean;
}

const DepositExplain = memo(({ explain, isPoint }: IDepositExplain) => {
  if (!explain) {
    return null;
  }
  return (
    <View className={Style.wrap}>
      {isPoint && <View className={Style.point} />}
      <Text fontWeight="normal" className={Style.depositExplainText}>
        {explain}
      </Text>
    </View>
  );
});

export default DepositExplain;
