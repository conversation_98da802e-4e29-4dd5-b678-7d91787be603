/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-useless-constructor */
/* eslint-disable complexity */
/* eslint-disable max-lines */
/* eslint-disable global-require */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import React, { PureComponent } from 'react';
import { XView as View, xRouter } from '@ctrip/xtaro';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import c2xStyles from './orderAmountC2xStyles.module.scss';
import { CarLog } from '../../../../Util/Index';
import BbkSkeletonLoading from '../SkeletonLoading';
import Schedule from '../Schedule';
import ReplenishPay from '../ReplenishPay';
import CancelPolicy from '../CancelPolicy';
import { Url, UITestID } from '../../../../Constants/Index';
import { DepositModalType } from '../../../../Constants/OrderDetail';
import Texts from '../../Texts';
import { PageType } from '../../enum';
import PriceInfoBlock from './PriceInfoBlock';
import RefundInfo from '../RefundInfo';

const { detailText } = Texts;

export interface IRenewalTags {
  desc: string;
  type: number;
}

interface AmountProps {
  finalQueryIsFinish: boolean;
  orderPriceInfo?: any;
  orderPrice?: any;
  orderBaseInfo?: any;
  reqData?: any;
  pickupStore?: any;
  returnStore?: any;
  cancelRuleInfo?: any;
  logBaseInfo?: any;
  fetchOrder: (data?: any) => void;
  orderRenewalEntry?: any;
  setDepositDetailModalVisible: ({ visible }) => void;
  freeDeposit: any;
  onPressTandC?: (_?: any, __?: any) => void;
  setOrderModalsVisible: (OrderModalsVisible?: any) => void;
  headerContent?: any;
  addPayments?: any;
  showOsdPriceDetailModal: () => void;
  handleRefundLayout?: (e: any) => void;
}

class OrderAmount extends PureComponent<AmountProps, {}> {
  constructor(props) {
    super(props);
  }

  showPriceDetail = () => {
    const { showOsdPriceDetailModal } = this.props;
    CarLog.LogCode({ name: '点击_详情页_打开价格明细' });
    showOsdPriceDetailModal();
  };

  popGoRerent = () => {
    const {
      pickupStore,
      orderRenewalEntry = {},
      returnStore,
      reqData,
      fetchOrder,
    } = this.props;
    const { orderId } = reqData;
    const { renewalButton } = orderRenewalEntry;
    const url = `${Url.OSD_CRN_URL}&landingto=rerent&orderId=${orderId}
    ${renewalButton ? `actionUrl=${renewalButton.actionUrl}` : ''}
    &storeTelephone=${pickupStore.storeTel}
    &originalReturnDateLocal=${dayjs(returnStore.localDateTime).format(
      'YYYYMMDDHHmmss',
    )}`;
    xRouter.navigateTo({ url, success: () => fetchOrder() });
  };

  showDepositDetailModal = type => {
    const {
      setDepositDetailModalVisible,
      setOrderModalsVisible,
      freeDeposit,
      orderBaseInfo = {},
    } = this.props;
    // type 0-押金政策 ，1-免押弹层
    if (
      type === DepositModalType.toAuthent &&
      !!freeDeposit?.freeDepositBtn?.statusType
    ) {
      setOrderModalsVisible({ depositPaymentModal: { visible: true } });
    } else {
      // 判断免押状态
      setDepositDetailModalVisible({ visible: true });
    }

    const { orderId, orderStatus } = orderBaseInfo;
    CarLog.LogCode({
      name: '订单详情页免押模块_点击',

      info: {
        orderId,
        orderStatus,
      },
    });
  };

  render() {
    const {
      orderBaseInfo = {},
      orderPriceInfo = {},
      onPressTandC,
      orderPrice,
      reqData,
      orderRenewalEntry = {},
      freeDeposit,
      headerContent,
      addPayments,
      finalQueryIsFinish,
      handleRefundLayout,
      cancelRuleInfo,
      logBaseInfo,
    } = this.props;
    const { renewalButton, renewalInfo } = orderRenewalEntry || {};
    const replenishPayLen = addPayments?.additionalPaymentList?.length;
    // 是否是海外且没有押金明细返回
    const isOsdNotHasFreeDepositData = !freeDeposit?.isHasFreeDepositData;
    return (
      <View className={c2xStyles.Container}>
        {headerContent()}
        <View className={c2xStyles.contentWrap}>
          {/* 总价订单号明细 */}
          {finalQueryIsFinish && (
            <PriceInfoBlock
              isOsdNotHasFreeDepositData={isOsdNotHasFreeDepositData}
              orderBaseInfo={orderBaseInfo}
              orderPriceInfo={orderPriceInfo}
              onPressTandC={onPressTandC}
              orderPrice={orderPrice}
              reqData={reqData}
              freeDeposit={freeDeposit}
              showPriceDetail={this.showPriceDetail}
              showDepositDetailModal={this.showDepositDetailModal}
            />
          )}
          {/* 补款入口 */}
          {replenishPayLen > 0 && (
            <ReplenishPay
              noSpace={true}
              testID={CarLog.LogExposure({ name: '曝光_订单详情页_补款模块' })}
            />
          )}
          {/* 退款模块 */}
          <RefundInfo onLayout={handleRefundLayout} />

          {finalQueryIsFinish && (
            <CancelPolicy
              cancelRuleInfo={cancelRuleInfo}
              orderBaseInfo={orderBaseInfo}
              testID={CarLog.LogExposure({
                name: '曝光_订单详情页_取消政策',
                info: logBaseInfo,
              })}
            />
          )}

          {/* 海外续租入口 */}
          {!renewalButton && renewalInfo && renewalInfo.type >= 0 && (
            <Schedule
              title="续租记录"
              subTitle={renewalInfo.title}
              content={renewalInfo?.description?.replace('￥', '¥')}
              detail={detailText}
              detailPress={this.popGoRerent}
              testID={CarLog.LogExposure({ name: '曝光_订单详情页_续租记录' })}
              taTestID={UITestID.car_testid_page_order_renewalentry}
            />
          )}
        </View>
        {!finalQueryIsFinish && (
          <BbkSkeletonLoading
            visible={true}
            pageName={PageType.OrderDetailFirstScreen1}
          />
        )}
      </View>
    );
  }
}

export default OrderAmount;
