@import '../../../scss/color.scss';

.sectionWrapOsd {
  border-bottom-width: 0.5px;
  border-color: $eef1f6;
  padding-top: 24px;
  padding-bottom: 24px;
}
.credentialEntryTitle {
  font-size: 30px;
  line-height: 38px;
  font-family: PingFangSC-Regular;
  height: 38px;
  color: $C_111;
}
.credentialDescTextNew {
  color: $blue1;
  font-size: 24px;
  line-height: 30px;
  font-family: PingFangSC-Regular;
}
.credentialDescText {
  color: $blue3;
  font-size: 24px;
  line-height: 30px;
  font-family: PingFangSC-Regular;
}
.canclePolicyDescWrap {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
}
.canclePolicyDesc {
  font-size: 24px;
  line-height: 36px;
  font-family: PingFangSC-Regular;
  color: $C_111;
}
.sectionDescTitle {
  font-size: 24px;
  line-height: 36px;
  font-family: PingFangSC-Regular;
  color: $gray666;
}
.sectionDescTitleIcon {
  font-size: 24px;
  color: $gray666;
}
.arrowRightIcon {
  font-size: 26px;
  color: $blue3;
}
.cashReturnTipIcon {
  color: $C_F51909;
  margin-left: 3px;
  font-size: 24px;
  opacity: 0.6;
}
.orderIdsLine {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 8px;
}
.subTitle3 {
  font-size: 24px;
  line-height: 32px;
  font-family: PingFangSC-Regular;
  color: $C_111;
  align-self: flex-start;
}
.subTitleMarginLeft {
  margin-left: 16px;
}
.Container {
  flex-shrink: 1;
  padding-bottom: 40px;
  background-color: $white;
  margin-left: 0px;
  margin-right: 0px;
  overflow: visible;
  z-index: 2;
}
.contentWrap {
  padding-left: 32px;
  padding-right: 32px;
}
.wrap {
  padding-left: 32px;
  padding-right: 32px;
  padding-top: 40px;
  padding-bottom: 40px;
  background-color: $white;
}
.flexRow {
  flex-direction: row;
  align-items: center;
}
