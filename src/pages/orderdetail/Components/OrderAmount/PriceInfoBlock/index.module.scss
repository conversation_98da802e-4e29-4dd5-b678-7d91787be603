@import '../../../../scss/color.scss';

.feeDetailWrap {
  padding-top: 40px;
  padding-bottom: 32px;
}
.line {
  height: 1px;
  transform: scaleY(0.5);
  background-color: $C_e5e5e5;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.amountWrap {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
}
.flexRowWrap {
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
}
.AmountSubTitle {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-right: 12px;
}
.totalPriceWrap {
  flex-direction: row;
}
.totalPriceText {
  font-size: 36px;
  line-height: 44px;
  color: $C_111;
  font-family: PingFangSC-Semibold;
}
.debatePrice {
  color: $C_FF7700;
  font-size: 26px;
  font-family: PingFangSC-Regular;
}
.onlinePayWrap {
  margin-top: 16px;
  flex-direction: row;
  margin-bottom: 8px;
}
.mt16 {
  margin-top: 16px;
}
.leftWrap {
  width: 164px;
  flex-direction: row;
}
.onlinePayText {
  font-size: 28px;
  line-height: 36px;
  color: $C_111;
  font-family: PingFangSC-Medium;
  color: $C_111;
}
.poaPayWrap {
  flex-direction: row;
  margin-bottom: 8px;
}
.poaPayText {
  font-size: 28px;
  line-height: 36px;
  font-family: PingFangSC-Medium;
  color: $C_111;
}
.depositWrap {
  flex-direction: row;
}
.depositText {
  font-size: 28px;
  line-height: 36px;
  color: $C_111;
  font-family: PingFangSC-Medium;
  color: $C_111;
}
.depositExplainText {
  font-size: 28px;
  line-height: 36px;
  color: $C_111;
  font-family: PingFangSC-Regular;
  color: $C_111;
}
.depositLabelWrap {
  border-radius: 5px;
  border-width: 1px;
  border-style: solid;
  border-color: $C_597FB3;
  padding-left: 5px;
  padding-right: 5px;
  height: 36px;
  margin-left: 10px;
}
.depositExplainWrap {
  flex: 1;
}
.depositMoreWrap {
  padding-top: 8px;
}
.depositLabelText {
  font-size: 24px;
  font-family: PingFangSC-Medium;
  color: $C_0f4999;
}
.operateWrap {
  flex-direction: row;
  align-items: center;
  margin-top: 16px;
}
.splitLine {
  width: 2px;
  height: 24px;
  background-color: $grayc5;
  margin-left: 24px;
  margin-right: 24px;
}
.operateText {
  font-size: 28px;
  line-height: 36px;
  color: $C_111;
  font-family: PingFangSC-Medium;
  color: $C_111;
}
