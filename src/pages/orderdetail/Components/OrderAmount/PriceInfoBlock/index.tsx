/* eslint-disable react-native/no-color-literals */
import React, { memo } from 'react';
import {
  XView as View,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';
import getPixel from '@pages/utils/getPixel';
import Text from '@pages/components/Text';
import UnderText from '@pages/components/UnderText';
import NumberText from '@pages/components/NumberText';
import { noop } from '@pages/utils/util';
import Shark from '@pages/shark';
import Touchable from '@pages/components/Touchable';
import CurrencyFormatter from '@pages/components/CurrencyFormatter';
import c2xStyles from './index.module.scss';
import { ORDER_STATUS } from '../../../../../Constants/OrderDetail';
import { CarLog, Utils } from '../../../../../Util/Index';
import BookingTerms from '../../BookingTerms';
import DepositExplain from '../DepositExplain';
import Texts from '../../../Texts';
import UITestId from '../../../../../Constants/UITestID';

const { tc2AfterRental, againBack } = Texts;

const getDebate = info => {
  let price = 0;
  if (info && info.length > 0) {
    info.forEach(element => {
      if (element.isNeedDebate) {
        price = Number(Utils.Add(price, element.deductionAmount));
      }
    });
  }
  return price;
};

interface IPriceInfoBlock {
  isOsdNotHasFreeDepositData: boolean;
  orderBaseInfo: any;
  orderPriceInfo: any;
  onPressTandC: any;
  orderPrice: any;
  reqData: any;
  freeDeposit: any;
  showPriceDetail: () => void;
  showDepositDetailModal: (type) => void;
}

const PriceInfoBlock = memo(
  ({
    isOsdNotHasFreeDepositData,
    orderBaseInfo,
    orderPriceInfo,
    onPressTandC,
    orderPrice,
    reqData,
    freeDeposit,
    showPriceDetail = noop,
    showDepositDetailModal = noop,
  }: IPriceInfoBlock) => {
    const { orderStatus, distributionChannelId } = orderBaseInfo || {};

    const { orderId } = reqData || {};
    const { coupons } = orderPriceInfo || {};
    const getDebatePrice = getDebate(coupons);
    const {
      totalAmount,
      customerPayAmountOnArrival,
      localPayAmountOnArrival,
      customerCurrency = 'CNY',
      localCurrency = '',
      customerPayAmount,
    } = orderPrice || {};
    const {
      showFreeLabel,
      creaditRentTag,
      depositExplainV2,
      freeDepositTitle,
    } = freeDeposit || {};
    const rowCount =
      (freeDepositTitle ? 1 : 0) + (depositExplainV2?.length || 0);
    return (
      <XViewExposure
        testID={CarLog.LogExposure({ name: '曝光_订单详情页_费用模块' })}
        className={c2xStyles.feeDetailWrap}
      >
        <View
          testID={UITestId.car_testid_page_order_price_detail}
          className={c2xStyles.amountWrap}
        >
          <View style={c2xStyles.flexRowWrap}>
            <View className={c2xStyles.AmountSubTitle}>
              {/** 订单总额 */}
              {totalAmount >= 0 && (
                <View className={c2xStyles.totalPriceWrap}>
                  <Text fontWeight="bold" className={c2xStyles.totalPriceText}>
                    {Shark.getShark('key.cars.orderDetail.amount.totalPrice')}
                    &nbsp;
                  </Text>
                  <CurrencyFormatter
                    price={totalAmount}
                    style={{
                      color: '#006ff6',
                      fontSize: getPixel(36),
                      fontFamily: 'PingFangSC-Semibold',
                      lineHeight: getPixel(44),
                    }}
                    currency={customerCurrency}
                  />
                </View>
              )}

              {!!getDebatePrice && (
                <NumberText
                  fontWeight="normal"
                  className={c2xStyles.debatePrice}
                >
                  &nbsp;
                  {`${tc2AfterRental}${againBack}¥${getDebatePrice}`}
                </NumberText>
              )}
            </View>
          </View>
        </View>
        {/** 在线付 */}
        {customerPayAmount > 0 && (
          <View className={c2xStyles.onlinePayWrap}>
            <View className={c2xStyles.leftWrap}>
              <Text fontWeight="medium" className={c2xStyles.onlinePayText}>
                {Shark.getShark('key.cars.orderDetail.amount.onlinePay')}
              </Text>
            </View>
            <CurrencyFormatter
              price={customerPayAmount}
              style={{
                color: '#111111',
                fontSize: getPixel(28),
                fontFamily: 'PingFangSC-Regular',
                lineHeight: getPixel(36),
              }}
              currency={customerCurrency}
            />
          </View>
        )}
        {/** 到店付 */}
        {customerPayAmountOnArrival > 0 && (
          <View
            className={classNames(
              c2xStyles.poaPayWrap,
              !(customerPayAmount > 0) && c2xStyles.mt16,
            )}
          >
            <View className={c2xStyles.leftWrap}>
              <Text fontWeight="medium" className={c2xStyles.poaPayText}>
                {Shark.getShark('key.cars.orderDetail.amount.poaPay')}
              </Text>
            </View>
            <CurrencyFormatter
              price={customerPayAmountOnArrival}
              style={{
                color: '#111111',
                fontSize: getPixel(28),
                fontFamily: 'PingFangSC-Regular',
                lineHeight: getPixel(36),
              }}
              currency={customerCurrency}
            />
            {localPayAmountOnArrival > 0 && (
              <Text fontWeight="normal" className={c2xStyles.poaPayText}>
                {Shark.getShark('key.cars.orderDetail.amount.localPoaPay', [
                  localCurrency,
                  localPayAmountOnArrival,
                ])}
              </Text>
            )}
          </View>
        )}
        {/** 押金 */}
        <View>
          <View
            className={classNames(
              c2xStyles.depositWrap,
              !(customerPayAmount > 0) &&
                !(customerPayAmountOnArrival > 0) &&
                c2xStyles.mt16,
            )}
          >
            <View className={c2xStyles.leftWrap}>
              <Text fontWeight="medium" className={c2xStyles.depositText}>
                {Shark.getShark('key.cars.orderDetail.amount.deposit')}
              </Text>
              {showFreeLabel && (
                <View className={c2xStyles.depositLabelWrap}>
                  <Text
                    fontWeight="medium"
                    className={c2xStyles.depositLabelText}
                  >
                    {creaditRentTag}
                  </Text>
                </View>
              )}
            </View>
            {rowCount === 1 && (
              <DepositExplain
                explain={freeDepositTitle || depositExplainV2?.[0]?.explain}
              />
            )}
          </View>
          {rowCount > 1 && (
            <View className={c2xStyles.depositMoreWrap}>
              <DepositExplain explain={freeDepositTitle} isPoint={true} />
              {depositExplainV2?.map((item, index) => {
                return (
                  <DepositExplain
                    key={`depositExplainV2_${String(index)}`}
                    explain={item?.explain}
                    isPoint={true}
                  />
                );
              })}
            </View>
          )}
        </View>

        {/** 费用明细、押金明细操作按钮 */}
        <View className={c2xStyles.operateWrap}>
          <UnderText
            testID="ta-feeDetail"
            onClick={showPriceDetail}
            fontWeight="medium"
            className={c2xStyles.operateText}
          >
            {Shark.getShark('key.cars.orderDetail.amount.priceDetail')}
          </UnderText>

          {!isOsdNotHasFreeDepositData && (
            <>
              <View className={c2xStyles.splitLine} />
              <Touchable
                testID="ta-depositDetail"
                onClick={showDepositDetailModal}
              >
                <View
                  testID={CarLog.LogExposure({
                    name: '曝光_订单详情页_押金模块',
                    info: {
                      orderId,
                      orderStatus,
                    },
                  })}
                >
                  <UnderText
                    fontWeight="medium"
                    className={c2xStyles.operateText}
                  >
                    {Shark.getShark(
                      'key.cars.orderDetail.amount.depositDetail',
                    )}
                  </UnderText>
                </View>
              </Touchable>
            </>
          )}
        </View>
        {orderStatus === ORDER_STATUS.Unsubmitted &&
          distributionChannelId === 17120 && (
            <BookingTerms onPressTandC={onPressTandC} />
          )}
        <View className={c2xStyles.line} />
      </XViewExposure>
    );
  },
);

export default PriceInfoBlock;
