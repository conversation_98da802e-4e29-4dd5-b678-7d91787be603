import React, { memo } from 'react';
import orderDetailStore, {
  getOrderDetailDepositInfo,
} from '@pages/orderdetail/state/orderdetail';
import sesameStore from '@pages/orderdetail/state/sesame';
import OrderAmount from './OrderAmount';
import { AppContext } from '../../../../Util/Index';

interface IAmount {
  showOsdPriceDetailModal: () => void;
  headerContent: () => void;
  onPressTandC: (policySelectedId, labName) => void;
  handleRefundLayout: (e) => void;
  logBaseInfo?: any;
}

const Amount = memo(
  ({
    showOsdPriceDetailModal,
    headerContent,
    onPressTandC,
    handleRefundLayout,
    logBaseInfo,
  }: IAmount) => {
    const orderPriceInfo = orderDetailStore(state => state.orderPriceInfo);
    const orderPrice = orderDetailStore(state => state.orderPrice);
    const orderBaseInfo = orderDetailStore(state => state.orderBaseInfo);
    const cancelRuleInfo = orderDetailStore(state => state.cancelRuleInfo);
    const pickupStore = orderDetailStore(state => state.pickupStore);
    const returnStore = orderDetailStore(state => state.returnStore);
    const reqOrderParams = orderDetailStore(state => state.reqOrderParams);
    const reqData = reqOrderParams || { orderId: AppContext.UrlQuery?.orderId };
    const orderRenewalEntry = orderDetailStore(
      state => state.orderRenewalEntry,
    );
    const authStatus = sesameStore(state => state.authStatus);
    const userName = sesameStore(state => state.userName);
    const freeDeposit = orderDetailStore(state => state.freeDeposit);
    const freeDepositInfo = getOrderDetailDepositInfo(
      authStatus,
      freeDeposit,
      userName,
      orderBaseInfo,
    );
    const addPayments = orderDetailStore(state => state.additionPaymentInfo);
    const finalQueryIsFinish = orderDetailStore(
      state => state.queryOrderAllDataSuccess,
    );
    const { fetchOrder2, setDepositDetailModalVisible, setOrderModalsVisible } =
      orderDetailStore.getState();
    return (
      <OrderAmount
        showOsdPriceDetailModal={showOsdPriceDetailModal}
        headerContent={headerContent}
        onPressTandC={onPressTandC}
        handleRefundLayout={handleRefundLayout}
        orderPriceInfo={orderPriceInfo}
        orderPrice={orderPrice}
        orderBaseInfo={orderBaseInfo}
        cancelRuleInfo={cancelRuleInfo}
        pickupStore={pickupStore}
        returnStore={returnStore}
        reqData={reqData}
        orderRenewalEntry={orderRenewalEntry}
        freeDeposit={freeDepositInfo}
        addPayments={addPayments}
        finalQueryIsFinish={finalQueryIsFinish}
        fetchOrder={fetchOrder2}
        setDepositDetailModalVisible={setDepositDetailModalVisible}
        setOrderModalsVisible={setOrderModalsVisible}
        logBaseInfo={logBaseInfo}
      />
    );
  },
);

export default Amount;
