@import '../../../../../Common/src/Tokens/tokens/color.scss';

.summaryPolicies {
  font-size: 28px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_111111;
  margin-top: 16px;
}
.wrap {
  padding-top: 32px;
  padding-bottom: 32px;
  border-top-width: 1px;
  border-top-color: $grayBorder;
}
.headerWrap {
  flex-direction: row;
  justify-content: space-between;
}
.crossTypeName {
  font-size: 32px;
  line-height: 40px;
  font-family: PingFangSC-Medium;
  color: $C_111111;
}
.editIcon {
  color: $C_111111;
  font-size: 28px;
  line-height: 36px;
  margin-top: 16px;
}
.wrapFirst {
  border-top-width: 0;
}
.flexRow {
  flex-direction: row;
  align-items: center;
}
.rightArrow {
  margin-top: 16px;
}
