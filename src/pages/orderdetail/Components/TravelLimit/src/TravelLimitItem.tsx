/* eslint-disable import/no-extraneous-dependencies */
import {
  map as lodashMap,
  find as lodashFind,
  groupBy as lodashGroupBy,
  transform as lodashTransform,
} from 'lodash-es';
import React, { memo, useMemo, useCallback } from 'react';
import { XView as View, xClassNames } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import Touchable from '@pages/components/Touchable';
import shark from '@pages/shark';
import UnderText from '@pages/components/UnderText';
import c2xStyles from './travelLimitItemC2xStyles.module.scss';
import { CarLog } from '../../../../../Util/Index';
import { ITravelLimitItem, ICrossType } from './Types';
import SelectedResult from './SelectedResult';
import { UITestID } from '../../../../../Constants/Index';

const EmptyPolicy = ['null', 'undefined'];

// 旅行限制选择结果返显数据
export const getTravelLimitSelectedResult = (
  selectedResult,
  locations = [],
) => {
  const bbkLocations = lodashMap(selectedResult, item => {
    const locationItem = lodashFind(locations, {
      regionId: item?.regionId || item?.code,
    });
    return locationItem;
  })?.filter(item => !!item);

  if (!bbkLocations?.length) {
    return [];
  }

  // 对所有的政策, 按照跨境状态&政策是否相同进行合并
  const groupByStatusName = lodashGroupBy(
    bbkLocations,
    location => location?.statusName,
  );

  // 对分组后的政策，按照政策进行分组
  const groupByPolicy = lodashMap(groupByStatusName, policyItems => {
    const policyList = lodashGroupBy(policyItems, item => item?.policy);
    return {
      [policyItems?.[0]?.statusName]: policyList,
    };
  });

  const selectedResultData = groupByPolicy?.map(item => {
    const statusNameGroup = Object.keys(item)?.[0];
    const policyList = item[statusNameGroup];
    const policyListKey = Object.keys(policyList);
    // 处理逻辑和CrossPlace组件一致
    return {
      title: statusNameGroup,
      details: policyListKey?.map(policyKey => {
        const placeStr = lodashTransform(
          policyList[policyKey],
          (result: any[], n: { name: any }) => {
            result.push(n?.name);
          },
          [],
        );
        const locationsStr = placeStr?.join('，');
        let note = '';
        if (!!policyKey && !EmptyPolicy?.includes(policyKey)) {
          note = policyKey;
        }
        return {
          locations: locationsStr,
          note,
        };
      }),
    };
  });
  return selectedResultData;
};

const TravelLimitItem: React.FC<ITravelLimitItem> = memo(
  ({
    crossType,
    crossTypeName,
    summaryTitle,
    summaryPolicies,
    locations,
    selectedResult,
    isFirst,
    logBaseInfo,
    onSelectCountry,
  }: ITravelLimitItem) => {
    const isShowEditIcon = useMemo(
      () => crossType === ICrossType.CrossCountry && selectedResult?.length > 0,
      [crossType, selectedResult],
    );

    const isShowRightArrow = useMemo(
      () => crossType === ICrossType.CrossCountry && locations?.length > 0,
      [crossType, locations],
    );

    const subContent = useMemo(() => {
      if (crossType === ICrossType.CrossCountry && selectedResult?.length > 0) {
        return (
          <SelectedResult
            data={getTravelLimitSelectedResult(selectedResult, locations)}
          />
        );
      }
      let iSummaryTitle = summaryTitle;
      if (summaryTitle) {
        if (summaryPolicies?.[0]) {
          iSummaryTitle = `${summaryTitle}：${summaryPolicies?.[0]}`;
        }
      } else if (summaryPolicies?.[0]) {
        iSummaryTitle = summaryPolicies?.[0];
      }
      return (
        <>
          {!!iSummaryTitle && (
            <Text className={c2xStyles.summaryPolicies}>{iSummaryTitle}</Text>
          )}
        </>
      );
    }, [crossType, selectedResult, locations, summaryTitle, summaryPolicies]);

    const isDisabled = useMemo(
      () => crossType !== ICrossType.CrossCountry || !locations?.length,
      [crossType, locations],
    );

    const handlePress = useCallback(() => {
      CarLog.LogCode({
        name: '点击_产品详情页_跨境跨岛政策',

        info: {
          ...logBaseInfo,
          crossType: crossTypeName,
        },
      });
      if (
        crossType === ICrossType.CrossCountry &&
        locations?.length > 0 &&
        onSelectCountry
      ) {
        onSelectCountry();
      }
    }, [crossType, logBaseInfo, locations, crossTypeName, onSelectCountry]);

    return (
      <Touchable
        testID={`${UITestID.car_testid_travellimit_item}_${crossTypeName}`}
        debounce={true}
        onClick={handlePress}
        debounceTime={300}
        disabled={isDisabled}
      >
        <View
          testID={`${UITestID.c_testid_travelLimitItem}_${crossTypeName}`}
          className={xClassNames(
            c2xStyles.wrap,
            isFirst && c2xStyles.wrapFirst,
          )}
        >
          {!!crossTypeName && (
            <>
              <View className={c2xStyles.headerWrap}>
                <Text className={c2xStyles.crossTypeName} fontWeight="medium">
                  {crossTypeName}
                </Text>
              </View>
              {isShowRightArrow && (
                <UnderText fontWeight="medium" className={c2xStyles.editIcon}>
                  {isShowEditIcon
                    ? shark.getShark('key.cars.order.modifyCrossBorder')
                    : shark.getShark('key.cars.order.selectCrossBorder')}
                </UnderText>
              )}
            </>
          )}
          {subContent}
        </View>
      </Touchable>
    );
  },
);

export default TravelLimitItem;
