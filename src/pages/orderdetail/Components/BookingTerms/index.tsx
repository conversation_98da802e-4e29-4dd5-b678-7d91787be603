/* eslint-disable import/no-extraneous-dependencies */
import { debounce as lodashDebounce } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useCallback } from 'react';
import { XView as View, xMergeStyles, xRouter } from '@ctrip/xtaro';

import TermsBar from '@ctrip/rn_com_car/dist/src/Components/Basic/TermsBar';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import { font, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { useCurrentLocale } from '@ctrip/rn_com_car/dist/src/Shark/src/Index';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { CarLog } from '../../../../Util/Index';
import { Platform } from '../../../../Constants/Index';
import { IBookingTerms } from '../../Types';

const CNContent = '点击继续支付表示已阅读并同意 预订条款';
const CNKeyword = '预订条款';
const SPECIAL_LOCAL = [
  {
    locale: 'ko_KR',
    url: 'https://pages.trip.com/service-guideline/terms-ko-kr.html',
  },
  {
    locale: 'ja_JP',
    url: 'https://pages.trip.com/service-guideline/terms-ja-jp.html',
  },
];

const { getPixel, autoProtocol, getLineHeight } = BbkUtils;
const styles = StyleSheet.create({
  termsTextStyle: {
    lineHeight: getLineHeight(36),
    ...font.replenishSubTitle,
    color: color.blueGrayIcon,
  },
  termsHighLightStyle: {
    lineHeight: getLineHeight(36),
    ...font.replenishSubTitle,
    textDecorationLine: 'none',
  },
  Mt6: {
    marginTop: getPixel(6),
  },
});

const BookingTerms: React.FC<IBookingTerms> = ({ onPressTandC }) => {
  const locales = useCurrentLocale();

  const handlePressTandC = useCallback(() => {
    CarLog.LogCode({ name: '点击_填写页_条款细则' });
    const localeItem =
      locales && SPECIAL_LOCAL.find(v => v.locale === locales.locale);
    if (localeItem && localeItem.url) {
      xRouter.navigateTo({ url: ocaleItem.url });
      return;
    }
    onPressTandC();
  }, [locales]);

  const onPressPolicy = useCallback(() => {
    CarLog.LogCode({ name: '点击_填写页_隐私条款' });
    if (!locales) return;
    const policyUrl = `https://pages.trip.com/service-guideline/privacy-policy-${locales.locale.replace(
      '_',
      '-',
    )}.html`;
    xRouter.navigateTo({ url: policyUrl });
  }, [locales]);

  const handlePressPrecondition = useCallback(
    lodashDebounce(
      () => {
        const { Agreement } = Platform.CAR_CROSS_URL;
        const forwardurl = autoProtocol(Agreement.OSD);
        xRouter.navigateTo({ url: forwardurl });

        CarLog.LogCode({ name: '点击_填写页_预订条款' });
      },
      100,
      {
        leading: true,
        trailing: false,
      },
    ),
    [],
  );

  return (
    <View
      style={xMergeStyles([
        { flexDirection: 'row', alignItems: 'center' },
        styles.Mt6,
      ])}
    >
      <TermsBar
        isChecked={true}
        hasCheckbox={false}
        text={[CNContent]}
        keywords={[CNKeyword]}
        funcObj={{
          条款约定: handlePressTandC,
          隐私政策: onPressPolicy,
          [CNKeyword]: handlePressPrecondition,
        }}
        highLightStyle={styles.termsHighLightStyle}
        textStyle={styles.termsTextStyle}
      />
    </View>
  );
};

export default withTheme(BookingTerms);
