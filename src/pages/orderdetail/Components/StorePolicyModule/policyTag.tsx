import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';
import Touchable from '@pages/components/Touchable';
import Text from '@pages/components/Text';
import Styles from './policyTag.module.scss';

export interface PropsType {
  labName: string;
  id: string;
  onDetailPress?: (id: string, labName?: string) => {};
}

const PolicyTag = (props: PropsType) => {
  const { onDetailPress, id, labName } = props;
  return (
    <Touchable debounceTime={300} onClick={() => onDetailPress?.(id, labName)}>
      <View className={Styles.policyItem} testID="ta-storePolicy">
        <Text className={Styles.policyTag}>{labName}</Text>
      </View>
    </Touchable>
  );
};
export default memo(PolicyTag);
