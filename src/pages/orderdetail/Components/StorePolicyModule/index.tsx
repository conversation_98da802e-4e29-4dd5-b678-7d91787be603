import React, { useCallback } from 'react';
import { XView as View } from '@ctrip/xtaro';
import Text from '@pages/components/Text';
import Touchable from '@pages/components/Touchable';
import { PolicyPressType } from '@pages/orderdetail/enum';
import shark from '@pages/shark';
import Styles from './index.module.scss';
import PolicyTag from './policyTag';

interface PropsType {
  lables: Array<string>;
  onDetailPress?: (id: string, name?: string) => void;
}

const StorePolicy: React.FC<PropsType> = props => {
  const { lables, onDetailPress } = props;

  const getPolicyTagDom = () => {
    if (lables.length === 0) return null;
    const policyTagDom = [];
    lables.map((item, index) => {
      // @ts-ignore
      const props = { ...item, onDetailPress };
      // @ts-ignore 升级072
      policyTagDom.push(<PolicyTag key={index} {...props} />);
    });
    return policyTagDom;
  };

  const onPress = useCallback(() => {
    onDetailPress?.(PolicyPressType.All, PolicyPressType.All);
  }, [onDetailPress]);

  const policyTagDom = getPolicyTagDom();
  const isShowPolicies = policyTagDom && policyTagDom.length > 0;

  if (!isShowPolicies) return null;

  return (
    <Touchable debounceTime={300} onClick={onPress}>
      <Text className={Styles.title} fontWeight="bold">
        {shark.getShark('key.cars.order.storePolicy')}
      </Text>
      <View className={Styles.policyContainer}>{policyTagDom}</View>
    </Touchable>
  );
};

export default StorePolicy;
