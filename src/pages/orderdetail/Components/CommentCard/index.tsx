import Image from '@c2x/components/Image';
import React, { useCallback, useState, memo } from 'react';
import {
  xRouter,
  XView as View,
  XViewExposure,
  XLinearGradient as LinearGradient,
} from '@ctrip/xtaro';
import orderDetailStore, {
  getCommentButtonInfo,
} from '@pages/orderdetail/state/orderdetail';
import Touchable from '@pages/components/Touchable';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Text from '@pages/components/Text';
import shark from '@pages/shark';
import c2xStyles from './commentCardC2xStyles.module.scss';
import { CarLog } from '../../../../Util/Index';
import { ImageUrl, UITestID } from '../../../../Constants/Index';

const { getPixel } = BbkUtils;

const scorePositionTop = {
  1: 250,
  2: 200,
  3: 150,
  4: 100,
  5: 50,
};
interface CommentIconProps {
  score: number;
  curSelectScore?: number;
  clickScorePress: (score: number) => void;
}

const CommentIcon = memo(
  ({ score, curSelectScore, clickScorePress }: CommentIconProps) => {
    const handleClickScore = useCallback(() => {
      clickScorePress(score);
    }, [clickScorePress, score]);
    return (
      <Touchable
        testID={UITestID.car_testid_page_order_status_commentcard_icon}
        onClick={handleClickScore}
        className={c2xStyles.iconImg}
      >
        <Image
          src={`${ImageUrl.DIMG04_PATH}1tg3312000l964d5173DF.png`}
          mode="scaleToFill"
          className={c2xStyles.bgImg}
          style={
            score <= curSelectScore && {
              top: -getPixel(scorePositionTop[curSelectScore]) ?? 0,
            }
          }
        />
      </Touchable>
    );
  },
);

const CommentCard = () => {
  const orderBaseInfo = orderDetailStore(state => state.orderBaseInfo);
  const operation = orderDetailStore(state => state.operation);
  const commentButtonInfo = getCommentButtonInfo(operation);
  const orderId = orderDetailStore(state => state.reqOrderParams?.orderId);
  const orderStatusInfo = orderBaseInfo?.orderStatus;
  const { orderStatus } = orderStatusInfo || {};
  const { label, url, disableCode } = commentButtonInfo || {};
  const [curSelectScore, setCurSelectScore] = useState(0);
  const handleClickScore = useCallback(
    scoreNum => {
      setCurSelectScore(scoreNum);
      xRouter.navigateTo({ url: `${url}&score=${scoreNum}` });
      CarLog.LogCode({
        name: '点击_订单详情页_点评卡片',
        info: { orderId, orderStatus, commentScore: scoreNum },
      });
    },
    [orderId, orderStatus, url],
  );
  const handleGoToComment = useCallback(() => {
    xRouter.navigateTo({ url });
    CarLog.LogCode({
      name: '点击_订单详情页_点评卡片',
      info: { orderId, orderStatus, commentScore: 0 },
    });
  }, [orderId, orderStatus, url]);
  if (disableCode !== 1) {
    return null;
  }
  return (
    <XViewExposure
      testID={CarLog.LogExposure({
        name: '曝光_订单详情页_点评卡片',
        info: { orderId, orderStatus },
      })}
      className={c2xStyles.wrapper}
    >
      <LinearGradient
        start={{ x: 1.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.5 }}
        locations={[0, 1]}
        className={c2xStyles.wrap}
        colors={['rgb(238, 246, 255)', 'rgb(255, 255, 255)']}
      >
        <Touchable
          testID={UITestID.car_testid_page_order_status_commentcard}
          onClick={handleGoToComment}
        >
          <View className={c2xStyles.titleWrap}>
            <View className={c2xStyles.titleWrapLeft}>
              <Image
                src={`${ImageUrl.DIMG04_PATH}1tg2912000l96h8izCEAC.png`}
                className={c2xStyles.logoImg}
              />
              <Text className={c2xStyles.titleText} fontWeight="bold">
                {shark.getShark('key.cars.order.earnPointsReview')}
              </Text>
            </View>

            <Text className={c2xStyles.subTitleText} fontWeight="medium">
              {label}
            </Text>
          </View>
          <View className={c2xStyles.scoreWrap}>
            <Text className={c2xStyles.scoreText}>
              {shark.getShark('key.cars.order.veryPoor')}
            </Text>
            <View className={c2xStyles.scoreItemWrap} testID="ta-review">
              {[1, 2, 3, 4, 5].map(item => {
                return (
                  <CommentIcon
                    score={item}
                    key={item}
                    curSelectScore={curSelectScore}
                    clickScorePress={handleClickScore}
                  />
                );
              })}
            </View>
            <Text className={c2xStyles.scoreText}>
              {shark.getShark('key.cars.order.verySatisfied')}
            </Text>
          </View>
        </Touchable>
      </LinearGradient>
    </XViewExposure>
  );
};
CommentCard.defaultProps = { style: {} };
export default CommentCard;
