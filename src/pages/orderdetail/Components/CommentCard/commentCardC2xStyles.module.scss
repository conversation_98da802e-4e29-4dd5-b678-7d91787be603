@import '../../../scss/color.scss';

.wrapper {
  margin-bottom: 16px;
}
.wrap {
  background-color: $white;
  padding-top: 37px;
  padding-bottom: 42px;
  padding-left: 36px;
  padding-right: 32px;
}

.iconImg {
  width: 50px;
  height: 50px;
  margin-right: 24px;
  overflow: hidden;
}
.bgImg {
  width: 50px;
  height: 350px;
  position: absolute;
  top: 0px;
}
.titleWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.titleWrapLeft {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.titleText {
  color: $C_111;
  font-size: 36px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
}
.subTitleText {
  color: $C_ff5500;
  font-size: 26px;
  line-height: 36px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
}
.logoImg {
  width: 40px;
  height: 40px;
  margin-right: 8px;
}
.scoreWrap {
  margin-top: 30px;
  border-radius: 50px;
  height: 87px;
  padding-left: 58px;
  padding-right: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border: 1px solid $C_e5e5e5;
}
.scoreText {
  color: $C_111;
  font-size: 28px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-top: 3px;
}
.scoreItemWrap {
  flex: 1;
  margin-left: 52px;
  // margin-right: 56px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
