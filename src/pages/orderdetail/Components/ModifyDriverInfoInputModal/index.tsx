import StyleSheet from '@c2x/apis/StyleSheet';
import Keyboard from '@c2x/apis/Keyboard';
import React, { useState, useEffect } from 'react';
import { xMergeStyles, XView as View } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';

import BbkLayer from '@ctrip/rn_com_car/dist/src/Components/Basic/Layer';
import Button from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import MobileInput from '@ctrip/rn_com_car/dist/src/Components/Basic/MobileInput';
import {
  ensureFunctionCall,
  useMemoizedFn,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { KeyboardAwareScrollView } from '@c2x/extraPackages/react-native-keyboard-aware-scroll-view';
import Input from '@ctrip/rn_com_car/dist/src/Components/Basic/Input';
import c2xStyles from './modifyDriverInfoInputModalC2xStyles.module.scss';
import { UITestID } from '../../../../Constants/Index';
import { ContactType, ModifyDriverInfoType } from '../../enum';
import { LocalContactInfoType } from '../../Types';
import { asMobileList } from '../../../../Constants/OrderDetail';
import AsMobileBlock from '../AsMobileBlock';
import { InputFormatType } from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';

const { getPixel, fixIOSOffsetBottom, isIos } = BbkUtils;
const styles = StyleSheet.create({
  searchBtn: {
    height: getPixel(88),
    lineHeight: getPixel(88),
    borderRadius: getPixel(12),
    backgroundColor: color.C_006FF6,
  },
  deductInf: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderColor: color.grayBorder,
    height: getPixel(339),
  },
  deductInput: {
    marginTop: 0,
  },
  btnTxt: {
    marginTop: getPixel(-2),
    ...font.title1LightStyle,
    lineHeight: getPixel(48),
    height: getPixel(48),
  },
  pb32: {
    paddingBottom: fixIOSOffsetBottom(isIos ? 0 : 32),
  },
  mt8: {
    marginTop: getPixel(8),
  },
  mb80: { marginBottom: getPixel(80) },
});

interface FProps {
  visible: boolean;
  title: string;
  iptTitle: string;
  buttonTxt: string;
  flightIptValue?: any;
  iptPlaceholder: string;
  keyboardType?: string;
  onRequestClose: (args) => void;
  onSubmit: (isCheck?: boolean) => void;
  onChangeFlightNo?: (args) => void;
  onChangeLocalContactInfo?: (args) => void;
  type?: string;
  onSelectContactType?: (type) => void;
  localContactInfo?: LocalContactInfoType;
  mobile?: string;
  pickUpAreaCode?: string;
  errorMsg?: string;
  clearFlightErrorMsg?: () => void;
}

const ModifyDriverInfoInputModal = ({
  visible,
  title,
  iptTitle,
  iptPlaceholder,
  onSubmit,
  onChangeFlightNo,
  onChangeLocalContactInfo,
  flightIptValue,
  buttonTxt,
  onRequestClose,
  keyboardType,
  type,
  onSelectContactType,
  localContactInfo,
  mobile,
  pickUpAreaCode,
  errorMsg: errorMsgProp,
  clearFlightErrorMsg,
}: FProps) => {
  const [isActive, setIsActive] = useState(false);
  const [isFlightActive, setIsFlightActive] = useState(false);
  const [errorMsg, setErrorMsg] = useState(errorMsgProp);
  const asMobileNumber = mobile === localContactInfo?.data ? '' : mobile;
  const onPressAreaCode = useMemoizedFn(() => {
    ensureFunctionCall(onSelectContactType(type));
    Keyboard.dismiss();
  });
  const localContactInputFocus = useMemoizedFn(() => {
    setIsActive(true);
  });
  const flightNoInputFocus = useMemoizedFn(() => {
    setIsFlightActive(true);
    clearFlightErrorMsg();
  });

  const localContactInputBlur = useMemoizedFn(() => {
    setIsActive(false);
  });
  const flightNoInputBlur = useMemoizedFn(() => {
    setIsFlightActive(false);
    ensureFunctionCall(onSubmit(true));
  });
  const setAsMobile = useMemoizedFn(() => {
    ensureFunctionCall(onChangeLocalContactInfo(asMobileNumber));
    Keyboard.dismiss();
  });
  const handleSubmit = useMemoizedFn(() => {
    if (!errorMsg) {
      onSubmit();
    }
    Keyboard.dismiss();
  });
  const getContactInputTitle = useMemoizedFn(() => {
    if (localContactInfo?.contactType === ContactType.localPhone) {
      return '当地电话';
    }
    return '账号';
  });
  useEffect(() => {
    setErrorMsg(errorMsgProp);
  }, [errorMsgProp]);
  const inputLeftTitle =
    localContactInfo?.contactType === ContactType.localPhone
      ? `+${pickUpAreaCode}`
      : localContactInfo?.title;
  const showAsMobile =
    isIos &&
    !!isActive &&
    !!asMobileNumber &&
    asMobileList.includes(localContactInfo?.contactType);
  return (
    <BbkLayer
      modalVisible={visible}
      onRequestClose={() => {
        onRequestClose({ visible: false });
      }}
      doneTestID={UITestID.car_testid_page_order_layerwithbutton_done}
      closeModalBtnTestID={
        UITestID.car_testid_page_order_layerwithbutton_closemask
      }
      title={title}
      hideFtooterBtn={true}
      contentStyle={styles.pb32}
      hasHeaderBorder={false}
      needListenerkeyboard={true}
    >
      <KeyboardAwareScrollView
        enableAutomaticScroll={false}
        keyboardDismissMode="on-drag"
        keyboardShouldPersistTaps="handled"
        keyboardOpeningTime={0}
      >
        <View style={xMergeStyles([styles.deductInf])}>
          {type === ModifyDriverInfoType.flightNo ? (
            <View className={c2xStyles.pt40}>
              {!!isFlightActive && (
                <Text className={c2xStyles.deductBri}>{iptTitle}</Text>
              )}
              <Input
                value={flightIptValue}
                underlineColorAndroid="transparent"
                clearButtonMode="never"
                onChangeText={onChangeFlightNo}
                placeholder="航班号（选填）"
                defaultPlaceholder={iptPlaceholder}
                placeholderTextColor={color.guideStepSequenceColor}
                testID={UITestID.car_testid_page_order_layerwithbutton_input}
                // @ts-ignore
                keyboardType={keyboardType}
                formatType={InputFormatType.flight}
                error={!!errorMsg}
                errorTip={errorMsg}
                style={styles.deductInput}
                onInputFocus={flightNoInputFocus}
                onInputBlur={flightNoInputBlur}
              />
            </View>
          ) : (
            <MobileInput
              title={getContactInputTitle()}
              value={localContactInfo?.data}
              placeholder="便于门店与您联系"
              defaultPlaceholder={getContactInputTitle()}
              onChangeText={onChangeLocalContactInfo}
              spaceLength={0}
              areaCode={inputLeftTitle}
              onInputBlur={localContactInputBlur}
              onInputFocus={localContactInputFocus}
              onPressAreaCode={onPressAreaCode}
              keyboardType={keyboardType}
              style={styles.mt8}
            />
          )}
        </View>
      </KeyboardAwareScrollView>
      <Button
        onPress={handleSubmit}
        text={buttonTxt}
        colorType="blue"
        buttonStyle={xMergeStyles([
          styles.searchBtn,
          showAsMobile && styles.mb80,
        ])}
        textStyle={styles.btnTxt}
        testID={UITestID.car_testid_page_order_layerwithbutton_button}
      />

      {!!showAsMobile && (
        <AsMobileBlock setAsMobile={setAsMobile} mobile={asMobileNumber} />
      )}
    </BbkLayer>
  );
};

export default withTheme(ModifyDriverInfoInputModal);
