import React, { memo } from 'react';
import {
  XView as View,
  xMergeStyles,
  XViewExposure,
  xClassNames,
} from '@ctrip/xtaro';
import Touchable from '@pages/components/Touchable';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Text from '@pages/components/Text';
import Icon from '@pages/components/Icon';
import orderDetailStore from '@pages/orderdetail/state/orderdetail';
import useMemoizedFn from '@pages/hooks';
import c2xStyles from './storeMessageEntryC2xStyles.module.scss';
import Taro from '../../../../xTaroApi';
import { CarLog, Utils } from '../../../../Util/Index';

const { getPixel } = BbkUtils;

const StoreMessageEntry: React.FC = memo(() => {
  const data: any = orderDetailStore(state => state.didNoticeData);
  const orderId = orderDetailStore(state => state.reqOrderParams?.orderId);
  const orderStatus = orderDetailStore(
    state => state.orderBaseInfo?.orderStatus,
  );

  const { noticeTitle, noticeList } = data || {};
  const onPress = useMemoizedFn(() => {
    const params = {
      orderId,
      orderStatus: orderStatus?.orderStatus,
      title: noticeTitle,
      pageType: 'storeMessage',
    };
    CarLog.LogCode({
      name: '点击_DID入口_门店消息',
    });
    Taro.navigateTo({
      url: `/pages/order/storeMessages/index?${Utils.toParams(
        params,
        false,
        true,
      )}`,
    });
  });
  if (!noticeList?.length) {
    return null;
  }
  return (
    <XViewExposure
      testID={CarLog.LogExposure({
        name: '曝光_DID入口_门店消息',
      })}
    >
      <Touchable
        testID="ta-storeInfo"
        onClick={onPress}
        debounceTime={300}
        className={c2xStyles.wrap}
      >
        <View
          className={xClassNames(c2xStyles.content, c2xStyles.contentFlexStart)}
        >
          {!!noticeTitle && (
            <Text
              className={c2xStyles.title}
              style={xMergeStyles(
                noticeList.length > 2 && { marginBottom: getPixel(8) },
              )}
              fontWeight="medium"
            >
              {noticeTitle}
            </Text>
          )}

          <View className={c2xStyles.content}>
            {noticeList.map(
              (item, index) =>
                !!item.typeName && (
                  <View
                    key={`noticeListItem_${String(index)}`}
                    className={xClassNames(
                      c2xStyles.listItem,
                      index > 0 && c2xStyles.ml24,
                    )}
                  >
                    <View className={c2xStyles.listPoint} />
                    <Text
                      numberOfLines={1}
                      fontWeight="normal"
                      className={c2xStyles.listText}
                    >
                      {item.text}
                    </Text>
                  </View>
                ),
            )}
          </View>
        </View>
        <Icon className={c2xStyles.rightIcon}>&#xe944;</Icon>
      </Touchable>
    </XViewExposure>
  );
});
StoreMessageEntry.defaultProps = { data: {} };

export default StoreMessageEntry;
