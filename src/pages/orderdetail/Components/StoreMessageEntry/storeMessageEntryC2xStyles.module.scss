@import '../../../scss/color.scss';

.wrap {
  margin-top: 24px;
  padding: 28px 24px;
  background-color: $white;
  border-radius: 16px;
  flex-direction: row;
  align-items: center;
}
.wrapBg {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 188px;
  height: 54px;
  border-radius: 16px;
  z-index: 99;
}
.title {
  font-size: 28px;
  font-family: PingFangSC-Medium;
  margin-right: 16px;
  line-height: 36px;
  color: $C_111;
}
.content {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  flex: 1;
  margin-right: 24px;
}
.listItem {
  color: $black;
  flex-direction: row;
  align-items: center;
}
.ml24 {
  margin-left: 24px;
}
.listPoint {
  width: 8px;
  height: 8px;
  background-color: $C_FF7529;
  border-radius: 5px;
  margin-right: 8px;
}
.listText {
  color: $C_555555;
  font-size: 24px;
  font-family: PingFangSC-Regular;
  line-height: 32px;
}
.rightIcon {
  font-size: 36px;
}
.content {
  flex: 1;
  flex-direction: row;
  align-items: center;
}
