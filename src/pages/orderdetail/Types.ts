import ModalProps from '@c2x/components/Modal/types';
import { CSSProperties } from 'react';
import { LayoutChangeEvent } from 'react-native';
import { PointsType } from '@ctrip/rn_com_car/dist/src/Logic/src/Member/Types/MemberShipRights';
import {
  CustomerPhoneModalType,
  ILockStatus,
  OrderStatusCtrip,
} from '../../Constants/OrderDetail';
import { QueryOsdModifyOrderNoteResponseType } from '../../Types/Dto/QueryOsdModifyOrderNoteRequestAndResponseType';
import { AlbumType } from '../../Types/Dto/QueryVehicleDetailListResponseType';
import { EarlyReturnRecord } from '../../Types/Dto/QueryVehicleDetailInfoResponseType';
import { AreaInfo } from '../../Types/Dto/GetAreaListType';
import { CityInfo } from '../../Types/Dto/GetCityListType';
import * as OrderDetailCons from '../../Constants/OrderDetail';
import {
  DetailsItemLabelColorCode,
  PageRole,
  InsuranceStatus,
  EasylifeType,
  ModifyOrderAllOperationsCodeType,
  OrderStatusTextLabelType,
  IDelayStatus,
  ContactType,
  cardType,
  PassPortIdtype,
  IGuidInfoType,
  ContentType,
} from './enum';

export interface AreaHistoryType {
  cityId: string;
  cityInfo: CityInfo;
  locationInfo: AreaInfo[];
}

export interface LocalContactInfoType {
  logo?: string;
  title?: string;
  contactType: ContactType;
  isSelected: boolean;
  data: string;
  icon?: string;
  type?: number;
  isMask?: boolean;
}

export interface PropsType {
  type?: string;
  btnText?: string;
  secondBtnText?: string;
  verifyNowContent?: {
    iconTitle?: string;
    boldContent?: string;
  };
  verifyOutOfDateContent?: {
    contents?: Array<{
      type: string;
      content?: string;
    }>;
    boldContent?: string;
  };
  // 信用租通用结构
  verifyRes?: {
    boldTitle?: string;
    contents?: Array<{
      type: ContentType;
      content?: string;
    }>;
    subTitle?: string;
  };
  verifySuccess?: {
    boldTitle?: string;
    contents?: Array<{
      type: string;
      content?: string;
      leftText?: string;
      labelText?: string;
      rightText?: string;
    }>;
  };
  verifySuccessIsd?: {
    boldTitle?: string;
    contents?: Array<{
      type: string;
      nameText?: string;
      content?: string;
      labels?: Array<{ labelText?: string }>;
    }>;
  };
  verifyFailure?: {
    boldTitle?: string;
    contents?: Array<{
      type: ContentType;
      content?: string;
    }>;
  };
  verifyRealName?: {
    boldContent?: string;
    boldContent2?: string;
    contents?: Array<{
      type: string;
      content?: string;
    }>;
  };
  verifyFailSesameSuccessTrip?: {
    boldTitle?: string;
    contents?: Array<{
      type: string;
      content?: string;
    }>;
  };
  verifyFailNetworkError?: {
    boldTitle?: string;
    contents?: Array<{
      type: string;
      content?: string;
    }>;
  };
  verifyChangeDriver?: {
    boldTitle?: string;
    contents?: Array<{
      type?: string;
      content?: string;
      leftText?: string;
      nameText?: string;
      rightText?: string;
    }>;
  };
  verifyUnder18?: {
    boldTitle?: string;
    contents?: Array<{
      type?: string;
      content?: string;
    }>;
  };
  warnings?: string[];
  tips?: string[];
  footers?: string[];
  onPress?: () => void;
  onClose?: () => void;
  onSecondBtnPress?: () => void;
  isImageHeader?: boolean;
  showContentTitle?: boolean;
  contentWrapStyle?: CSSProperties;
  tipStyle?: CSSProperties;
  contentTextStyle?: CSSProperties;
}

export interface CreditPropsType extends PropsType {
  visible?: boolean;
  showClose?: boolean;
  maskHide?: boolean;
  onRequestClose?: () => void;
  useModal?: boolean;
}

export interface ISelectedIdType {
  idtype: PassPortIdtype;
  typename: string;
  subTitle?: string;
  summaryObject?: Array<any>;
}

export interface IMergeGuidInfo {
  storeGuid: string;
  address: string;
  type: IGuidInfoType;
}

export interface IStoreSelfServiceInfo {
  isSelfService?: boolean;
  text?: string;
}

export interface VehicleInfoLogDataType {
  vehicleName?: string;
  vehicleCode?: string;
  vendorName?: string;
  vendorCode?: string;
  pStoreCode?: string;
  rStoreCode?: string;
}

export interface StoreAlbumPageParamsType {
  storeId?: string;
  vehicleCode?: string;
  skuId?: number;
  albumType?: AlbumType;
}

export interface IInstalmentDetailItem {
  superscript: string;
  title: string;
  subtitle: string;
  loanPayStageCount: string;
  loanPayBusType?: string;
}
export interface ImagesPageParamsType {
  storeCode?: string;
  vehicleId?: string;
  categoryId?: number;
}
export interface FooterBarDataType {
  price?: number;
  originPrice?: number;
  discountPrice?: number;
  currencyCode?: string;
  unitDesc?: string;
  renderGapPrice?: React.ReactNode;
  renderPrice?: React.ReactNode;
}

export interface ImagesType {
  imageList: string[];
  video: string;
  videoCover: string;
  imagesTotalNumber: number;
  albumName?: string;
}

export interface LocationType {
  way?: string;
  address?: string;
  rentCenterName?: string;
  rentCenterId?: number;
  pickupServiceType?: string;
  dropoffServiceType?: string;
}

export interface IInstalmentInfo {
  description: string;
  instalmentDetailList: Array<IInstalmentDetailItem>;
}

export interface ProductReducer {
  isProductLoading?: boolean;
  isPriceLoading?: boolean;
  isFail?: boolean;
  isPriceFail?: boolean;
  // 选中的 package insPackageId
  curInsPackageId?: number;
  // 选中的 insuranceDetail packageId
  curPackageId?: number;
  // 额外设备
  selectedExtras?: any[];
  // ISD 租车保障
  selectedInsuranceId?: any[];
  // ISD 增值服务
  addOnCodes?: string[];
  curEquipments?: any[];
  curBomCode?: string;
  // 当前支付方式——原始值
  payMode?: number;
  // 自营险、预授权fix后的值，实际用户看到的支付方式
  showPayMode?: number;
  // 押金支付方式
  depositPayType?: number;
  // 准备切换的支付方式，不可用则会退回为 depositPayType
  nextDepositPayType?: number;
  // 跨岛、洲、境
  crossPlaces?: any[];
  selectedIdType?: ISelectedIdType;
  showPriceConfirm?: boolean;
  iousInfo?: IInstalmentInfo;
  selectedLoanPayStageCount?: string;
  isRebookOsd?: boolean;
  queryOsdModifyOrderNote?: () => void;
}

export interface PromotionType {
  type?: number;
  title?: string;
  description?: string;
  longTag?: string;
  longDesc?: string;
  couponDesc?: string;
  deductionPercent?: number;
  deductionAmount?: number;
  dayDeductionAmount?: number;
  payofftype?: number;
  payoffName?: string;
  code?: string;
  isFromCtrip?: boolean;
  islimitedTimeOfferType?: boolean;
  sortNum?: number;
  strategySource?: string;
  earningsCost?: string;
  businessCost?: string;
  resourceCost?: string;
  configVersion?: string;
  isEnabled?: boolean;
  actionedDate?: string;
  expiredDate?: string;
  extDesc?: string;
  selected?: boolean;
  startAmount?: number;
  isOverlay?: boolean;
  promotionId?: number;
  overlayDesc?: string;
  couponName?: string;
  unitName?: string;
  popAmountTile?: string;
  deductionType?: number;
  discountType?: number;
  adjustPriceCode?: string;
}

export interface ActivityDetailType {
  title?: string;
  status?: number;
  promotion?: PromotionType;
  promotions?: Array<PromotionType>;
  currentTotalPrice?: number;
}

export interface UsableCouponsType {
  type?: number;
  title?: string;
  description?: string;
  longTag?: string;
  longDesc?: string;
  couponDesc?: string;
  deductionPercent?: number;
  deductionAmount?: number;
  dayDeductionAmount?: number;
  payofftype?: number;
  payoffName?: string;
  code?: string;
  isFromCtrip?: boolean;
  islimitedTimeOfferType?: boolean;
  sortNum?: number;
  strategySource?: string;
  earningsCost?: string;
  businessCost?: string;
  resourceCost?: string;
  configVersion?: string;
  isEnabled?: boolean;
  actionedDate?: string;
  expiredDate?: string;
  extDesc?: string;
  selected?: boolean;
  startAmount?: number;
  isOverlay?: boolean;
  promotionId?: number;
  overlayDesc?: string;
  couponName?: string;
  unitName?: string;
  popAmountTile?: string;
  deductionType?: number;
  deductionName?: string;
  labels?: Array<LabelsType>;
  unionType?: number;
  vendorCouponCode?: string;
  vendorKey?: string;
  couponType?: number;
}

export interface NotApplyCouponType {
  title?: string;
  description?: string;
  code?: string;
  type?: number;
  typeDesc?: string;
  sortNum?: number;
}

export interface CouponListType {
  usableCoupons?: Array<UsableCouponsType>;
  unusableCoupons?: Array<UsableCouponsType>;
  selectedCoupon?: UsableCouponsType;
  notApplyCoupon?: NotApplyCouponType;
  status?: number;
  title?: string;
  subTitle?: string;
  description?: string;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  tips?: Array<string>;
}

export interface DriverItem {
  type: string;
  value?: string;
  error?: boolean;
  isEnable?: boolean;
  contactType?: ContactType;
  contactTypeName?: string;
}

export interface IdCardTypesType {
  idCardType?: number;
  idCardName?: string;
}

export interface RootObject {
  responseStatus: ResponseStatus;
  orderBaseInfo: OrderBaseInfo;
  vehicleInfo: VehicleInfo;
  vendorInfo: VendorInfo;
  pickupStore: PickupStore;
  returnStore: PickupStore;
  driverInfo: DriverInfo;
  insuranceDescriptions: InsuranceDescription[];
  cancelRuleInfo: CancelRuleInfo;
  refundProgressList: any[];
  isdFeeInfo: IsdFeeInfo;
  isdVendorInsurance: IsdVendorInsurance;
  onlinePreAuth: any[];
  invoice: Invoice;
  rentCenter: RentCenter;
  modifyInfo: ModifyInfo;
  baseResponse: BaseResponse;
  addPayments: any[];
  faqListInfo: FaqListInfo;
  isdCarMgImUrl: string;
  creditInfo: CreditInfo;
  isAlipay: boolean;
  freeDeposit: FreeDeposit;
  extendedInfo: ExtendedInfo;
}

export interface ExtendedInfo {
  attr: any;
  showCustomerCallModal: boolean;
  orderExtDescList: any[];
  flightDelayRule: any;
  ctripInsuranceVersion?: string;
}

export interface FreeDeposit {
  depositStatus: number;
  showDepositType: number;
  depositExplain: string;
  payMethodExplain: string;
  freeDepositType: number;
  freeDepositWay: number;
  preAmountForCar: number;
  preAmountForPeccancy: number;
  depositItems: DepositItem[];
  deductionTime: string;
  isBeforeNow: boolean;
}

export interface DepositItem {
  depositTitle: string;
  deposit?: number;
  depositStatus: number;
  explain: string;
}

export interface CreditInfo {
  cashPledgeStatus: number;
  deposit: number;
  depositExplain: string;
  requestid: string;
  serviceAgreement: string;
  wZDeposit: number;
}

export interface FaqListInfo {
  faqList: any[];
}

export interface BaseResponse {
  isSuccess: boolean;
  code: string;
  returnMsg: string;
  requestId: string;
  cost: number;
}

export interface ModifyInfo {
  pickupcityId: string;
  returncityId: string;
  pickupStoreId: string;
  returnStoreId: string;
  vendorId: string;
  vehicleId: string;
  isgranted: boolean;
  grantedCode: string;
  ctripVehicleId: string;
  pickupStoreServiceType: string;
  returnStoreServiceType: string;
  rateCategory: string;
  rateCode: string;
  payMode: number;
  vdegree: string;
  priceType: number;
}

export interface RentCenter {
  rentCenter: boolean;
}

export interface Invoice {
  deliveryInfo: DeliveryInfo;
}

export interface DeliveryInfo {}

export interface IsdVendorInsurance {
  insurancedesc: string;
  insurancelist: Insurancelist[];
}

export interface Insurancelist {
  title: string;
  type: number;
  desclist: Desclist[];
}

export interface Desclist {
  title?: string;
  desclist: string[];
}

export interface IsdFeeInfo {
  salesAmount: number;
  actualAmount: number;
  noPayAmount: number;
  firstPayAmount: number;
  totalAmount: number;
  orderAmount: number;
  extraAmount: number;
  deductAmount: number;
  rebackAmount: number;
  precar: number;
  prepeccancy: number;
  priceType: number;
  rateCode: string;
  rateCategory: string;
  isWholeday: number;
  feeList: FeeList[];
  preAuthDesc: string;
  preAuthAmount: number;
  preAuthDisplay: number;
  totalRentalPrice: number;
  exceedTenancy: number;
  exceedPrice: number;
  dailyPrice: number;
}

export interface FeeList {
  priceCode: string;
  priceName: string;
  amount: number;
  quantity: number;
  descdetail: any[];
}

export interface CancelRuleInfo {
  cancelTip: string;
  cancelReasons: string[];
  cancelRules: CancelRule[];
}

export interface CancelRule {
  freeStatus: number;
  free: number;
  title: string;
  context: string;
  time: string;
  hit: boolean;
}

export interface InsuranceDescription {
  code: string;
  name: string;
  minCoverage: number;
  maxCoverage: number;
  productId: number;
  shortDesc: string;
  quantity: number;
  status: number;
  ordertitle: string;
  requestid: string;
  dlabel: string[];
  exttip: string;
}
export interface ContactWayList {
  contactWayType?: ContactType;
  contactWayValue?: string;
  contactWayName?: string;
}
export interface DriverInfo {
  name: string;
  email: string;
  telphone: string;
  areaCode: string;
  flightNo: string;
  iDCardType: number;
  iDCardNo: string;
  distributionMobile: string;
  distributionEmail: string;
  contactWayList: ContactWayList[];
  isChangeContact?: boolean;
  age?: string;
}

export interface PickupStore {
  localDateTime: string;
  storeName: string;
  storeCode: string;
  storeAddress: string;
  longitude: number;
  latitude: number;
  storeTel: string;
  cityName: string;
  provinceName: string;
  countryName: string;
  fromTime: string;
  toTime: string;
  cityId: number;
  storeSerivceName: string;
  userAddress: string;
  userLongitude: number;
  userLatitude: number;
  serviceType: string;
  serviceDetails: string[];
  addrTypeName: string;
  storeID: number;
  commentCount: number;
  pickUpOffLevel: number;
  sendTypeForPickUpOffCar: number;
  contactWayList: ContactWayList[];
}
export interface VendorInfo {
  vendorName: string;
  vendorImageUrl: string;
  vendorID: number;
  vendorConfirmCode: string;
  isSelf: boolean;
  selfName: string;
  vendorMobileImageUrl: string;
  bookingNotice: any[];
  commentInfo: CommentInfo;
}

export interface CommentInfo {
  vendorGoodType: number;
  exposedScore: number;
  topScore: number;
  level: string;
  commentLabel: string;
}

export interface VehicleInfo {
  vehicleName: string;
  passengerNum: number;
  transmission: string;
  vehicleGroupName: string;
  vendorVehicleCode: string;
  imageUrl: string;
  similarImageUrls: string[];
  granted: boolean;
  grantCode: string;
  vendorVehicleID: number;
  ctripVehicleID: string;
  vehicleDegree: string;
  displacement: string;
  labels: any[];
  license: string;
  licenseStyle: string;
  doorNum: number;
}

export interface OrderBaseInfo {
  orderId: number;
  uId: string;
  orderDate: number;
  orderStatus: number;
  orderStatusDesc: string;
  allOperations: AllOperation[];
  orderTip: OrderTip;
  useDate: number;
  returnDate: number;
  duration: number;
  ftype: number;
  useCityID: number;
  useCity: string;
  selfName: string;
  vendorOrderCode: string;
  useQuantity: number;
  processStatus: number;
  lastEnablePayTime: number;
  orderType: number;
  payMode: number;
  payModeDesc: string;
  distributionChannelId: number;
  quickPayNo: string;
  remark: string;
  rateCode: string;
  rateCategory: string;
  grantedCode: string;
  preAmountForCar: number;
  preAmountForPeccancy: number;
  preAmountType: number;
  preAuthStatus: number;
  vendorPreAuthInfo: VendorPreAuthInfo;
  preAmountDesc: PreAmountDesc[];
  freeCancelTime: number;
  cancelRuleDesc: string;
  alipay: boolean;
  safeRent: boolean;
  successSafeRentAuth: boolean;
  orderContact: boolean;
  continueBackPay: boolean;
  creditRiskResult: string;
  foreInsurance: number;
}

export interface PreAmountDesc {
  name: string;
  description: string;
}

export interface VendorPreAuthInfo {
  preAuthDisplay: number;
  preWay: number;
  authdesc: string;
  authMartket: number;
  authLabel: string;
  quickPayNo: string;
}

export interface OrderTip {
  tipContentArray: string[];
}

export interface AllOperation {
  operationId: number;
  buttonName: string;
  enable: boolean;
}

export interface ResponseStatus {
  timestamp: string;
  ack: string;
  errors: any[];
  extension: Extension[];
}

export interface Extension {
  id: string;
  value: string;
}

export interface ModalItemType {
  visible?: boolean;
  data?: any;
  isModifyOrder?: boolean;
}
export interface OrderModalsVisible {
  createInsModalVisible?: ModalItemType;
  insFailedModalVisible?: ModalItemType;
  optimizeModalVisible?: ModalItemType;
  confirmModal?: ModalItemType;
  cancelOrderConfirmModal?: ModalItemType;
  pickUpMaterials?: ModalItemType;
  renewTipModal?: ModalItemType;
  refundDetailModal?: ModalItemType;
  sesameRepeatOrderModal?: ModalItemType;
  ehiModifyOrderModal?: ModalItemType;
  ehiFreeDepositModal?: ModalItemType;
  OrderModalsVisible?: ModalItemType;
  buyInsConfirmModal?: ModalItemType;
  damageFeeDetailModalVisible?: ModalItemType;
  orderCashBackModal?: ModalItemType;
  depositPaymentModal?: ModalItemType;
  reviewUnopenedModal?: ModalItemType;
  optimizationStrengthenModal?: ModalItemType;
  advanceReturnModal?: ModalItemType;
  advanceReturnFeeModal?: ModalItemType;
  businessLicenseModal?: ModalItemType;
  etcIntroModal?: ModalItemType;
  etcUseHelperModal?: ModalItemType;
  claimProcessVisible?: ModalItemType;
  vehicleUseNotesModal?: ModalItemType;
  pickUpMaterialsModal?: ModalItemType;
  distanceInvalidateModal?: ModalItemType;
  businessTimePolicyModal: ModalItemType;
  businessTimeModal: ModalItemType;
  fuelDescModalVisible?: ModalItemType;
}

// 0: 确认，1: 取消（导航栏back回退，侧滑回退，android物理键回退），2: 保代页面异常导致保险必须取消
export enum InsCallStatus {
  submit = 0,
  back = 1,
  cancel = 2,
}

export enum QueryOrderApiStatusType {
  unstart = 0,
  before = 3,
  success = 1,
  fail = 2,
}

export enum CarAssistantItemType {
  PickUpMaterials = 12,
} // 取车材料

export enum InsuranceAndXProductGroup {
  Insurance = 1,
  XProduct = 2,
}

export interface OrderCashBackInfoLabels {
  code?: string;
  title?: string;
  subTitle?: string;
}

// 返现结果类型
export enum CashReturnResultType {
  WaitReturn = 0,
  ReturnFail = 1,
  ReturnSuccess = 2,
}

export interface OrderCashBackInfo {
  notices?: string[];
  labels?: OrderCashBackInfoLabels[];
  type?: CashReturnResultType;
}

export enum AuthType {
  regular = 0,
  byPhone = 1,
}

export enum TipItemType {
  /**
   * 取车材料
   */
  PickUpMaterials = 1,
  /**
   * 自驾政策
   */
  DriverLicense = 2,
  /**
   * 微信入群入口
   */
  MicroEnterPrise = 3,
  /**
   * 合同翻译版
   */
  ContactTemplates = 4,
  /**
   * 提车凭证
   */
  Voucher = 5,
  /**
   * 标题
   */
  Title = 6,
  // 车况拍照
  PHOTOGRAPH = 7,
  // 免费办理翻译件
  TRANSLATION = 8,
}

export enum ITipItemStatusType {
  /**
   * 不可用
   */
  Unable = 0,
  /**
   * 1可用
   */
  Able = 1,
}

export interface ITipItemButton {
  title?: string;
  statusType?: ITipItemStatusType;
  appWeChatUrl?: string;
  weChatUrl?: string;
  h5Url?: string;
}

export interface ITipItem {
  content?: string;
  style?: string;
  type?: TipItemType;
  title?: string;
  subTitle?: string;
  note?: string;
  url?: string;
  urls?: LinkDTO[];
  button?: ITipItemButton;
  index?: number;
  orderStatus?: number;
  orderId?: string;
  countryId?: number;
  onPress?: () => void;
  hasThreeElements?: boolean;
  vendorId?: string;
  btnNumber?: number;
}
export interface LinkDTO {
  url?: string | undefined;
  /**
   * 1-车损、2-违章、3-退款、4-补款
   */
  type?: string | undefined;
  desc?: string | undefined;
}
export interface TipsCardProps {
  visible?: boolean;
  propData?: Array<ITipItem>;
  data?: Array<ITipItem>;
  isOneRow?: boolean;
  orderStatus?: number;
  orderId?: string;
  countryId?: number;
  vendorId?: string;
  onPress?: () => void;
}

export interface RenewCardProps {
  onPress: (day: number) => void;
  orderId?: string;
  orderStatus?: string;
  visible?: boolean;
  data?: Array<ITipItem>;
  isSelfService?: boolean;
  isShowTopBorder?: boolean;
}

export interface ContinuePayCardProps {
  onPress?: () => void;
  amount?: number;
  payTip?: string;
  payTick?: ContinuePayTickRes;
  osdOriginOrderId?: string;
  onTiming?: () => void;
  onTimeOut?: () => void;
}
export interface AuthCardProps {
  visible?: boolean;
  isAuthPassed?: boolean;
  title?: string;
  buttonText?: string;
  onPress?: () => void;
  wrapStyle?: CSSProperties;
  authImageStyle?: CSSProperties;
}

export interface MoreCardsProps {
  title?: string;
  moreCount?: number;
  onPress?: () => void;
}

export interface RefundCardProps {
  visible?: boolean;
  title: string;
  isShowQuestion?: boolean;
  onPressQuestion?: () => void;
  statusData?: any;
}

export interface CustomerServiceCardProps {
  visible?: boolean;
  title?: string;
  time?: string;
  subTitle?: string;
  onPress?: () => void;
}

export enum ICardHistory {
  isHistory = 'true',
  notHistory = 'false',
  unknown = 'unknown',
}

export enum IAuthStatus {
  Passed = 4,
}

export enum ILimitCode {
  LimitArea = 'limitArea',
  Mileage = 'mileage',
}

export interface DriverHeaderProps {
  onPressAddMoreDriver: () => void;
}

export interface DriverItemProps {
  title: string;
  content: string;
}

export interface DriverDescProps {
  desc: string;
  onPress: () => void;
}

export interface DriverInfoProps {
  data?: DriverInfo;
  extendedInfo?: any;
  supportInfo?: any;
  onPressAddMoreDriver?: () => void;
  orderId?: number;
  orderStatus?: number;
}

export enum DriverInfoField {
  Telephone = 'telphone',
  IDCardType = 'iDCardType',
}

export interface VendorCallProps {
  desc: string;
  storeAttendant: any;
  setPhoneModalVisible: (data: any) => void;
  setPersonPhoneModalVisible: (data: any) => void;
  orderId?: number;
  orderStatus?: number;
}
export interface ButtonsVendorCallProps {
  storeAttendant: any;
  setPhoneModalVisible: (data: any) => void;
  setPersonPhoneModalVisible: (data: any) => void;
  orderId?: number;
  orderStatus?: number;
  operationButtons?: AllOperationsType[];
  finalQueryIsFinish?: boolean;
  isFulfillmentOSD?: boolean;
}

export enum IStoreAttendantType {
  PickUp = 1,
  DropOff = 2,
}

export interface IOrderBuriedPointData {
  orderId?: number;
  orderStatus?: number;
}

export enum IPayStatus {
  ToPay = 0,
} // 待支付

export enum OrderMessageCardType {
  Refund = 'Refund', // 无损取消申请服务进度
  CustomerService = 'CustomerService', // 客服服务进度
  Tips = 'Tips', // 用车小贴士
  LicenceAuth = 'LicenceAuth', // 认证驾驶员
  Renew = 'Renew', // 续租卡片
  ContinuePay = 'ContinuePay', // 继续支付
  FulFillMent = 'FulFillMent', // 履约卡片
  PolicyTips = 'PolicyTips', // 政策栏
  CommentCard = 'CommentCard', // 点评卡片
  DidNotice = 'DidNotice',
} // 门店消息

export interface IMessageCardConfig {
  type: OrderMessageCardType; // 卡片类型
  title: string; // 卡片标题
  isShow: boolean; // 卡片是否展示
  isHistory: boolean; // 是否历史消息
}

export enum MessageCardRenderMode {
  CurrentAndMore = 'CurrentAndMore', // 订详入口展示
  Current = 'Current', // 消息助手页
  History = 'History',
} // 消息助手页
export interface ContinuePayTickRes {
  visible: boolean;
  minute: number;
  second: number;
}
export interface IMessageCardStatus {
  currentCardsMap: IMessageCardConfig[];
  historyCardsMap: IMessageCardConfig[];
}

export interface IChangeReminder {
  changeTipTime: number;
  tipText: {
    title: string;
    description: string;
  };
}
export interface OrderCancelInfo {
  feeInfo: FeeInfo;
  lossDetail: LossDetail;
  cancelReasonList: CancelReasonList2[];
  cashback: Cashback;
  cancelTip: CancelTip;
  changeReminder?: IChangeReminder;
}

export interface CancelTip {
  title: string;
  desc: string[];
}

export interface Cashback {
  title: string;
  desc: string;
  titleSuppl: TitleSuppl;
}

export interface CancelReasonList2 {
  reason: string;
  code: number;
  type: string;
  tip?: Tip;
  cashback: boolean;
  cancelReasonList?: CancelReasonList[];
}

export interface CancelReasonList {
  reason: string;
  code: number;
  type: string;
  cashback: boolean;
}

export interface Tip {
  title: string;
  desc: string;
  button: string;
  code: number;
}

export interface LossDetail {
  title: string;
  titleSuppl: TitleSuppl;
  backDescList: BackDescList[];
  nonRefundable: NonRefundable;
  changeReminder: IChangeReminder;
  cancelPageTopTip: string;
}

export interface NonRefundable {
  title: string;
  descList: DescList[];
}

export interface DescList {
  title: string;
  subTitle: string;
}

export interface BackDescList {
  title: string;
  subTitle?: string;
}

export interface TitleSuppl {
  text: string;
  color: string;
}

export interface FeeInfo {
  amount: number;
  currencyCode: string;
  holidays: boolean;
  canRefund: boolean;
  localAmount: number;
  localCurrencyCode: string;
  warnTip: string;
  confirmMsg: string;
}

export enum ZhimaWarnType {
  yihai = 1, // 一嗨不支持免押
  normal = 2,
} // 已达最大笔数

export interface TextsItemType {
  title: string;
  link?: {
    text?: string;
    url?: string;
  };
}

export enum IOrderZhimaBtnType {
  RealName = 1,
  Auth = 2,
  Comfirm = 3,
}

export interface ZhimaResultMap {
  warnTip?: string;
  warnType?: ZhimaWarnType;
  texts?: TextsItemType[];
  btnType?: IOrderZhimaBtnType;
}

export interface CustomerPhoneModalProps extends ModalProps {
  orderId?: string;
  orderStatus?: number;
  type?: CustomerPhoneModalType;
  modalVisible?: boolean;
  menuList?: any;
  orderPriceInfo?: any;
  tourImJumpUrl?: string;
  title?: string;
  storeAttendant?: any;
  vendorImUrl?: string;
}
export interface IPenaltyChangeTip {
  title: string;
  description?: string;
}

export interface IOsdFeeTitleExplainModal {
  visible?: boolean;
  feeTitle?: string;
  feeTitleExplain?: string;
  onCancel?: () => void;
}
export interface IOsdModifyOrderModal {
  visible?: boolean;
  setLocationAndDatePopIsShow?: ({ visible }) => void;
  searchPanelModalRefFn?: (data) => void;
  currentPageId?: string;
  orderId?: number;
  osdModifyOrderNote?: QueryOsdModifyOrderNoteResponseType;
  pressSearchCallback?: () => void;
}

export interface InsuranceProtectionsProps {
  isdFeeInfo: any;
  packageInfo: any;
  showInsDetailModal: (data?: any) => void;
  appOrderDetailIsSettlementOfClaimOpen: boolean;
  orderStatus: any;
  extendedInfo?: any;
  isuranceBox?: any;
  showOsdInsuranceModal?: (item, anchor) => void;
  openInsuranceReminderEnglishModal?: (englishContent) => void;
  onInsuranceElectronicDetail?: (url) => void;
  queryExtraInsurance?: (data) => void;
  autoRefresh?: () => void;
  directOpen?: OrderDetailCons.DirectOpen;
  handleMainscrollerScroll?: (y: number) => void;
}

export interface PickupMaterialsModalProps {
  data: any;
  onHide?: () => void;
  visible: boolean;
}

export interface MessageCardsProps {
  renderMode: MessageCardRenderMode;
  messageCardStatus: IMessageCardStatus;
  orderBaseInfo: any;
  supportInfo: any;
  isOrderDataByPhone: boolean;
  serviceTitle: string;
  serviceDesc: string;
  serviceLatestTime: string;
  showConsultProgressModal: () => void;
  refundPenaltyInfo: any;
  setPhoneModalVisible: (data?: any) => void;
  showCancelPenaltyModal: () => void;
  showOrderRefundDetailModal: () => void;
  tipsCardInfo: any;
  service?: any;
  payTick?: ContinuePayTickRes;
  isdFeeInfo: IsdFeeInfoType;
  ctripContinuePay: () => void;
  orderStatusCtrip: OrderStatusCtrip;
  showLimitPopModal?: () => void;
  showMileageLimitModal?: () => void;
  showStorePolicyModal?: (_?: any, __?: any) => void;
  showClaimProcessModal?: () => void;
  showVehicleUseNotesModal?: () => void;
  showFlightDelayRulesModal?: () => void;
}

export interface CnButtonTypes {
  text: string;
  onPress: (data: number) => void;
  disabled?: boolean;
  isGray?: boolean;
  id: number;
  index?: number;
  testID?: string;
  buttonStyle?: CSSProperties;
  textStyle?: CSSProperties;
  children?: any;
  isLoading?: boolean;
  buttonWidth?: number;
  isLast?: boolean;
  taTestID?: string;
}

export interface CnPayButtonTypes extends CnButtonTypes {
  buttonSize?: string;
  buttonType?: string;
}

export interface IReplenishPay {
  testID?: string;
  style?: CSSProperties;
  isPayBlock?: boolean;
  noSpace?: boolean;
}

export enum LinkTypes {
  VehicleDamage = '1',
  Violation = '2',
  Refund = '3',
  FeeDeductionFailed = '4',
}

export enum DotLevelType {
  Large = '10',
  Small = '20',
}

// 链接
export interface ILink {
  type: LinkTypes;
  desc: string;
  onlyVehicleDamageId: number;
  orderId: number;
  orderStatus: number;
  setVehicleDamageId: (data: number) => void;
  openRefundDetailModal: () => void;
}

// 进度标签
export interface ILabel {
  color: string;
  text: string;
}

// 进度点
export interface IDot {
  level: DotLevelType;
  color: string;
}

export enum LabelType {
  Gray = '0',
  Orange = '1',
  Green = '2',
}

// 进度Item
export interface ISubText {
  subDesc: string;
  feeVoucher?: Array<string>;
}

export interface IProgressInfo {
  mainText: string;
  subText: Array<ISubText>;
  links: Array<ILink>;
  type: string;
  level: DotLevelType;
  color: string;
  name: string;
  depositStatus: number;
  isFirst: boolean;
  isLast: boolean;
  currentIndex: number;
  index: number;
  onlyVehicleDamageId: number;
  deductId?: number;
  orderStatus?: number;
  orderId?: number;
  setVehicleDamageId: (data: number) => void;
  openRefundDetailModal: () => void;
}

export enum SecretBoxStage {
  NoSecretBox,
  SecretBox,
  OpenSecretBox,
}
export interface VehicleProps {
  orderBaseInfo?: any;
  vehicleInfo?: any;
  vendorInfo?: any;
  pickupStore?: any;
  returnStore?: any;
  similarVehicleInfo?: any;
  theme?: any;
  limitRuleCont?: any;
  orderDetailRef?: any;
  setLimitRulePopVisible?: (data: any) => void;
  setLabelsModalVisible?: (data: any) => void;
  limitRulePopVisible?: boolean;
  getLimitContentData?: () => void;
  extendedInfo?: ExtendedInfo;
  carTags?: Array<VendorTagType>;
  testID?: string;
  useCityID?: any;
  setOrderModalsVisible: (data: {
    [key: string]: { [key: string]: boolean };
  }) => void;
  safeRent?: boolean;
  orderId?: number;
  orderStatus?: number;
  finalQueryIsFinish: boolean;
  advanceReturnRecord: any;
  isEasyLife2024?: boolean;
  orderDetailResponse?: any;
  logBaseInfo?: any;
  nationalChainTagTitle?: string;
  onPressVendor?: () => void;
}

export interface OTimeLineProps {
  lTitle: string;
  rTitle: string;
  isShowYear: boolean;
  diffGap: string;
  pdateYmdString: string;
  rdateYmdString: string;
  pdateYearString: string;
  rdateYearString: string;
}

export interface IFlightDelayRuleEntry {
  description?: string;
  delayWarnTip?: string;
  delayStatus?: IDelayStatus;
  onPress?: () => void;
  logBaseInfo?: any;
}

export enum ISelfServiceAssistantType {
  Authentication = 1, // 认证
  VehicleStatus = 2, // 车辆状态
  VehicleControl = 3, // 车辆控制
  GoToReturnCarPoint = 4, // 前往还车点
  ReRent = 5, // 续租
  ChangeVehicle = 6, // 行中换车
  Other = 0,
} // 其他

export enum ISelfServiceAssistantStatus {
  NotCertified = 'notCertified', // 未认证
  Certified = 'certified', // 已认证
  Preparation = 'preparation', // 准备中
  Arranged = 'arranged', // 已准备
  BeingInspected = 'beingInspected',
} // 检测中

export interface ISelfServiceAssistant {
  data: ISelfServiceAssistantItem[];
  orderId: number;
  orderStatus: string;
  isShowSelfServiceRenew: boolean;
  isVehicleStatusPolling: boolean;
  fetchOrder: (data) => void;
  setPhoneModalVisible: (data) => void;
}

export enum ITextStyle {
  Button = '0',
  Link = '1',
}

export interface ISelfServiceAssistantItem {
  type: ISelfServiceAssistantType;
  status: ISelfServiceAssistantStatus;
  title: string;
  content: string[];
  note: string;
  urlName: string;
  code: ITextStyle;
  index: number;
  orderId: number;
  isVehicleStatusPolling: boolean;
  fetchOrder: (data) => void;
  setPhoneModalVisible: (data) => void;
}

export interface ISelfServiceAssistantVehicleControl {
  lockStatus?: ILockStatus; // 门锁状态，1=开，0=关
  energyType?: IEnergyType; // 能源类型
  oilPercent?: number; // 剩余油量百分比
  remainOil?: number; // 剩余油量
  electricPercent?: number; // 剩余电量
  remainMileage?: number; // 剩余里程
  oilThreshold?: number; // 低油量提醒阈值
  electricThreshold?: number; // 低电量阈值
  index?: number;
  isVehicleStatusPolling: boolean;
  queryVehicleStatus: (data) => void; // 获取车机状态
  selfServiceOperation: (data) => void; // 操作车机
  isContractTracker?: boolean; // 是否是履约可视化
  isGray?: boolean; // 是否置灰
  grayTitle?: string; // 灰色标题
  grayDesc?: string; // 灰色描述
}

export interface ISelfServiceAssistantCountDown {
  title: string;
  expirationTime: string;
  orderId: number;
  fetchOrder: (data) => void;
  isContractTracker?: boolean;
}

export enum IEnergyType {
  Oil = 1,
  Electric = 2,
  Hybrid = 3,
}

export interface ISelfServiceEnergyItem {
  name: string;
  currentPercent: number;
  isLower: boolean;
  value: string;
  remainMileage: string;
  index: number;
}

export interface ISelfServiceEnergy {
  data: ISelfServiceEnergyItem[];
}

export enum IInvoiceType {
  CAN_NOT_INVOICE = 0, // 不可开票
  CAN_INVOICE = 1, // 可开票（新增）
  CAN_MODIFY_INVOICE = 2, // 已申请开票可修改
  CAN_NOT_MODIFY_INVOICE = 3,
} // 已申请开票不可修改

export enum IInvoiceButtonCode {
  EDIT = 0, // 我要发票
  LOOK = 1,
} // 查看发票

export enum IPolicyTipType {
  limit = 0, // 限行政策
  mileage = 1, // 里程限制与禁行
  accident = 2, // 车辆事故处理
  store = 3, // 门店政策
  selfService = 4, // 自助取还事项
  flightDelay = 5, // 航班延误保留政策
  accidentOsd = 6,
} // 车辆意外与故障处理

export type PriceDaily = {
  date: string; // 日期
  oDprice: string; // 原天价
  priceStr: string; // 展示金额
  showType: number; // 展示类型（前端样式用） 0 默认宽度， 1 day1-day2 展示宽度
};

export interface IItemDetailType {
  code?: string;
  title: string;
  subTitle?: string;
  description?: string;
  currentTotalPrice?: number;
  priceDailys?: Array<PriceDaily>; // 日历价
  hourDesc?: string; // 小时费描述
  dPriceDesc?: string; // 日均价描述
  isPromotion?: boolean; // 是否是优惠
  showFree?: boolean; // 是否免费
  isPriceDesc?: boolean; // 是否是支付描述，比如："礼品卡￥600 + 支付宝￥73"这类信息
  size?: string;
  desc?: string;
}

export interface IItemType {
  title: string; // 标题
  subTitle?: string; // 副标题
  description?: string; // 描述
  currencyCode?: string;
  currentTotalPrice: number; // 总价
  currentDailyPrice?: number; // 日价
  originDailyPrice?: number; // 优惠前的日均价
  originTotalPrice?: number; // 优惠前的总价
  code?: string;
  items?: Array<IItemDetailType>;
  notices?: Array<string>;
  hourDesc?: string; // 小时费描述
}

export interface IFeeDetailType {
  chargesInfos: Array<IItemType>; // 分组费用详情
  chargesSummary: IItemType; // 金额信息
  cashBackInfo: IItemType; // 返现
  points: PointsType; // 积分
  offlineFee: IItemType; // 线下购买项
}

export enum PolicyPressType {
  All = 'all',
  AccidentBreakdownRule = 34, // 门店政策-意外或故障
  importantInformation = 33,
}
export interface WeChatInviteButton {
  Title?: string | null;
  /**
   * 状态 0 不可用 1可用
   */
  StatusType?: number | null;
  /**
   * app跳转的微信小程序邀请链接
   */
  appWeChatUrl?: string | null;
  /**
   * 微信小程序邀请链接
   */
  WeChatUrl?: string | null;
  /**
   * H5邀请链接
   */
  H5Url?: string | null;
}

export interface CarAssistantSummaryV2DTO {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 副标题
   */
  SubTitle?: string | null;
  /**
   * 1-取车材料，2-自驾政策，3-微信入群入口，4-合同翻译版，5-提车凭证，6-标题
   */
  type?: number | null;
  /**
   * 按钮
   */
  button?: WeChatInviteButton | null;
  url?: string | null;
  Note?: string | null;
  urls?: LinkDTO[] | null;
}

export interface QueryOrderFulfillmentInfoResponseType {
  responseStatus?: ResponseStatusType | undefined;
  result?: ResponseResult | undefined;
  /**
   * 履约节点集合
   */
  fulfillmentNodeList?: OrderFulfillmentNodeDTO[] | undefined;
  /**
   * nodeHash
   */
  nodeHash?: string | undefined;
}
export interface OrderFulfillmentNodeDTO {
  /**
   * 履约节点类型 1-取车，2-用车，3-还车
   */
  nodeType?: number | undefined;
  /**
   * 履约节点状态，0-未开始，1-进行中，2-已完成
   */
  nodeStatus?: number | undefined;
  /**
   * 标题，取车、用车、还车
   */
  nodeTitle?: string | undefined;
  /**
   * 履约步骤集合
   */
  processList?: OrderFulfillmentProcessDTO[] | undefined;
}
export interface OrderFulfillmentProcessDTO {
  /**
   * 步骤标题
   */
  title?: string | undefined;
  /**
   * 状态 0-未开始，1-进行中，2-已完成
   */
  processStatus?: number | undefined;
  /**
   * 按钮集合信息
   */
  operationList?: OrderOperation[] | undefined;
  /**
   * 排序
   */
  sort?: number | undefined;
  /**
   * 子步骤
   */
  subProcessList?: OrderFulfillmentProcessDTO[] | undefined;
  /**
   * 步骤层级, 1,2,3,4,5
   */
  level?: number | undefined;
  /**
   * 步骤code
   */
  processCode?: string | undefined;
  /**
   * 说明
   */
  desc?: string | undefined;
}
export interface OrderOperation {
  /**
   * 操作ID 1-去支付 2-取消 3-去点评 4-再次预订 5-打印提车单 6-打印电子发票 7-修改
   * 订单 8-修改详情 9-查看修改后订单 10-查看修改前订单
   */
  operationId?: number | undefined;
  /**
   * 按钮名称
   */
  buttonName?: string | undefined;
  enable?: boolean | undefined;
  /**
   * 显示效果： none不显示
   */
  display?: string | undefined;
  /**
   * 0-正常露出点评，无积分奖励，1-正常露出，有积分奖励，2追评，3查看点评，4露出，但不能点评
   */
  code?: number | undefined;
  /**
   * 按钮上的标签，示例：最高150积分
   */
  label?: string | undefined;
  /**
   * 跳转地址
   */
  url?: string | undefined;
  /**
   * 不支持修改的原因，取消文案展示会用到 1、订单状态非已确认都不支持修改订单，2、一嗨不支持，3、供应
   * 商黑名单不支持 ，4、2小时以内不支持
   */
  disableCode?: number | undefined;
  /**
   * 按钮内容信息
   */
  contents?: ButtonContentDTO[] | undefined;
  attrExtra?: AttrExtra[] | undefined;
}
export interface AttrExtra {
  code?: string | undefined;
  value?: string | undefined;
}
export interface ButtonContentDTO {
  /**
   * 1、黑体加粗，2、灰色小字，3、红色字，4虚线
   */
  type?: number | undefined;
  text?: string | undefined;
  val?: string | undefined;
}
export interface ResponseResult {
  /**
   * 是否成功
   */
  success?: boolean | undefined;
  /**
   * 自定义错误码
   */
  errorCode?: string | undefined;
  /**
   * 自定义错误信息
   */
  errorMsg?: string | undefined;
}
export interface ResponseStatusType {
  timestamp?: string | undefined;
  ack?: AckCodeType | undefined;
  errors?: ErrorDataType[] | undefined;
  build?: string | undefined;
  version?: string | undefined;
  extension?: ExtensionType[] | undefined;
  /**
   * 描述信息
   */
  responseDesc?: string | undefined;
  userID?: string | undefined;
  msg?: string | undefined;
  /**
   * 响应编码（20000：成功）
   */
  responseCode?: number | undefined;
  code?: string | undefined;
}
export interface ExtensionType {
  /**
   * ExtensionType
   */
  id?: string | undefined;
  /**
   * ExtensionType
   */
  version?: string | undefined;
  /**
   * ExtensionType
   */
  contentType?: string | undefined;
  /**
   * ExtensionType
   */
  value?: string | undefined;
}
export interface ErrorDataType {
  message?: string | undefined;
  /**
   * A unique code that identifies the particular error
   *  condition that occurred.
   */
  errorCode?: string | undefined;
  /**
   * ErrorDataType
   */
  stackTrace?: string | undefined;
  /**
   * ErrorDataType
   */
  severityCode?: SeverityCodeType | undefined;
  /**
   * ErrorDataType
   */
  errorFields?: ErrorFieldType | undefined;
  /**
   * ErrorDataType
   */
  errorClassification?: ErrorClassificationCodeType | undefined;
}
export interface ErrorFieldType {
  /**
   * ErrorFieldType
   */
  fieldName?: string | undefined;
  /**
   * ErrorFieldType
   */
  errorCode?: string | undefined;
  /**
   * ErrorFieldType
   */
  message?: string | undefined;
}

enum ErrorClassificationCodeType {
  ServiceError = 0,
  ValidationError = 1,
  FrameworkError = 2,
  SLAError = 3,
}

enum SeverityCodeType {
  Error = 0,
  Warning = 1,
}

enum AckCodeType {
  Success = 0,
  Failure = 1,
  Warning = 2,
  PartialFailure = 3,
}

export enum CheckSubmitReturnCarCode {
  returnCarDistanceInvalidateFail = '11',
}
export interface NoticeFromDid {
  /**
   * 对应全局统一的枚举，1=车损，2=违章停车费，5=结算单据
   */
  emailType?: number | null;
  /**
   * 多语言，对应文件类型名称
   */
  typeName?: string | null;
  /**
   * 多语言，消息内容，如：1条车损
   */
  text?: string | null;
}
export interface DidNoticeDataTypes {
  noticeList?: NoticeFromDid[] | null;
  /**
   * 多语言，“门店消息”
   */
  noticeTitle?: string | null;
  /**
   * 是否历史消息
   */
  history?: boolean | null;
}

export interface ISurveyEntry {
  orderId?: number;
  orderStatus?: number;
  storeId?: number;
  onPressYes?: () => void;
  onPressNo?: () => void;
}

export type CardResultType = {
  idNo?: string;
  name?: string;
  expiryDate?: string;
  issueDate?: string;
  cardAImageUrl?: string;
  cardBImageUrl?: string;
  licenceType?: string;
  authority?: string;
  resultA?: boolean;
  resultB?: boolean;
  msg?: string;
  isActive?: boolean;
};

export interface SubListType {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  labelCode?: string;
  positionCode?: string;
}

export interface SubListType2 {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  subList?: Array<SubListType>;
  labelCode?: string;
  positionCode?: string;
}

export interface VendorTagType {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  subList?: Array<SubListType2>;
  labelCode?: string;
  positionCode?: string;
}

export interface WarningDto {
  urgencyType?: number;
  urgencyLevel?: number;
  warningTitle?: string;
  warningContent?: string;
  warningRichText?: string;
}

export interface WarningListResponseType {
  warningDtos: Array<WarningDto>;
  prefixTitle?: string;
}

export interface RenderFilterItemsP {
  nFilterItems: Array<any>;
  sesameBarTexts?: any;
  onPressFilter: (data, index: number) => void;
  style?: CSSProperties;
}

export interface DailyPriceDTO {
  /**
   * 日期
   */
  date?: string | null;
  /**
   * 展示金额
   */
  priceStr?: string | null;
  /**
   * 原天价
   */
  oDprice?: string | null;
  /**
   * 展示类型（前端样式用） 0 默认宽度， 1 day1-day2 展示宽度
   */
  showType?: number | null;
}

export interface FeeSubItem {
  subTitle?: string | null;
  amount?: number | null;
  price?: number | null;
  count?: number | null;
  unit?: string | null;
  localCurrencyCode?: string | null;
  /**
   * 日均价描述
   */
  dPriceDesc?: string | null;
  /**
   * 小时费描述
   */
  hourDesc?: string | null;
  /**
   * 日价
   */
  priceDailys?: DailyPriceDTO[] | null;
}

export interface FeeItem {
  serviceCode?: string | null;
  title?: string | null;
  /**
   * 例如活动为标题时，子标题时活动名
   */
  subTitle?: string | null;
  /**
   * 字体样式，b加粗，无/空则默认
   */
  fieldStyle?: string | null;
  /**
   * 改变类型，0 无变化，1上涨，2下降，3不可用
   */
  changeType?: number | null;
  /**
   * 0, 普通费用项，1活动，2优惠券，3汇总类费用
   */
  type?: number | null;
  oldPrice?: FeeSubItem | null;
  newPrice?: FeeSubItem | null;
  /**
   * 是否是必选的费用
   */
  fixed?: boolean | null;
}

export interface EarlyReturnPriceInfo {
  /**
   * 原始总价
   */
  originTotalFee?: number | null;
  newTotalFee?: number | null;
  /**
   * 违约金比例
   */
  penaltyRate?: number | null;
  /**
   * 需要退补款金额，负数就是退款
   */
  payAmount?: number | null;
  /**
   * 违约金金额
   */
  penaltyAmount?: number | null;
}

export interface IFeeInfo {
  feeList: FeeItem[];
  priceInfo: EarlyReturnPriceInfo;
}

export interface DailyPrice {
  date: string; // 日期
  priceStr: string; // 展示金额
  oDprice: string; // 原天价
  showType: number; // 展示类型（前端样式用） 0 默认宽度， 1 day...
}
export interface OldPriceType {
  subTitle?: string;
  amount?: number;
  localCurrencyCode?: string;
  dPriceDesc?: string;
  hourDesc?: string;
  priceDailys: Array<DailyPrice>;
}

export interface FeeInfoListType {
  serviceCode?: string;
  title?: string;
  subTitle?: string;
  fieldStyle?: string;
  changeType?: number;
  type?: number;
  oldPrice?: OldPriceType;
  newPrice?: OldPriceType;
  isBoldBottomBorder?: boolean;
}

export interface PackageTipsType {
  title?: string;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: number;
  subList?: Array<SubListType2>;
}

export interface EasyLifeTagType {
  code?: string;
  title?: string;
  type?: number;
  description?: string;
}

export interface InsuranceStatusName {
  title?: string;
  color?: DetailsItemLabelColorCode;
}

export interface IInsNameAndExcess {
  code?: string;
  name: string;
  groupCode?: string;
  excessShortDesc: string;
  excessShortDescNew?: string;
  isZeroExcess?: boolean;
  label?: string;
  labelDescription?: string;
  description?: string;
  isFromCtrip?: boolean;
  isHasExcess?: boolean;
  isInclude?: boolean;
  modalLabel?: Array<string>;
  insuranceStatus?: number;
  insuranceStatusName?: InsuranceStatusName;
  insuranceStatusDesc?: string;
  giveUp?: boolean;
  itemUrl?: string;
}

export interface ProductTraceData {
  vendorCode?: string;
  vendorId?: string;
  vendorName?: string;
  vehicleCode?: string;
  pStoreCode?: string;
  rStoreCode?: string;
  ifCalabi: boolean;
  pStoreId?: string;
  rStoreId?: string;
  skuId?: string;
  packageSellingRuleId?: string;
}

export interface PackageInfosType {
  insPackageId?: number;
  packageName?: string;
  currencyCode?: string;
  defaultBomCode?: string;
  defaultPackageId?: string;
  groupCode?: string;
  guaranteeDegree?: number;
  naked?: boolean;
  insuranceNames?: Array<string>;
  lowestDailyPrice?: number;
  gapPrice?: number;
  stepPrice?: number;
  subType?: number;
  descTitle?: string;
  description?: string;
  noticeDescTitle?: string;
  packageTips?: Array<PackageTipsType>;
  easyLifeTag?: Array<EasyLifeTagType>;
  insNameAndExcess: Array<IInsNameAndExcess>;
  unIncludeInsNameAndExcess?: Array<IInsNameAndExcess>;
  allInsNameAndExcess?: Array<IInsNameAndExcess>;
  summaryInsNameAndExcess?: Array<IInsNameAndExcess>;
  isHasExcess?: boolean;
  groupInsNameAndExcess?: any;
  logInfo?: {
    guaranteePkgName: string;
    insuranceId: Array<string>;
    vendorCode: number;
    groupName: string;
  };
  labels?: Array<any>;
}

export interface IInsurancePrice {
  price: number;
  currency: string;
}

export interface IDetailsItemLabel {
  label?: string;
  colorCode?: DetailsItemLabelColorCode;
}

export interface IDetailsItem {
  text: string;
  excessDesc?: string;
  isZeroExcess?: boolean;
  isHasExcess?: boolean;
  isInclude?: boolean;
  label?: string;
  labelDescription?: string;
  code?: string;
  description?: string;
  isFromCtrip?: boolean;
  isModal?: boolean;
  modalLabel?: Array<string>;
  highLight?: boolean;
  detailsColor?: string;
  highLightColor?: string;
  isQuestionIcon?: boolean;
  isFirst?: boolean;
  role?: PageRole;
  insuranceStatus?: InsuranceStatus;
  insuranceStatusName?: InsuranceStatusName;
  insuranceStatusDesc?: string;
  giveUp?: boolean;
  itemUrl?: string;
  isOSDInsurance2?: boolean;
  onPress?: () => void;
  openExcessIntroduceModal?: () => void;
  onAnchorInsuranceDetail?: (code) => void;
  onInsuranceElectronicDetail?: (code, url) => void;
}

interface LabelsItem {
  title: string;
  description: string;
  code: string;
  type: number;
  typeDesc: string;
  sortNum: number;
}

export interface IInsuranceItemContent {
  descTitle?: string;
  description?: string;
  noticeDescTitle?: string;
  isInclude?: boolean;
  insNameAndExcess?: Array<IInsNameAndExcess>;
  packageTips?: Array<PackageTipsType>;
  subType?: EasylifeType;
  isModal?: boolean;
  role?: PageRole;
  labels?: Array<LabelsItem>;
  onPressDetail?: () => void;
  openExcessIntroduceModal?: () => void;
  onAnchorInsuranceDetail?: (code) => void;
  onInsuranceElectronicDetail?: (code, url) => void;
}

export interface IPackageDesc {
  descTitle?: string;
  description?: string;
  subType?: EasylifeType;
  noticeDescTitle?: string;
}

export interface IExcessIntroduceModal {
  visible: boolean;
  onCancel: () => void;
  excessIntroduce?: string;
  excessIntroduceNew?: string;
  excessIntroduceBgOneNew?: string;
  excessIntroduceBgTwoNew?: string;
  excessIntroduceDesc?: string;
}

export interface IInsuranceNoticeMustReadModal {
  visible: boolean;
  onCancel: () => void;
  insuranceNotice?: string;
}

export interface IInsuranceReminderEnglishModal {
  visible: boolean;
  onCancel: () => void;
  englishContent?: string;
}

export type AddInstructDataType = {
  title: string;
  content: string;
};

export interface AllOperationsType {
  operationId?: number;
  buttonName?: string;
  enable?: boolean;
  display?: string;
  code?: ModifyOrderAllOperationsCodeType;
  label?: string;
  url?: string;
  disableCode?: number;
}

export interface DescDetailType {
  title?: string;
  desc?: string;
}

export interface FeeListType {
  priceCode?: string;
  priceName?: string;
  shortDesc?: string;
  priceDesc?: string;
  amount?: number;
  quantity?: number;
  descdetail?: Array<DescDetailType>;
}

export interface IsdFeeInfoType {
  salesAmount?: number;
  actualAmount?: number;
  noPayAmount?: number;
  firstPayAmount?: number;
  totalAmount?: number;
  orderAmount?: number;
  extraAmount?: number;
  deductAmount?: number;
  rebackAmount?: number;
  precar?: number;
  prepeccancy?: number;
  priceType?: number;
  rateCode?: string;
  rateCategory?: string;
  isWholeday?: number;
  feeList?: Array<FeeListType>;
  preAuthDesc?: string;
  preAuthAmount?: number;
  preAuthDisplay?: number;
  totalRentalPrice?: number;
  rentCardTotalFee?: number;
  exceedTenancy?: number;
  exceedPrice?: number;
  dailyPrice?: number;
}

export interface StatusProps {
  payCountDownTimeOut?: () => void;
  vendorInfo?: any;
  isOrderDataByPhone?: boolean;
  pageId?: string;
  showConsultProgressModal?: () => void;
  showPhoneModal?: (type?: any) => void;
  showCancelPenaltyModal?: () => void;
  showOrderRefundDetailModal?: () => void;
  showAdvanceReturnModal?: () => void;
  productDetails?: any;
  isVehicleStatusPolling: boolean;
  claimProcessModalData?: any;
  policyList?: any;
  gotoPolicy?: (policySelectedId?, labName?) => void;
  onLayout?: (e: LayoutChangeEvent) => void;
}

interface ExtType {
  orderInfo?: {
    ctype?: string; // 内容类型ORD: 订单,PRD: 产品,UDF: 用户自定义,
    cid?: string; // 内容类型对应的具体值为ORD时传订单ID，为PRD时传产品ID，为UDF时传自定义内容ID,
    desc?: string; // 订单/产品描述（根据ctype传递对应的描述）,
    title?: string; // 标题,
    amount?: string; // 价格,
    currency?: string; // 币种（三字码）
    // 业务三字码，如FLT（国内机票） FLIT（国际机票） HTL（国内酒店） 具体的三字码业务按照业务线自己的传值，IM会和后端进行透传
    bu?: string; // channel字段，同一业务线下区分场景使用，业务自定义自己识别即可
    /** 以下字段App 8.0及以上支持解析使用** */
    status?: string; // 订单卡片上要显示的状态，比如火车票 已停运
  };
  cardInfo?: {
    // 非必传
    desc?: string; // 订单/产品描述（根据ctype传递对应的描述）,
    title?: string; // 标题,
    actionList?: [
      {
        // 卡片上外露操作按钮列表
        actionButton?: string; // 按钮标题
        actionParam?: string; // 按钮绑定的url链接
        actionType?: number; // 按钮类型，1：代表点击后跳转到actionParam对应的页面，2：点击后以IM卡片消息的形式发出去，actionParam作为卡片消息跳转url
      },
    ];
  };
}

interface ExtendInfoType {
  oldChatUrl?: string;
  [key: string]: any;
}

export interface ImParamType {
  /**
   * 公共字段
   */
  isPreSale?: number; // 0:代表售后 1代表售前
  bizType?: number;
  pageId?: string; // 用来区分业务入口场景的标识，必须维护在cms系统中
  ext?: ExtType; // JsonStr（扩展字段,订单相关内容）Base64编码替换
  thirdPartytoken?: string; // 用于传递业务需要传递给后端的一些业务参数KEY，目前适用于度假，租车，酒店ebk场景
  /**
   * 自定义字段
   */
  orderId?: string;
  extendInfo?: ExtendInfoType;
}
export interface ImageObject {
  imageUrl: string;
  /**
   * 与图片同级的 ReactElement,
   * 方便插入一些 label
   */
  slotDom?: any;
}
interface TableContent {
  title?: string;
  desc?: string;
}
interface TableItem {
  rowIndex?: string;
  columnIndex?: string;
  content?: string;
}
export interface PolicyTableProps {
  tableHead?: string;
  content?: TableContent[];
  items?: TableItem[];
  theme?: any;
}

export interface StringObjs {
  content?: string;
  style?: string;
  url?: string;
  table?: PolicyTableProps;
  imgList?: ImageObject[];
  htmlText?: string;
}

export interface IProps {
  contentObject?: ContentObjectItem[];
  txtStyle?: CSSProperties;
  ctStyle?: CSSProperties;
}

export interface ContentObjectItem {
  contentStyle?: string;
  stringObjs?: StringObjs[];
  style?: string;
}

export interface TextItemProps {
  title: string;
  content?: string;
  contentObject?: ContentObjectItem[];
  style?: CSSProperties;
}

export interface LabelsType {
  title?: string;
  subTitle?: string;
  code?: string;
  type?: number;
  grade?: number;
  sortNum?: number;
}

export interface ItemsType {
  title?: string;
  subTitle?: string;
  description?: string;
  complexSubTitle?: ComplexSubTitleType;
  contents?: Array<ComplexSubTitleType>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  tableTitle?: string;
  freezeDeposit?: boolean;
  showCreditCard?: boolean;
  activityCode?: string;
  discountType?: number;
}

export interface ItemsType2 {
  title?: ComplexSubTitleType;
  subTitle?: string;
  description?: string;
  complexSubTitle?: ComplexSubTitleType;
  contents?: Array<ComplexSubTitleType>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  items?: Array<ItemsType>;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  tableTitle?: string;
  freezeDeposit?: boolean;
  showCreditCard?: boolean;
  activityCode?: string;
  discountType?: number;
}

export interface StringObjsType {
  content?: string;
  style?: string;
  url?: string;
}

export interface ComplexSubTitleType {
  contentStyle?: string;
  stringObjs?: Array<StringObjsType>;
}

export interface EhiNoteInfoProps {
  title?: string;
  button?: { title?: string };
  contents?: ComplexSubTitleType[];
  items?: ItemsType2[];
  table?: ItemsType2[];
}

export interface StringObjsType {
  content?: string;
  style?: string;
  url?: string;
}

export interface ComplexSubTitleType {
  contentStyle?: string;
  stringObjs?: Array<StringObjsType>;
}

export interface PressButtonExtrasParams {
  day?: number; // 续租天数
  nodeType?: number; // 履约节点类型
  promptMessage?: string; // 弹窗提示信息
}

export interface LocalContactInfoType {
  logo?: string;
  title?: string;
  contactType: ContactType;
  isSelected: boolean;
  data: string;
  icon?: string;
  type?: number;
  isMask?: boolean;
}

export interface ISelectedItem {
  code: string;
  groupCode: string;
  isSelected: boolean;
  name: string;
}

export interface ISelectedResultParams {
  selectedResult?: ISelectedItem[];
  callback?: () => void;
}

export interface ISelectedItem {
  code: string;
  groupCode: string;
  isSelected: boolean;
  name: string;
}

export interface ISelectedResultParams {
  selectedResult?: ISelectedItem[];
  callback?: () => void;
}

export interface ILocation {
  name: string;
  status: number;
  statusName: string;
  firstChar: string;
  regionId: string;
  isSelected?: boolean;
  policy?: string | null;
}

export interface ISelectedDetail {
  locations: string;
  note: string;
}

export interface ISelectedResultItem {
  title: string;
  details: ISelectedDetail[];
  isFirst?: boolean;
  isLast?: boolean;
}

export interface ITravelLimitItem {
  title?: string;
  subTitle?: string;
  crossType: number;
  crossTypeName: string;
  summaryTitle: string;
  summaryPolicies?: string[];
  locations?: ILocation[];
  selectedResult?: ISelectedResultItem[];
  isFirst?: boolean;
  logBaseInfo?: any;
  onSelectCountry?: () => void;
}

export interface ITravelLimit {
  title?: string;
  notes?: string[];
  crossLocationsInfos?: ITravelLimitItem[];
  selectedResult?: any[];
  setTravelLimitSelectedResult?: (data: ISelectedItem[]) => void;
  setOrderDetailTravelLimitSelectedResult?: (
    data: ISelectedResultParams,
  ) => void;
  onLayout?: (data) => void;
  logBaseInfo?: any;
  moduleTitle?: string;
  style?: CSSProperties;
}

export interface IFeeDetailOsd {
  visible?: boolean;
  onClose?: () => void;
  data?: any;
  footerChildren?: any;
  onPressBtn?: () => void;
  isLogin?: boolean;
  role?: PageRole;
  showExplainModal?: (feeTitle: string, feeTitleExplain: string) => void;
  productBaseLogInfo?: any;
  testID?: string;
}

export enum MaterialsType {
  CarRentalMustRead,
  PickupMaterials,
}

export interface IFilterItem {
  name: string;
  code: string;
  isSelected: boolean;
}

export type IitemInfo = {
  type: cardType; // 1 机票 2 酒店 3火车票
  typeLabel: string;
  fromCity: string;
  toCity: string;
  itemName: string; // 酒店名称
  isRoundTrip: boolean; // 是否往返
  dateList: Array<string>;
  itemNames?: Array<string>; // 名称-兼容多行展示
};

export type IpointInfo = {
  cityId: number;
  cityName: string;
  locationType?: number;
  locationCode?: number;
  locationName: string;
  date: string;
  poi: {
    longitude: number;
    latitude: number;
  };
  countryName?: string;
  isDomestic?: string;
};

export interface ItravelItems {
  itemInfo: IitemInfo;
  pickupPoint: IpointInfo;
  returnPoint: IpointInfo;
}

export interface INewSearchPanel {
  style?: CSSProperties;
  ptime?: Date;
  rtime?: Date;
  pcity?: ILocation;
  rcity?: ILocation;
  showDropoff?: boolean;
  isShowFilterItems?: boolean;
  timeLineStyle?: any;
  filterItems?: Array<IFilterItem>;
  timeWarning?: string;
  insufficientTimeWarning?: string;
  adultSelectNum?: number;
  childSelectNum?: number;
  age?: string;
  isCrossCRNContainer?: boolean;
  isShowLocationBar?: boolean;
  hasLocationPermission?: boolean;
  searchBtnLottieJson?: string;
  searchButtonText?: string;
  searchBtnTextIcon?: string;
  searchBtnTextIconStyle?: CSSProperties;
  searchBtnBg?: string;
  adultMaxNum?: number;
  isShowItineraryCard?: boolean;
  isLoadingItineraryCardInfo?: boolean;
  selectedItineraryCard?: ItravelItems;
  travelItems?: ItravelItems[];
  isShowShadow?: boolean;
  searchPanelButtonType?: string;
  isShowAdvantage?: boolean;
  isHideAutonomy?: boolean;
  onTimeChange: (data: { ptime: string; rtime: string }) => void;
  onIsShowDropOffChange: (data: boolean) => void;
  onPressFilter?: (item: IFilterItem, index: number) => void;
  onPressSearch: () => void;
  onAgeChange: (data) => void;
  setDateInfo?: (data: { pickup: Date; dropoff: Date }) => void;
  setLocationInfo?: (data: any) => void;
  onPressPosition?: () => void;
  onPressCity?: (isPickUp: boolean) => void;
  onPressLocation?: (isPickUp: boolean) => void;
  updateSelectedItineraryCard?: (data: any) => void;
  onPressPickUpDate?: () => void;
  onPressDropOffDate?: () => void;
  onPressRentalTime?: () => void;
  onPressNumberSelect?: () => void;
  onPressAgeSelect?: () => void;
  onPressAgeTip?: () => void;
  onAgeTipClose?: () => void;
  onNumberCancel?: () => void;
  onAgeCancel?: () => void;
  setPickType?: (data: { pickType: string }) => void;
  handleAreaPress?: (data) => void;
  isOsd?: boolean;
  searchBtnStyle?: CSSProperties;
}

export interface IbkSearchPanelModal extends INewSearchPanel {
  onCancel?: () => void;
  visible?: boolean;
  useCRNModal?: boolean;
  isHideAutonomy?: boolean;
  location?: string;
  animateType?: string;
  wrapperStyle?: CSSProperties;
  closeIconStyle?: CSSProperties;
  headerStyle?: CSSProperties;
  headerText?: string;
  headerTextStyle?: CSSProperties;
  tip?: React.ReactNode;
}

export type GetLocationUrlFunction = (data: {
  pickType: 'pickup' | 'dropOff';
  pageType: 'City' | 'Area';
  cityId?: string;
  cityName?: string;
  areaId?: string;
  areaName?: string;
  isHideAutonomy?: boolean;
  isIsd?: boolean;
  filterPoi?: any;
}) => string;

export interface IStateType {
  lang?: string;
  messages?: any;
}

export interface IBookingTerms {
  content?: string;
  onPressTandC: () => void;
}

export interface IDepositDescriptionType {
  title: string;
  content?: string;
  description: string;
  isShowFree: boolean;
  positiveDesc?: string;
  isShowQuestion: boolean;
  onPressQuestion?: () => void;
  creditCardImgList?: string[];
  wrapStyle?: CSSProperties;
  index?: number;
}

export interface ICarCenterLabel {
  title?: string;
  soldOutTextColor?: any;
  marginBottom?: number;
}

export interface IEasyLifeLabel {
  title?: string;
  soldOutTextColor?: any;
  marginBottom?: number;
}

export interface IThemeConfig {
  bookingBg?: string;
  privilegeBg?: string;
  serviceBg?: string;
  homeBg?: string;
  // 融合首页
  bookingCombineBg?: string;
  privilegeCombineBg?: string;
  serviceCombineBg?: string;
  homeCombineBg?: string;
  searchBtnBg?: string;
  searchBtnShadowBg?: string;
  searchBtnLottieJsonURL?: string;
  footerBg?: string;
  footerBgX?: string;
  footerSelectedColor?: string; // 底部字体选中颜色
  footerUnSelectedColor?: string; // 底部字体未选中颜色
  footerBarCustomProcess?: (data: any[]) => void;
  vendorListBg?: string; // 供应商列表页背景图片
}

export interface SubObjectType {
  title?: string;
  subTitle?: string;
  content?: Array<string>;
  type?: number;
  urlName?: string;
  url?: string;
  note?: string;
}

export interface SubObjectType2 {
  title?: string;
  subTitle?: string;
  content?: Array<string>;
  type?: number;
  urlName?: string;
  url?: string;
  subObject?: Array<SubObjectType>;
  note?: string;
}
