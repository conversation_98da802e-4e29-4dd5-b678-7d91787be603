// 证照B版订单 跳转驾照翻译件列表 18862/OSDQueryOrder
export const fyjRes = {
  ResponseStatus: {
    Timestamp: '2024-06-07 16:52:29',
    Ack: 'Success',
    Errors: [],
    Extension: [
      {
        Id: 'CLOGGING_TRACE_ID',
        Value: '1401178138905393883',
      },
      {
        Id: 'RootMessageId',
        Value: '921822-0a901051-477152-50020',
      },
    ],
  },
  orderBaseInfo: {
    orderId: 70006698452,
    uId: 'M2236692079',
    channelType: '17671',
    orderDate: 1715752434000,
    orderLocale: 'zh-CN',
    orderStatus: 2,
    orderStatusDesc: '已确认',
    orderStatusName: '已确认',
    orderStatusCtrip: 'CAR_CONFIRMED',
    allStatuses: [],
    allOperations: [
      {
        operationId: 4,
        buttonName: '再次预订',
        enable: true,
      },
      {
        operationId: 3,
        enable: false,
        code: 4,
      },
      {
        operationId: 2,
        buttonName: '取消订单',
        enable: true,
        contents: [
          {
            description:
              '海外租车价格库存随时存在波动，重新预订价格可能会上涨，请你再次确认是否取消？',
          },
        ],
      },
    ],
    orderTip: {
      tipType: 3,
      tipContent: '',
      tipContentArray: ['已为您预留车辆，取车时请记得携带身份证件'],
      warnType: 0,
    },
    payMode: 2,
    extOperation: [
      {
        operationId: 17,
        buttonName: 'Resend Confirmation Email',
        enable: true,
      },
    ],
  },
  continuePayInfo: {
    needContinuePay: false,
  },
  orderPriceInfo: {
    packageType: 3,
    currentTotalPrice: 1592.0,
    currentCurrencyCode: 'CNY',
    localTotalPrice: 229.16,
    localCurrencyCode: 'USD',
    payMode: 2,
    payModeDesc: '在线支付',
    prepayPrice: {
      title: '',
      totalPrice: 1592.0,
      currencyCode: 'CNY',
    },
    localPrice: {
      title: '',
      totalPrice: 0.0,
      currencyCode: 'USD',
    },
    prepayPriceDetails: [
      {
        title: 'shark|common_carHireFee',
        totalPrice: 1592.0,
        currencyCode: 'CNY',
      },
    ],
    localPriceDetails: [],
    priceDetails: [
      {
        title: 'shark|common_carHireFee',
        totalPrice: 1658.0,
        currencyCode: 'CNY',
      },
    ],
    payAmount: 1592.0,
    couponAmount: 66.0,
    coupons: [
      {
        couponCode: 'myjfehjjcd',
        promotionId: 565853359,
        deductionAmount: 66.0,
        displayName: '立减券',
      },
    ],
  },
  vehicleInfo: {
    vehicleName: '克莱斯勒  Pacifica',
    special: false,
    passengerNum: 7,
    luggageNum: 2,
    hasAC: false,
    transmission: 'AT',
    vehicleGroupName: '迷你商务车',
    vendorVehicleCode: 'MVAR_ChryslerPacifica_563acba7',
    imageUrl: 'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
    vehicleDisplacement: '',
    displacement: '',
    doorNum: 4,
    fuelType: '3',
    vehicleGroupId: 5,
    fourDrive: false,
  },
  vendorInfo: {
    vendorName: 'Budget',
    vendorImageUrl:
      '//pic.c-ctrip.com/car/osd/ctrip/vendor_logo/budget_2020.png',
    confirmDate: 1715882056000,
    confirmDateStr: '2024-05-17 01:54',
    platformCode: '',
    platformName: '编辑公司信息-测试供应商',
    bizVendorCode: '14016',
    commentInfo: {
      vendorGoodType: 1,
      exposedScore: 0.0,
      topScore: 5.0,
      commentLabel: '',
      commentCount: 0,
      hasComment: 0,
      link: '/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=78944&calabiVehicleId=550740&vehicleName=null&productCategoryId=35&isHideNavBar=YES',
    },
    broker: false,
  },
  pickupStore: {
    localDateTime: '2024-06-25 10:00:00',
    storeName: 'BOSTON LOGAN INTL AIRPORT',
    storeCode: 'BOST01',
    storeAddress: '15 TRANSPORTATION WAY,LOGAN INTERNATIONAL AIRPORT,02128',
    longitude: -71.029167,
    latitude: 42.381944,
    storeGuide:
      '在航班到达层提取行李后，前往位于航站楼外的shuttle bus站，乘坐免费巴士到达租车中心办理取车手续。',
    storeLocation: '门店位于洛干国际机场内',
    storeTel: '************',
    storeOpenTimeDesc: '{"":"24小时营业"}',
    cityName: '波士顿',
    provinceName: '马萨诸塞州',
    countryName: '美国',
    userSearchLocation: '洛干国际机场',
    storeID: 78944,
    location: {
      locationType: 1,
      locationName: '洛干国际机场',
      locationCode: 'BOS',
      continent: {
        id: 66,
        name: '美国',
      },
      country: {
        id: 66,
        name: '美国',
      },
      province: {
        id: 10179,
        name: '马萨诸塞州',
      },
      city: {
        id: 26848,
        name: '波士顿',
      },
      poiInfo: {
        latitude: 42.365613,
        longitude: -71.00956,
        type: 1,
      },
    },
    disclaimer: 'The above information is provided by the branch',
    localDateTimeDTO: {
      date: '2024年6月25日',
      time: '10:00',
    },
    countryId: 66,
  },
  returnStore: {
    localDateTime: '2024-06-27 10:00:00',
    storeName: 'BOSTON LOGAN INTL AIRPORT',
    storeCode: 'BOST01',
    storeAddress: '15 TRANSPORTATION WAY,LOGAN INTERNATIONAL AIRPORT,02128',
    longitude: -71.029167,
    latitude: 42.381944,
    storeGuide:
      '在门店停车场附近的道路旁通常能看到“Rental Car Return”的指示牌，按照指引将车辆驶入到对应的停车场办理还车手续，随后搭乘租车公司免费接驳巴士返回机场即可。',
    storeLocation: '门店位于洛干国际机场内',
    storeTel: '************',
    storeOpenTimeDesc: '{"":"24小时营业"}',
    cityName: '波士顿',
    provinceName: '马萨诸塞州',
    countryName: '美国',
    userSearchLocation: '洛干国际机场',
    storeID: 78944,
    location: {
      locationType: 1,
      locationName: '洛干国际机场',
      locationCode: 'BOS',
      continent: {
        id: 66,
        name: '美国',
      },
      country: {
        id: 66,
        name: '美国',
      },
      province: {
        id: 10179,
        name: '马萨诸塞州',
      },
      city: {
        id: 26848,
        name: '波士顿',
      },
      poiInfo: {
        latitude: 42.365613,
        longitude: -71.00956,
        type: 1,
      },
    },
    disclaimer: 'The above information is provided by the branch',
    localDateTimeDTO: {
      date: '2024年6月27日',
      time: '10:00',
    },
    countryId: 66,
  },
  driverInfo: {
    name: '16ed5c834a9f587e89944abf9e7b3f278fd1f25cfa3361f7ae3bdda700c13593',
    age: '29',
    email: '106e316c6592e3f0bf026eae901d4331df2391c8cbf288aa9118b5d9a2bc4337',
    telphone:
      'cc8459cd646c817151fec4c6dc8ce2588191b1be27a0afac9622de681bf67a3b',
    areaCode: '86',
    flightNo: '',
    decryptTelphone: '13657229573',
    decryptMail: '<EMAIL>',
    lastName:
      '7bca63591d25a8521cd6369ca4d459388e5d0462cf33fbf1f84015786b57d4b3',
    firstName:
      '0369a0d64cc2837bac219ed50fcd0b1a4bcd62527353717014c9d17c570ccf02',
  },
  extraInfos: [],
  cancelRuleInfo: {
    cancelTip: 'This booking can be canceled',
    longTitle: 'In the following circumstances, you will not receive a refund:',
    ruleList: [
      '- You pick up the car late or drop it off early',
      '- You do not pick up the car within the given timeframe',
      '- You do not provide the required documents when picking up the car',
      "- The main driver's credit card does not have sufficient funds",
    ],
    cancelReasons: [
      '行程变更/取消',
      '修改订单',
      '重复下单',
      '车不能跨境跨岛',
      '信用卡问题',
      '驾照证件问题',
      '其他网站更便宜',
      '其他',
    ],
    cancelTipColor: 1,
    modifyTip: {
      title: 'Booking cannot be modified',
      desc: 'If you need to make changes, cancel it and rebook',
    },
    osdCancelRuleInfo: {
      title: '取消政策',
      subTitle: '支付完成至2024-06-22 12:00可免费取消',
      complexSubTitle: {
        contentStyle: '1',
        stringObjs: [
          {
            content: '支付完成至2024-06-22 12:00可免费取消',
            style: '5',
          },
          {
            content: '，',
            style: '6',
          },
          {
            content: '支付完成至2024-06-22 12:00可免费取消',
            style: '6',
          },
        ],
      },
      code: 'FreeCancel',
      type: 300,
      items: [
        {
          title: '支付完成至2024-06-22 12:00',
          subTitle: '支付完成至取车前70小时',
          description: '可免费取消',
          showFree: true,
          key: '339921',
          lossFee: 0,
        },
        {
          title: '2024-06-22 12:00后',
          subTitle: '取车前70小时后',
          description: '取消将收取全部租金作为违约金，最多收取¥500.00',
          showFree: false,
          key: '339928',
          lossFee: 1592,
        },
      ],
      description: '注意：均为当地时间',
      showFree: true,
    },
    customerCurrency: 'CNY',
  },
  refundProgressList: [],
  baseResponse: {
    isSuccess: true,
    code: '200',
    returnMsg: 'success',
    requestId: 'becf5b66-3deb-4198-947a-b952aaf0b6c5',
    cost: 1565,
  },
  additionPaymentInfo: {
    additionalPaymentList: [
      {
        orderId: 1128168945062119,
        amount: 130,
        reason: '订单取消违约金',
        payStatus: 0,
        toPayCount: 130,
        toPayAmount: 130,
        additionalPaymentId: 47629,
        bizScene: 4,
      },
    ],
  },
  jumpModifyToPay: true,
  packageInfos: [
    {
      insPackageId: 3,
      isDefault: true,
      packageName: '基础套餐',
      currencyCode: 'USD',
      defaultBomCode: '',
      defaultPackageId: 341615,
      guaranteeDegree: 0.0,
      naked: false,
      lowestDailyPrice: 0,
      gapPrice: 0,
      stepPrice: 0,
    },
  ],
  productDetails: [
    {
      insPackageId: 3,
      insuranceItems: [
        {
          productId: 341615,
          title: '车辆碰撞保障',
          description: '保障车辆碰撞损失',
          code: 'CDW',
          type: 7,
          name: '车辆碰撞保障',
          isInclude: true,
          isFromCtrip: false,
          insuranceDetail: [
            {
              packageId: 341615,
              currencyCode: 'USD',
              minExcess: 0.0,
              maxExcess: 0.0,
            },
          ],
          converageExplain: {
            title: '承保范围',
            content: [
              '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后',
            ],
          },
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '发生事故后报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          excessTable: {
            title: '起赔额 0起赔额',
            subObject: [
              {
                title: '车行承担',
                content: ['全部损失'],
              },
              {
                title: '客户或承租方承担',
                content: ['US$ 0'],
              },
            ],
          },
        },
        {
          productId: 341615,
          title: '车辆盗抢保障',
          description: '保障车辆被盗的损失',
          code: 'TP',
          type: 7,
          name: '车辆盗抢保障',
          isInclude: true,
          isFromCtrip: false,
          insuranceDetail: [
            {
              packageId: 341615,
              currencyCode: 'USD',
              minExcess: 0.0,
              maxExcess: 0.0,
            },
          ],
          converageExplain: {
            title: '承保范围',
            content: [
              '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '不承保范围：\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '发生事故后报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          excessTable: {
            title: '起赔额 0起赔额',
            subObject: [
              {
                title: '车行承担',
                content: ['全部损失'],
              },
              {
                title: '客户或承租方承担',
                content: ['US$ 0'],
              },
            ],
          },
        },
      ],
      insuranceDesc: ['呃呃呃'],
      productInfoList: [
        {
          minPackageItmes: [
            {
              title: '不限里程',
              type: 2,
              code: 'ULM',
              description: '租期内没有公里数限制。\n',
            },
            {
              title: '满油取还',
              type: 4,
              code: 'FRFB_OIL',
              description:
                '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
            },
          ],
        },
      ],
      claimsProcess: [],
      extraDesc: [
        '',
        '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比',
        '',
        '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比',
      ],
      briefInsuranceItems: [
        {
          name: '碰撞盗抢保障',
          isInclude: true,
          isFromCtrip: false,
          insuranceDetail: [
            {
              minExcess: 0.0,
              maxExcess: 0.0,
              excessShortDesc: '起赔额US$ 0',
              coverageWithoutPlatformInsuranceV2: '起赔额US$ 0',
            },
          ],
        },
      ],
    },
  ],
  extendedInfo: {
    flightDelayRule: {
      title: '航班延误保留政策',
      description: '若航班延误，门店将不保留车辆，请留取充足的时间取车',
      rules: [
        {
          title: '若航班延误，门店将不保留车辆，请留取充足的时间取车',
        },
        {
          title: '建议您填写航班号，如遇延误请携带机票前往取车',
        },
      ],
      delayStatus: 0,
    },
    klbVersion: 1,
    pickUpMaterials: [
      {
        title: '取车要求',
        content: ['驾驶员年龄25-65周岁，且持有驾照至少满12个月'],
        summaryContent: [
          '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
        ],
        type: 20,
        summaryContentObject: [
          {
            contentStyle: 'age',
            stringObjs: [
              {
                content: '25-65周岁',
              },
            ],
          },
          {
            contentStyle: 'licenseAge',
            stringObjs: [
              {
                content: '≥12个月',
              },
            ],
          },
        ],
        summaryTitle: '驾驶员需持有本人名下4项材料取车',
      },
      {
        title: '护照原件',
        subTitle: '',
        type: 0,
        subObject: [
          {
            title: '中国大陆护照',
            subTitle: '中国大陆护照原件',
            code: 'CN',
            subObject: [
              {
                title: '护照原件',
                content: [
                  '主驾驶员和其他额外驾驶员需提供他们名下的护照原件',
                  '护照签发地需与驾照发证国家/地区一致',
                ],
                type: 21,
              },
            ],
          },
        ],
      },
      {
        title: '驾照组合',
        content: [
          '你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可',
          '持有不同护照需要驾照组合要求可能不同',
        ],
        type: 1,
        subObject: [
          {
            type: 5,
            subObject: [
              {
                title: '门店支持以下驾照组合(任选其一)',
                type: 5,
                code: 'CN',
                subObject: [
                  {
                    title: '中国驾照原件 + 驾照国际翻译认证件',
                    content: [
                      '中国驾照原件：中国驾照原件',
                      '驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。',
                    ],
                    type: 22,
                    code: 'CDL,IDL',
                    sortNum: 2,
                    optimalType: '1',
                    status: '1',
                  },
                  {
                    title: '中国驾照原件 + 车行翻译件',
                    content: [
                      '中国驾照原件：中国驾照原件',
                      '车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 ',
                    ],
                    type: 22,
                    code: 'CDL,DLT',
                    url: 'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/driverlicenseTranslatePage?isHideNavBar=YES',
                    sortNum: 3,
                    status: '2',
                  },
                  {
                    title: '中国驾照原件 + 英文公证件 + 当地语言公证件',
                    content: [
                      '中国驾照原件：中国驾照原件',
                      '英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件',
                      '当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件',
                    ],
                    type: 22,
                    code: 'CDL,OET,OLT',
                    url: 'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/languagelicensePage?isHideNavBar=YES',
                    sortNum: 4,
                    status: '2',
                  },
                  {
                    title: '中国驾照原件 + 当地语言公证件',
                    content: [
                      '中国驾照原件：中国驾照原件',
                      '当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件',
                    ],
                    type: 22,
                    code: 'CDL,OLT',
                    url: 'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/languagelicensePage?isHideNavBar=YES',
                    sortNum: 5,
                    status: '2',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        title: '现金',
        summaryContent: ['现金支付说明（中）', '该门店不收取押金'],
        type: 18,
        subObject: [
          {
            title: '现金支付说明',
            content: ['现金支付说明（中）'],
            type: 19,
            summaryContentObject: [
              {
                contentStyle: 'SupportCreditCard',
                stringObjs: [
                  {
                    content: '0',
                  },
                ],
              },
            ],
          },
          {
            title: '押金说明',
            content: ['该门店不收取押金'],
            type: 8,
            table: [
              {
                title: '押金',
                description: '该门店不收取押金',
                showFree: false,
              },
            ],
          },
        ],
      },
      {
        title: '提车凭证',
        content: [
          '取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。',
        ],
        summaryContent: ['订单确认后，平台为您提供'],
        type: 3,
      },
    ],
    ctripInsuranceVersion: 'B',
    carAssistantSummary: [
      {
        content: '取车材料',
      },
      {
        content: '办理驾照翻译件',
      },
    ],
    osdDetailVersion: 'B',
    crossLocationsPolicy: {
      crossLocationsInfos: [
        {
          crossType: 3,
          locations: [
            {
              name: '安提瓜和巴布达',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'A',
              regionId: '201',
              isSelected: false,
            },
            {
              name: '安圭拉',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'A',
              regionId: '275',
              isSelected: false,
            },
            {
              name: '阿鲁巴',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'A',
              regionId: '171',
              isSelected: false,
            },
            {
              name: '巴巴多斯',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'B',
              regionId: '202',
              isSelected: false,
            },
            {
              name: '百慕大',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'B',
              regionId: '207',
              isSelected: false,
            },
            {
              name: '波多黎各',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'B',
              regionId: '208',
              isSelected: false,
            },
            {
              name: '伯利兹',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'B',
              regionId: '210',
              isSelected: false,
            },
            {
              name: '巴拿马',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'B',
              regionId: '18',
              isSelected: false,
            },
            {
              name: '巴哈马',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'B',
              regionId: '185',
              isSelected: false,
            },
            {
              name: '多米尼加共和国',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'D',
              regionId: '276',
              isSelected: false,
            },
            {
              name: '多米尼克',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'D',
              regionId: '217',
              isSelected: false,
            },
            {
              name: '法属圣马丁',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'F',
              regionId: '291',
              isSelected: false,
            },
            {
              name: '瓜德罗普岛',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'G',
              regionId: '261',
              isSelected: false,
            },
            {
              name: '格林纳达',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'G',
              regionId: '220',
              isSelected: false,
            },
            {
              name: '哥斯达黎加',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'G',
              regionId: '38',
              isSelected: false,
            },
            {
              name: '古巴',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'G',
              regionId: '39',
              isSelected: false,
            },
            {
              name: '荷兰加勒比区',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'H',
              regionId: '259',
              isSelected: false,
            },
            {
              name: '荷属圣马丁',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'H',
              regionId: '295',
              isSelected: false,
            },
            {
              name: '海地',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'H',
              regionId: '41',
              isSelected: false,
            },
            {
              name: '洪都拉斯',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'H',
              regionId: '44',
              isSelected: false,
            },
            {
              name: '加拿大',
              status: 2,
              statusName: '条件跨境',
              firstChar: 'J',
              policy:
                '车辆允许驶入加拿大，请在门店取车时告知跨境需求和地点以便门店工作人员为其准备跨境车辆所需要的绿卡（非居民保险卡，免费提供)',
              regionId: '47',
              isSelected: false,
            },
            {
              name: '开曼群岛',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'K',
              regionId: '223',
              isSelected: false,
            },
            {
              name: '库拉索岛',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'K',
              regionId: '294',
              isSelected: false,
            },
            {
              name: '墨西哥',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'M',
              regionId: '72',
              isSelected: false,
            },
            {
              name: '马提尼克',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'M',
              regionId: '279',
              isSelected: false,
            },
            {
              name: '美属维尔京群岛',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'M',
              regionId: '280',
              isSelected: false,
            },
            {
              name: '尼加拉瓜',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'N',
              regionId: '198',
              isSelected: false,
            },
            {
              name: '圣皮埃尔和密克隆岛',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'S',
              regionId: '289',
              isSelected: false,
            },
            {
              name: '圣巴泰勒米',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'S',
              regionId: '297',
              isSelected: false,
            },
            {
              name: '萨尔瓦多',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'S',
              regionId: '238',
              isSelected: false,
            },
            {
              name: '圣基茨和尼维斯',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'S',
              regionId: '243',
              isSelected: false,
            },
            {
              name: '圣卢西亚',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'S',
              regionId: '244',
              isSelected: false,
            },
            {
              name: '圣文森特和格林纳丁斯',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'S',
              regionId: '246',
              isSelected: false,
            },
            {
              name: '特克斯和凯科斯群岛',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'T',
              regionId: '265',
              isSelected: false,
            },
            {
              name: '特立尼达和多巴哥',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'T',
              regionId: '252',
              isSelected: false,
            },
            {
              name: '危地马拉',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'W',
              regionId: '90',
              isSelected: false,
            },
            {
              name: '英属维尔京群岛',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'Y',
              regionId: '282',
              isSelected: false,
            },
            {
              name: '牙买加',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'Y',
              regionId: '101',
              isSelected: false,
            },
            {
              name: '英属蒙塞拉特岛',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'Y',
              regionId: '299',
              isSelected: false,
            },
          ],
          crossTypeName: '跨境政策',
          summaryPolicies: ['若您的行程中涉及跨境，请提前选择'],
          title: '选择计划前往的国家',
          subTitle: '门店支持在以下区域跨境使用车辆：',
        },
        {
          crossType: 1,
          crossTypeName: '跨岛政策',
          summaryPolicies: [
            '由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨岛，建议您更换其它租车公司或车型组。',
          ],
        },
        {
          crossType: 2,
          crossTypeName: '跨州政策',
          summaryPolicies: [
            '由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨州/省，建议您更换其它租车公司或车型组。',
          ],
        },
      ],
      notes: [
        '若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。除特别说明的情况外，“跨境费用”为前往单个国家所需的费用。',
      ],
      title: '旅行限制',
    },
    sign: '2+NEdD828Z0Wik719zVkNTKXZJs=',
    promptInfos: [
      {
        title: '押金汇率说明',
        type: 12,
        contents: [
          {
            stringObjs: [
              {
                content:
                  '押金所需人民币按照授权免押时的汇率计算，押金实际所需人民币以授权免押时的金额为准',
              },
            ],
          },
        ],
      },
    ],
    carAssistantSummaryV2: [
      {
        title: '取车材料&租车指南',
        type: 1,
        url: 'https://www.fat10668.qa.nt.ctripcorp.com/carhire/materialsMustRead?orderId=70006698452&h5View=1&locale=zh_cn&orderStatus=CAR_CONFIRMED&vendorId=14016&countryId=66&isHideNavBar=YES&subEnv=fat11183',
      },
      {
        title: '提车凭证',
        type: 5,
        url: 'https://www.fat10668.qa.nt.ctripcorp.com/carhire/pickupVoucher?orderId=70006698452&h5View=1&locale=zh_cn&isHideNavBar=YES&subEnv=fat11183',
      },
      {
        title: '中英合同/验车单参照',
        type: 4,
        url: 'https://m.ctrip.com/tangram/ODAxNTg=?ctm_ref=vactang_page_80158&isHideNavBar=YES&apppgid=10650039393&statusBarStyle=1&channelid=237770',
      },
      {
        title: '自驾政策',
        type: 2,
        url: 'https://m.ctrip.com/tangram/ODAxNTg=?ctm_ref=vactang_page_80158&isHideNavBar=YES&apppgid=10650039393&statusBarStyle=1&channelid=237770',
      },
      {
        title: '取车时记得携带取车材料和提车凭证',
        type: 6,
      },
      {
        title: '03/27泰国自驾租车群',
        subTitle: '当地交规 客服答疑 车友交流',
        type: 3,
        button: {
          weChatUrl:
            'ctrip://wireless/login/openMiniProgram?username=gh_36ada103ba97&type=0&path=&edata=93714f221d4f38c34a489f221360657211346b8c337885172ae91f64ec9b5cab33a1441d252e11ea5339714a77459d730c674f042d5cf13ab5347f4d79c06149ff54bf47ff801b9a30ecb5a1e6719b74',
          title: '立即入群',
          h5Url:
            'https://m.fat28.qa.nt.ctripcorp.com/webapp/cars/marketing/bridgeWx?&edata=93714f221d4f38c34a489f221360657211346b8c337885172ae91f64ec9b5cab33a1441d252e11ea5339714a77459d730c674f042d5cf13ab5347f4d79c06149ff54bf47ff801b9a30ecb5a1e6719b74',
          appWeChatUrl:
            'ctrip://wireless/login/openMiniProgram?username=gh_36ada103ba97&type=0&path=Y3RyaXA6Ly93aXJlbGVzcy9sb2dpbi9vcGVuTWluaVByb2dyYW0/dXNlcm5hbWU9Z2hfMzZhZGExMDNiYTk3JnR5cGU9MCZwYXRoPSZlZGF0YT05MzcxNGYyMjFkNGYzOGMzNGE0ODlmMjIxMzYwNjU3MjExMzQ2YjhjMzM3ODg1MTcyYWU5MWY2NGVjOWI1Y2FiMzNhMTQ0MWQyNTJlMTFlYTUzMzk3MTRhNzc0NTlkNzMwYzY3NGYwNDJkNWNmMTNhYjUzNDdmNGQ3OWMwNjE0OWZmNTRiZjQ3ZmY4MDFiOWEzMGVjYjVhMWU2NzE5Yjc0',
          statusType: 1,
        },
      },
    ],
    attr: {
      ctripInsuranceVersion: 'B',
      isVehicle2: '1',
      osdConfirmNew: 'true',
      osdDetailVersion: 'A',
      voucherVersion: 'B',
    },
  },
  modifyInfoDto: {},
  insuranceAndXProductDesc: [
    {
      type: 1,
      desc: [
        '取车时，店员可能会向您推销额外保险或收费项目，请根据实际需要选购。在签署合同时，请仔细核对是否有额外收费。如被强制要求购买，请您不要签字，当场联系携程客服协助解决。',
      ],
    },
  ],
  authType: 0,
  useCalabiId: true,
};

// 新契约  待支付订单 1128168993028463 18862/queryOrderSecondOpen 18862/queryPreloadingOrderDetail
export const dzfddsecondRes = {
  vehicleInfo: {
    passengerNum: 4,
    transmission: 'AT',
    imageUrl: '',
    doorNum: 4,
    vehicleGroupName: '小型轿车',
    vehicleName: '日产 ',
    vendorVehicleCode: 'ECAV_NissanAlmera_d139fe91',
  },
  pickupStore: {
    businessTimePolicy: {
      content: [
        '18:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次¥444，费用及税费以实际支付为准。',
      ],
      summaryContent: ['18:00取车，门店预估收取服务费¥444。'],
      table: [
        {
          tableTitle: '时间段|服务费/次',
          code: 'BusinessTimePolicy',
          title: '服务费标准',
          notices: [
            '注意：请按时到达，如需提前或延后取还车务必提前联系门店。服务费和税费以实际支付为准。',
          ],
          showFree: false,
          type: 400,
          subTitle: '取车',
          items: [
            {
              showFree: false,
              positiveStatus: true,
              title: '18:00-23:59',
              description: '¥444',
            },
          ],
        },
      ],
    },
    localDateTimeDTO: {
      date: '2025年7月24日',
      time: '18:00',
    },
    longitude: 100.750112,
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    localDateTime: '2025-07-24 18:00:00',
    latitude: 13.689999,
    storeID: 176772,
    serviceType: '0',
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    storeCode: '2',
    storeName: 'Suvarnabhumi Airport',
    businessTimeDesc:
      '{"正常营业时间：":"00:00 - 17:59","收费营业时间：":"18:00 - 23:59"}',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  flight: {
    flightDelayRule: {
      title: '航班延误保留政策',
      rules: [
        {
          title:
            '如您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性、且供应商政策不同，可能无法为您保留车辆或无法免费取消订单，具体条款请以供应商政策为准，敬请谅解。',
        },
        {
          title: '建议您填写航班号，如遇延误请携带机票前往取车',
        },
      ],
      description: '航班延误保留政策',
    },
    flightNumber: 'EU8773',
  },
  checkResponseTime: 1750157403770.5898,
  checkRequestTime: 1750157403684.4531,
  baseResponse: {
    code: '0',
    requestId: 'da070430-263d-4338-8386-83f2f5180a41',
    cost: 56,
    isSuccess: true,
    returnMsg: 'success',
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1750157403850+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '6778345015937651998',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a779e61-486154-1521690',
      },
    ],
  },
  timeInterval: 86.13671875,
  resBodySize: 3133,
  returnStore: {
    businessTimePolicy: {
      content: [''],
    },
    localDateTimeDTO: {
      date: '2025年7月25日',
      time: '18:00',
    },
    longitude: 100.750112,
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    localDateTime: '2025-07-25 18:00:00',
    latitude: 13.689999,
    storeID: 176772,
    serviceType: '0',
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    storeCode: '2',
    storeName: 'Suvarnabhumi Airport',
    businessTimeDesc: '{"正常营业时间：":"24小时营业"}',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  orderBaseInfo: {
    orderId: 1128168993028463,
    orderStatus: {
      orderStatusCtrip: 'CAR_WAITING_PAY',
      orderStatusDesc: '待支付',
    },
    orderTip: {},
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 260,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 260,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1750157403683,
    afterFetch: 1750157403943,
  },
};

// 新契约  待支付订单 1128168993028463 18862/OSDQueryOrder  18862/queryOsdOrderDetail
export const dzfddRes = {
  timeInterval: 583.175048828125,
  returnStore: {
    storeLocation: '门店位于素万那普国际机场到达大厅',
    businessTimePolicy: {
      content: [''],
    },
    storeBusinessTime: [
      {
        timeStr: '24小时营业',
        free: true,
      },
    ],
    localDateTimeDTO: {
      date: '2025年7月25日',
      time: '18:00',
    },
    longitude: 100.750112,
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    localDateTime: '2025-07-25 18:00:00',
    latitude: 13.689999,
    storeWay: '步行可达',
    storeID: 176772,
    location: {
      city: {
        id: 359,
        name: '曼谷',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      locationType: 1,
      locationName: '素万那普国际机场',
      continent: {
        id: 1,
        name: '亚洲',
      },
      poiInfo: {
        type: 1,
        longitude: 100.750112,
        latitude: 13.689999,
      },
      locationCode: 'BKK',
      province: {
        id: 0,
      },
    },
    userSearchLocation: '素万那普国际机场',
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    storeCode: '2',
    storeName: 'Suvarnabhumi Airport',
    serviceType: '0',
    countryId: 4,
    businessTimeDesc: '{"正常营业时间：":"24小时营业"}',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  addPayments: [],
  coverage: [
    {
      code: 'ULM',
      title: '不限里程',
      type: 2,
      description: '租期内没有公里数限制。\n',
    },
    {
      title: '满电取还',
      type: 4,
      code: 'FRFB',
    },
    {
      title: '满电取还',
      type: 4,
      code: 'FRFB',
    },
  ],
  orderBaseInfo: {
    attr: {
      osdConfirmNew: 'true',
      voucherVersionV2: 'B',
      isContractTemplates: 'true',
      fulfillmentVersion: 'D',
      isVehicle2: '1',
      voucherVersion: 'B',
      osdModifyOrderVersion: 'B',
      ctripInsuranceVersion: 'B',
      klbVersion: '1',
      osdDetailVersion: 'B',
    },
    orderId: 1128168993028463,
    uid: 'M2258803416',
    orderTip: {
      urgentWarning: '库存紧张，别错过!',
      tipContentsWithTag: {
        tagDesc: '请在{tag}前完成支付，否则订单可能受价格/库存波动被取消',
        tagContent: '19:18',
      },
    },
    orderStatus: {
      orderStatus: 0,
      orderStatusName: '待支付',
      orderStatusDesc: '待支付',
      orderStatusCtrip: 'CAR_WAITING_PAY',
    },
    lastEnablePayTime: 1750159138000,
    osdModifyNewOrder: false,
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1750157404513+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '3221916615006749863',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a79d07a-486154-1808232',
      },
    ],
  },
  carAssistantSummary: [
    {
      title: '查看具体要求',
      type: 1,
      url: 'https://www.fat10668.qa.nt.ctripcorp.com/carhire/materialsMustRead?orderId=1128168993028463&h5View=1&locale=zh-CN&orderStatus=CAR_WAITING_PAY&vendorId=14059&countryId=4&isHideNavBar=YES&subEnv=fat14605&transparentbar=1&newOsdVoucherV2=1&orderLocale=zh-CN,th-TH,en-US',
    },
  ],
  operation: {
    extOperation: [],
    orderOperation: [
      {
        operationId: 2,
        enable: true,
        buttonName: '取消订单',
        contents: [
          {
            description:
              '租车价格库存随时存在波动，重新预定价格可能会上涨，请你再次确认是否取消？',
          },
        ],
      },
    ],
  },
  resBodySize: 31277,
  driverInfo: {
    areaCode: '86',
    lastName: 'LI',
    firstName: 'LIU',
    contactWayList: [],
    age: '34',
    optionalContactWayList: [
      {
        contactWayName: '微信',
        isCanModify: true,
        contactWayType: '1',
      },
      {
        contactWayName: 'WhatsApp',
        isCanModify: true,
        contactWayType: '4',
      },
      {
        contactWayName: 'LINE',
        isCanModify: true,
        contactWayType: '2',
      },
      {
        contactWayName: 'KakaoTalk',
        isCanModify: true,
        contactWayType: '3',
      },
      {
        contactWayName: '当地电话',
        isCanModify: true,
        contactWayType: '0',
      },
    ],
    email: 's**<EMAIL>',
    flightNo: 'EU8773',
    isChangeContact: false,
    name: 'LI/LIU',
    telphone: '139****3823',
  },
  osdPolicy: {
    carRentalMustRead: [
      {
        code: '4',
        sortNum: 1,
        title: '额外驾驶员',
        type: 6,
      },
      {
        code: '4',
        sortNum: 2,
        title: '营业时间外取还车',
        type: 7,
      },
      {
        code: '4',
        sortNum: 3,
        title: '提前/延后取还车',
        type: 41,
      },
      {
        code: '4',
        sortNum: 4,
        title: '费用须知',
        type: 40,
      },
      {
        code: '1',
        sortNum: 1,
        title: '确认政策',
        content: ['预订此车型后可快速确认订单。\n'],
        type: 0,
      },
      {
        code: '1',
        sortNum: 2,
        title: '取消政策',
        content: [
          '取车前1小时前可免费取消;取车前1小时后取消将收取租金10%作为违约金',
        ],
        type: 1,
        table: [
          {
            code: 'FreeCancel',
            title: '取消政策',
            complexSubTitle: {
              contentStyle: '1',
              stringObjs: [
                {
                  content: '当地时间7月24日17:00前可免费取消',
                  style: '5',
                },
                {
                  content: '，',
                  style: '6',
                },
                {
                  content: '当地时间7月24日17:00前可免费取消',
                  style: '6',
                },
              ],
            },
            showFree: true,
            type: 300,
            description: '注意：均为当地时间',
            subTitle: '当地时间7月24日17:00前可免费取消',
            items: [
              {
                showFree: true,
                title: '2025-07-24 17:00前',
                subTitle: '取车前1小时前',
                description: '可免费取消',
                key: '837005',
              },
              {
                showFree: false,
                title: '2025-07-24 17:00后',
                subTitle: '取车前1小时后',
                description: '取消将收取租金10%作为违约金',
                key: '837012',
              },
            ],
          },
        ],
      },
      {
        code: '1',
        sortNum: 3,
        title: '押金说明',
        content: [
          '押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541），取车时刷取信用卡预授权，还车后30-60天内退还',
        ],
        type: 2,
        table: [
          {
            title: '押金',
            showFree: false,
            description:
              '押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541），取车时刷取信用卡预授权，还车后30-60天内退还',
          },
        ],
      },
      {
        code: '1',
        sortNum: 4,
        title: '里程政策',
        content: ['该车辆没有里程限制。'],
        type: 3,
      },
      {
        code: '1',
        sortNum: 5,
        title: '能源政策',
        content: [
          '满油取还',
          '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
        ],
        type: 4,
      },
      {
        code: '1',
        sortNum: 6,
        title: '年龄要求',
        content: [
          '驾驶员年龄要求：25-65周岁',
          '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
        ],
        type: 5,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '里程政策',
            content: ['该车辆没有里程限制。'],
            type: 3,
          },
          {
            code: '2',
            sortNum: 2,
            title: '能源政策',
            content: [
              '满油取还',
              '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
            ],
            type: 4,
          },
          {
            code: '2',
            sortNum: 3,
            title: '年龄要求',
            content: [
              '驾驶员年龄要求：25-65周岁',
              '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
              '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
              '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
            ],
            type: 5,
          },
          {
            code: '2',
            sortNum: 4,
            title: '额外驾驶员',
            content: [
              '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
            ],
            type: 6,
          },
          {
            code: '2',
            sortNum: 5,
            title: '营业时间外取还车',
            content: [
              '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
            ],
            type: 7,
          },
          {
            code: '2',
            sortNum: 6,
            title: '当地费用',
            content: [
              '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
            ],
            type: 16,
          },
        ],
        title: '费用须知',
        content: [],
        type: 40,
        sortNum: 1,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '提前取车',
            content: ['1'],
            type: 17,
          },
          {
            code: '2',
            sortNum: 2,
            title: '延迟取车',
            content: ['3'],
            type: 18,
          },
          {
            code: '2',
            sortNum: 3,
            title: '提前还车',
            content: ['5'],
            type: 10,
          },
          {
            code: '2',
            sortNum: 4,
            title: '延迟还车',
            content: ['7'],
            type: 11,
          },
        ],
        title: '提前/延后取还车',
        content: [],
        type: 41,
        sortNum: 2,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '签合同前核对金额',
            content: [
              '办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。',
            ],
            type: 59,
          },
          {
            code: '2',
            sortNum: 2,
            title: '验车留存有效凭证',
            content: [
              '取车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身、油量里程、车损特写等）。 \n因各地车损处理方式不同，部分国家并非及时确认车损 ，需要您保留有效还车车况凭证（注意开启相机拍照的时间显示功能，需证明照片拍摄时间），否则后续产生车损争议平台可能因当地行业操作，以供应商凭证为准。',
            ],
            type: 60,
          },
        ],
        title: '取还车注意事项',
        content: [],
        type: 58,
        sortNum: 3,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 2,
            title: '管理费',
            content: [
              '如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。',
              '此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。',
            ],
            type: 22,
          },
        ],
        title: '租车保障',
        content: [],
        type: 20,
        sortNum: 4,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '额外设备',
            content: [
              '额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。',
            ],
            type: 24,
          },
          {
            code: '2',
            sortNum: 2,
            title: '税费',
            content: ['所有额外服务将会收取销售税费和当地费用。'],
            type: 25,
          },
        ],
        title: '附加服务',
        content: [],
        type: 23,
        sortNum: 5,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '修改',
            content: [
              '如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。',
            ],
            type: 31,
          },
          {
            code: '2',
            sortNum: 2,
            title: 'No show（未取车）',
            content: [
              'noshow 是指当你：',
              '计划提前取消订单但未告知我们，或者',
              '未在规定的时间内取车，或者',
              '无法提供取车时要出示的文件，或者',
              '无法提供主驾驶员名下有足够额度的信用卡',
              '以上情形，你之前预定的订单金额不会退还给你。',
            ],
            type: 32,
          },
        ],
        title: '取消，未取车和修改',
        content: [],
        type: 42,
        sortNum: 7,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '意外或故障',
            content: [
              '事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。',
            ],
            type: 34,
          },
          {
            code: '2',
            sortNum: 2,
            title: '道路救援',
            content: [
              '道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。',
            ],
            type: 35,
          },
          {
            code: '2',
            sortNum: 3,
            title: '遗失钥匙',
            content: ['遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用'],
            type: 36,
          },
          {
            code: '2',
            sortNum: 4,
            title: '安全带',
            content: [
              '安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。',
            ],
            type: 37,
          },
          {
            code: '2',
            sortNum: 5,
            title: '禁止吸烟',
            content: [
              '禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。',
            ],
            type: 38,
          },
          {
            code: '2',
            sortNum: 6,
            title: '价格计算',
            content: [
              '价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。',
              '取车的时候，客人可能会选择不同的车，而不是预订时候的车。',
              '如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。',
              '如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。',
            ],
            type: 39,
          },
          {
            code: '2',
            sortNum: 7,
            title: '违章条款',
            content: [
              '请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中可能产生的违章罚款费用以及供应商代为交涉产生的行政费用（也称“违章管理费”）。\n由于车辆登记在租车公司名下，因此产生违章时，交管局会直接通知租车公司，车行将会把承租人信息提供给当局。所有租车合同中均包含信用卡自动扣费授权条款，用于处理违章和路费欠缴事宜。提车时主驾驶人需签字确认该条款，因此在发生违章时，租车公司通常不会提前通知您，直接从信用卡中扣除当局确认所产生的行政费用（通常按违章次数进行收取）。\n由于隐私法律的限制，大部分车行无法代缴罚金，是由当地相关部门以邮件或纸质平邮的形式，通过您在租车合同上预留的邮箱或者地址通知您。',
            ],
            type: 13,
          },
        ],
        title: '租车公司重要信息',
        content: [],
        type: 33,
        sortNum: 8,
      },
    ],
    crossLocationsPolicy: {
      title: '旅行限制',
      crossLocationsInfos: [
        {
          crossType: 3,
          crossTypeName: '跨境政策',
          summaryPolicies: [
            '由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨境，建议您更换其它租车公司或车型组。',
          ],
        },
      ],
    },
  },
  vehicleInfo: {
    fuelNoteTitle: '汽油或柴油',
    seriesWarning: {
      introduce: {
        title: '同组车型',
        description:
          '同组车型是指按照车型功能、空间大小、座位数和车门数划分的紧凑型轿车、中型轿车、小型SUV等等这些分类。您在网上预订时选择的车并不能保证到店就一定取到一模一样的，可能会得到同组车型中的其他车。',
      },
      carProtection: {
        title: '取车保障',
        description:
          '在库存充足的情况下，车行会尽量提供您在预订时所选的车型\n如果这款车暂时没有库存，车行便会为您提供同车型组内的其他车型\n若因门店原因需要调整车型，不额外收取车型变更费用',
      },
      video: 'https://ak-v.tripcdn.com/videos/9D0g2r000001j0rrk1E0A.mp4',
      cover: 'https://ak-d.tripcdn.com/images/1tg1u12000dwdf1uw1DCD.png',
      cases: [
        {
          vehicleGroupName: '车型组',
          representativeVehicleName: '代表车型',
          vehicleGroupItems: '车组包含',
          vehicleGroupCode: 'default',
        },
        {
          vehicleGroupName: '紧凑型轿车',
          representativeVehicleName: '福特嘉年华',
          vehicleGroupItems: '丰田卡罗拉、现代雅绅特',
          vehicleGroupCode: 'D',
        },
        {
          vehicleGroupName: '中大型轿车',
          representativeVehicleName: '丰田凯美瑞',
          vehicleGroupItems: '雪佛兰迈锐宝、大众帕萨特',
          vehicleGroupCode: 'S',
        },
        {
          vehicleGroupName: '中大型SUV',
          representativeVehicleName: '大众途观',
          vehicleGroupItems: '丰田RAV4、吉普指南者',
          vehicleGroupCode: 'R',
        },
      ],
    },
    fuelType: 2416,
    vendorVehicleCode: 'ECAV_NissanAlmera_d139fe91',
    vehicleId: 543649,
    vehicleDisplacement: '',
    vehicleGroupName: '小型轿车',
    transmission: 'AT',
    esgInfo: {},
    passengerNum: 4,
    imageUrl: '',
    sippCode: 'ECAV',
    vehicleImgUrl: '',
    hasAC: false,
    fourDrive: false,
    luggageNum: 0,
    doorNum: 4,
    vehicleGroupId: 1001,
    special: false,
    transmissionType: 1,
    fuelNote:
      '门店会根据库存情况为您提供汽油车或者柴油车，具体燃油类型与加油型号以门店告知为准。',
    vehicleName: '日产 ',
  },
  pickupStore: {
    location: {
      city: {
        id: 359,
        name: '曼谷',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      locationType: 1,
      locationName: '素万那普国际机场',
      continent: {
        id: 1,
        name: '亚洲',
      },
      poiInfo: {
        type: 1,
        longitude: 100.750112,
        latitude: 13.689999,
      },
      locationCode: 'BKK',
      province: {
        id: 0,
      },
    },
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    storeCode: '2',
    storeLocation: '门店位于素万那普国际机场到达大厅',
    localDateTime: '2025-07-24 18:00:00',
    storeName: 'Suvarnabhumi Airport',
    outOfHourDescription:
      '18:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次¥444，费用及税费以实际支付为准。',
    storeBusinessTime: [
      {
        timeStr: '00:00 - 17:59',
        free: true,
      },
      {
        timeStr: '18:00 - 23:59',
        free: false,
      },
    ],
    latitude: 13.689999,
    storeOpenTimeDesc: '{"":"24小时营业"}',
    countryId: 4,
    guideStep: [
      {
        image:
          'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R4331200003to07133D1.png',
        content: 'Drive Car Rental柜台位于二楼的国际到达大厅，7-8号出口之间。',
      },
      {
        image:
          'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R43z1200003to073876B.jpg',
        content:
          '国际或国内到达的乘客：从行李领取区出来后需步行到国际到达大厅，然后直接前往7号出口，我们的柜台位于7-8号出口之间，写有蓝色的“Drive Car Rental”标志。',
      },
      {
        image:
          'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R4601200003to0dz75E5.jpg',
        content:
          '办理完手续后，下到一楼从7号出口出去，穿过马路到外侧道路，跟随写有“Car Rental”的指示牌前往停车场，在“Drive Car Rental"的指示标志处找工作人员取车。',
      },
    ],
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    localDateTimeDTO: {
      date: '2025年7月24日',
      time: '18:00',
    },
    businessTimeDesc:
      '{"正常营业时间：":"00:00 - 17:59","收费营业时间：":"18:00 - 23:59"}',
    longitude: 100.750112,
    storeWay: '步行可达',
    businessTimePolicy: {
      content: [
        '18:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次¥444，费用及税费以实际支付为准。',
      ],
      summaryContent: ['18:00取车，门店预估收取服务费¥444。'],
      table: [
        {
          tableTitle: '时间段|服务费/次',
          code: 'BusinessTimePolicy',
          title: '服务费标准',
          notices: [
            '注意：请按时到达，如需提前或延后取还车务必提前联系门店。服务费和税费以实际支付为准。',
          ],
          showFree: false,
          type: 400,
          subTitle: '取车',
          items: [
            {
              showFree: false,
              positiveStatus: true,
              title: '18:00-23:59',
              description: '¥444',
            },
          ],
        },
      ],
    },
    serviceType: '0',
    waitTimeDesc: '',
    userSearchLocation: '素万那普国际机场',
    storeID: 176772,
  },
  baseResponse: {
    code: '200',
    requestId: '4b979184-2cf8-4fed-9991-eb9bf60ef55f',
    cost: 538,
    isSuccess: true,
    returnMsg: 'success',
  },
  flight: {
    flightDelayRule: {
      title: '航班延误保留政策',
      rules: [
        {
          title:
            '如您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性、且供应商政策不同，可能无法为您保留车辆或无法免费取消订单，具体条款请以供应商政策为准，敬请谅解。',
        },
        {
          title: '建议您填写航班号，如遇延误请携带机票前往取车',
        },
      ],
      description: '航班延误保留政策',
    },
    flightNumber: 'EU8773',
  },
  osdRefund: {
    refundProgressList: [],
  },
  checkResponseTime: 1750157404460.731,
  checkRequestTime: 1750157403877.556,
  continuePayInfo: {
    needContinuePay: true,
    leftMinutes: 28,
    leftSeconds: 53,
  },
  platformInsurance: {
    insPackageId: 1,
    extraDesc: [
      '',
      '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比<url>https://www.baidu.com</url>实际赔付范围与标准以门店合同为准-卡拉比',
    ],
    packageInfos: [
      {
        insPackageId: 1,
        naked: false,
        defaultBomCode: '',
        currencyCode: '',
        defaultPackageId: 390412,
        gapPrice: 0,
        isDefault: true,
        stepPrice: 0,
        lowestDailyPrice: 0,
        packageName: '基础套餐',
        guaranteeDegree: 0,
      },
    ],
    insuranceItems: [
      {
        productId: 390412,
        description: '保障车辆碰撞损失',
        converageExplain: {
          title: '承保范围',
          content: [
            '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后',
          ],
        },
        excessTable: {
          title: '起赔额详见门店合同',
          subObject: [
            {
              title: '车行承担',
              content: ['起赔额以上部分（起赔额详见门店合同）'],
            },
            {
              title: '客户或承租方承担',
              content: ['起赔额及以下部分（据实承担）'],
            },
          ],
        },
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '发生事故后报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        type: 7,
        title: '车辆碰撞保障',
        code: 'CDW',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额详见门店合同',
            excessShortDesc: '起赔额详见门店合同',
            currencyCode: '',
            coverageWithPlatformInsurance: '起赔额详见门店合同',
            packageId: 390412,
          },
        ],
        unConverageExplain: {
          title: '不承保范围',
          content: [
            '不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准',
          ],
        },
        isInclude: true,
        name: '车辆碰撞保障',
        isFromCtrip: false,
      },
    ],
    briefInsuranceItems: [
      {
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额详见门店合同',
            excessShortDesc: '起赔额详见门店合同',
            currencyCode: '',
            coverageWithPlatformInsurance: '起赔额详见门店合同',
            packageId: 390412,
          },
        ],
        code: 'CDW',
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '发生事故后报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        isInclude: true,
        description: '保障车辆碰撞损失',
        type: 7,
        name: '车辆碰撞保障',
        isFromCtrip: false,
      },
    ],
    claimsProcess: [],
    reminder: [
      {
        subText:
          '如签署合同时，发现额外收费或强制购买，请不要签字，当场联系携程客服协助解决。',
        text: '温馨提示：',
      },
    ],
    ctripInsDeclaration: [
      {
        purchasedDesc:
          '该客人的保险套餐已满足当地法律的最低要求，请避免强迫客人添加任何保险，否则可能导致我处对相关当局的投诉。',
        requirement:
          "如果认为某项保险是必须的，请写明'xx保险是必须的'并附上签名和日期。",
        partner: '亲爱的供应商伙伴：',
        statement: '携程声明',
      },
      {
        purchasedDesc:
          'แพ็คเกจประกันภัยของลูกค้าครอบคลุมตามข้อกำหนดขั้นต่ำของกฎหมายท้องถิ่นแล้ว โปรดหลีกเลี่ยงการกดดันให้ลูกค้าซื้อประกันภัยเพิ่มเติม การกระทำดังกล่าวอาจทำให้เกิดข้อร้องเรียนได้',
        requirement:
          'หากคุณเชื่อว่าต้องมีประกันภัยบางประเภท โปรดระบุเป็นลายลักษณ์อักษรว่า "ต้องมีประกันภัย xx" พร้อมลงลายมือชื่อของคุณและวันที่',
        partner: 'เรียน ซัพพลายเออร์พาร์ทเนอร์:',
        statement: 'ใบแจ้งยอดของ Trip.com',
      },
      {
        purchasedDesc:
          "The customer's insurance package already meets minimum local law requirements. Please avoid pressuring the customer to add any insurance. Doing so may result in complaints being raised.",
        requirement:
          'If you believe a certain type of insurance is required, please indicate in writing that "xx insurance is required" and include your signature and date.',
        partner: 'Dear supplier partner:',
        statement: 'Trip.com Statement',
      },
    ],
  },
  freeDeposit: {
    depositExplainV2: [
      {
        explain:
          '到店支付押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541）',
        badDebt: false,
      },
    ],
    depositStatus: 0,
    depositItems: [
      {
        depositTitle: '押金',
        depositStatus: 2,
        explain: '取车时刷取信用卡预授权，还车后30-60天内退还',
        depositDesc: '押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541）',
      },
      {
        depositTitle: '支持卡种',
        depositDesc:
          '带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店不支持卡面带银联标志',
        creditUrlList: [
          'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
          'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
        ],
      },
    ],
    depositItemName: '押金详情',
  },
  rentalEssential: {
    mustRead: [],
    pickUpMaterial: [
      {
        title: '中国大陆护照',
      },
      {
        title: '中国驾照原件 + 驾照国际翻译认证件',
      },
      {
        title: '中国驾照原件 + 当地语言公证件',
      },
      {
        title: '国际信用卡(带芯片、卡号凸字、不带银联标记)',
      },
    ],
  },
  receipt: {},
  cancelRuleInfo: {
    longTitle: 'In the following circumstances, you will not receive a refund:',
    ruleList: [
      '- You pick up the car late or drop it off early',
      '- You do not pick up the car within the given timeframe',
      '- You do not provide the required documents when picking up the car',
      "- The main driver's credit card does not have sufficient funds",
    ],
    cancelTip: {
      cancelTip: 'This booking can be canceled',
      cancelTipColor: 1,
    },
    customerCurrency: 'CNY',
    topTips: [
      {
        title: '免费取消',
        type: 1,
        description: '当前时间7月24日17:00前',
      },
      {
        title: '取消需收费',
        type: 0,
        description: '当前时间7月24日17:00后',
      },
    ],
    osdCancelRuleInfo: {
      code: 'FreeCancel',
      title: '取消政策',
      complexSubTitle: {
        contentStyle: '1',
        stringObjs: [
          {
            content: '当地时间7月24日17:00前可免费取消',
            style: '5',
          },
          {
            content: '，',
            style: '6',
          },
          {
            content: '当地时间7月24日17:00前可免费取消',
            style: '6',
          },
        ],
      },
      showFree: true,
      type: 300,
      description: '注意：均为当地时间',
      subTitle: '当地时间7月24日17:00前可免费取消',
      items: [
        {
          showFree: true,
          lossFee: 0,
          title: '2025-07-24 17:00前',
          subTitle: '取车前1小时前',
          description: '可免费取消',
          key: '837005',
        },
        {
          showFree: false,
          lossFee: 0,
          title: '2025-07-24 17:00后',
          subTitle: '取车前1小时后',
          description: '取消将收取租金10%作为违约金',
          key: '837012',
        },
      ],
    },
  },
  authType: 0,
  orderPrice: {
    localCurrency: 'THB',
    localPayAmountOnArrival: 0,
    feeDetail: {
      chargesInfos: [
        {
          title: '车辆租金+基础套餐',
        },
        {
          code: 'Car',
          title: '基础租车费用',
          size: '约¥205×1天',
          currencyCode: 'CNY',
          currentTotalPrice: 205,
          description: '',
          type: 8,
          payMode: 2,
          items: [
            {
              code: 'Car',
              title: '基础租车费用',
              descList: [
                '满电取还, 满电取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 基本租车费用, 不限里程',
              ],
              description:
                '满电取还, 满电取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 基本租车费用, 不限里程',
              type: 8,
            },
            {
              title: '车辆碰撞保障',
              type: 1,
              code: 'Insurance',
            },
          ],
        },
      ],
      notIncludeCharges: {},
      rentalTerm: 1,
      chargesSummary: {
        code: 'Summary',
        title: '全额',
        currencyCode: 'CNY',
        notices: [],
        currentTotalPrice: 205,
        type: 103,
        items: [
          {
            code: '1',
            currentTotalPrice: 205,
            title: '在线预付',
            currencyCode: 'CNY',
          },
        ],
      },
      equipmentInfos: [],
      couponInfos: [],
    },
    customerPayAmount: 205,
    totalAmount: 205,
    customerPayAmountOnArrival: 0,
    priceDesc: {
      possibleChangeList: [
        {
          title: '里程政策',
          description: '该车辆没有里程限制。',
        },
        {
          title: '能源政策',
          description: '满油取还',
        },
        {
          title: '年龄要求',
          description: '驾驶员年龄要求：25-65周岁',
        },
        {
          title: '额外驾驶员',
          description:
            '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
        },
        {
          title: '营业时间外取还车',
          description:
            '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
        },
        {
          title: '当地费用',
          description:
            '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
        },
        {
          title:
            '到店支付押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541）',
        },
      ],
    },
    customerCurrency: 'CNY',
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 854,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 854,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1750157403877,
    afterFetch: 1750157404731,
  },
};

// 新契约  处理中订单 1128168993028463 18862/queryOrderSecondOpen 18862/queryPreloadingOrderDetail
export const clzddsecondRes = {
  osdCancelRuleInfo: {
    code: 'FreeCancel',
    title: '取消政策',
    complexSubTitle: {
      contentStyle: '1',
      stringObjs: [
        {
          content: '当地时间7月24日17:00前可免费取消',
          style: '5',
        },
        {
          content: '，',
          style: '6',
        },
        {
          content: '当地时间7月24日17:00前可免费取消',
          style: '6',
        },
      ],
    },
    showFree: true,
    type: 300,
    description: '注意：均为当地时间',
    subTitle: '当地时间7月24日17:00前可免费取消',
    items: [
      {
        showFree: true,
        lossFee: 0,
        title: '2025-07-24 17:00前',
        subTitle: '取车前1小时前',
        description: '可免费取消',
        key: '837005',
      },
      {
        showFree: false,
        lossFee: 21,
        title: '2025-07-24 17:00后',
        subTitle: '取车前1小时后',
        description: '取消将收取租金10%作为违约金',
        key: '837012',
      },
    ],
  },
  baseResponse: {
    code: '200',
    requestId: 'ddf5a00c-0e68-4694-a6d6-65bbc93d5516',
    cost: 601,
    isSuccess: true,
    returnMsg: 'success',
  },
  amount: 0,
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1750157613121+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '7536844124468735028',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a789841-486154-1724978',
      },
    ],
  },
  freeNote: '订单支付成功后，原订单将自动取消。',
  timeInterval: 631.046142578125,
  resBodySize: 1291,
  checkRequestTime: 1750157612410.32,
  warningNote: '新单以供应商确认结果为准，修改后可能发生变价。',
  checkResponseTime: 1750157613041.3662,
  canModify: true,
  currencyCode: 'CNY',
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 851,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 851,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1750157612409,
    afterFetch: 1750157613260,
  },
};

// 新契约  处理中订单 1128168993028463 18862/OSDQueryOrder  18862/queryOsdOrderDetail
export const clzddRes = {
  timeInterval: 805.***********,
  returnStore: {
    storeLocation: '门店位于素万那普国际机场到达大厅',
    businessTimePolicy: {
      content: [''],
    },
    storeBusinessTime: [
      {
        timeStr: '24小时营业',
        free: true,
      },
    ],
    localDateTimeDTO: {
      date: '2025年7月25日',
      time: '18:00',
    },
    longitude: 100.750112,
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    localDateTime: '2025-07-25 18:00:00',
    latitude: 13.689999,
    storeWay: '步行可达',
    storeID: 176772,
    location: {
      city: {
        id: 359,
        name: '曼谷',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      locationType: 1,
      locationName: '素万那普国际机场',
      continent: {
        id: 1,
        name: '亚洲',
      },
      poiInfo: {
        type: 1,
        longitude: 100.750112,
        latitude: 13.689999,
      },
      locationCode: 'BKK',
      province: {
        id: 0,
      },
    },
    userSearchLocation: '素万那普国际机场',
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    storeCode: '2',
    storeName: 'Suvarnabhumi Airport',
    serviceType: '0',
    countryId: 4,
    businessTimeDesc: '{"正常营业时间：":"24小时营业"}',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  addPayments: [],
  coverage: [
    {
      code: 'ULM',
      title: '不限里程',
      type: 2,
      description: '租期内没有公里数限制。\n',
    },
    {
      title: '满电取还',
      type: 4,
      code: 'FRFB',
    },
    {
      title: '满电取还',
      type: 4,
      code: 'FRFB',
    },
  ],
  orderBaseInfo: {
    orderStatus: {
      orderStatus: 1,
      orderStatusName: '处理中',
      orderStatusDesc: '处理中',
      orderStatusCtrip: 'CAR_PROCESSING',
    },
    orderId: 1128168993028463,
    uid: 'M2258803416',
    orderTip: {
      tipContentArray: [],
    },
    attr: {
      osdConfirmNew: 'true',
      voucherVersionV2: 'B',
      isContractTemplates: 'true',
      fulfillmentVersion: 'D',
      isVehicle2: '1',
      voucherVersion: 'B',
      osdModifyOrderVersion: 'B',
      ctripInsuranceVersion: 'B',
      klbVersion: '1',
      osdDetailVersion: 'B',
    },
    osdModifyNewOrder: false,
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1750157614076+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '7416255940517439613',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a789841-486154-1725011',
      },
    ],
  },
  carAssistantSummary: [
    {
      title: '查看具体要求',
      type: 1,
      url: 'https://www.fat10668.qa.nt.ctripcorp.com/carhire/materialsMustRead?orderId=1128168993028463&h5View=1&locale=zh-CN&orderStatus=CAR_PROCESSING&vendorId=14059&countryId=4&isHideNavBar=YES&subEnv=fat14605&transparentbar=1&newOsdVoucherV2=1&orderLocale=zh-CN,th-TH,en-US',
    },
  ],
  operation: {
    extOperation: [],
    orderOperation: [
      {
        operationId: 2,
        enable: true,
        buttonName: '取消订单',
        contents: [
          {
            description:
              '租车价格库存随时存在波动，重新预定价格可能会上涨，请你再次确认是否取消？',
          },
        ],
      },
    ],
  },
  resBodySize: 31820,
  driverInfo: {
    areaCode: '86',
    lastName: 'LI',
    firstName: 'LIU',
    contactWayList: [],
    age: '34',
    optionalContactWayList: [
      {
        contactWayName: '微信',
        isCanModify: true,
        contactWayType: '1',
      },
      {
        contactWayName: 'WhatsApp',
        isCanModify: true,
        contactWayType: '4',
      },
      {
        contactWayName: 'LINE',
        isCanModify: true,
        contactWayType: '2',
      },
      {
        contactWayName: 'KakaoTalk',
        isCanModify: true,
        contactWayType: '3',
      },
      {
        contactWayName: '当地电话',
        isCanModify: true,
        contactWayType: '0',
      },
    ],
    email: 's**<EMAIL>',
    flightNo: 'EU8773',
    isChangeContact: false,
    name: 'LI/LIU',
    telphone: '139****3823',
  },
  osdPolicy: {
    carRentalMustRead: [
      {
        code: '4',
        sortNum: 1,
        title: '额外驾驶员',
        type: 6,
      },
      {
        code: '4',
        sortNum: 2,
        title: '营业时间外取还车',
        type: 7,
      },
      {
        code: '4',
        sortNum: 3,
        title: '提前/延后取还车',
        type: 41,
      },
      {
        code: '4',
        sortNum: 4,
        title: '费用须知',
        type: 40,
      },
      {
        code: '1',
        sortNum: 1,
        title: '确认政策',
        content: ['预订此车型后可快速确认订单。\n'],
        type: 0,
      },
      {
        code: '1',
        sortNum: 2,
        title: '取消政策',
        content: [
          '取车前1小时前可免费取消;取车前1小时后取消将收取租金10%作为违约金',
        ],
        type: 1,
        table: [
          {
            code: 'FreeCancel',
            title: '取消政策',
            complexSubTitle: {
              contentStyle: '1',
              stringObjs: [
                {
                  content: '当地时间7月24日17:00前可免费取消',
                  style: '5',
                },
                {
                  content: '，',
                  style: '6',
                },
                {
                  content: '当地时间7月24日17:00前可免费取消',
                  style: '6',
                },
              ],
            },
            showFree: true,
            type: 300,
            description: '注意：均为当地时间',
            subTitle: '当地时间7月24日17:00前可免费取消',
            items: [
              {
                showFree: true,
                title: '2025-07-24 17:00前',
                subTitle: '取车前1小时前',
                description: '可免费取消',
                key: '837005',
              },
              {
                showFree: false,
                title: '2025-07-24 17:00后',
                subTitle: '取车前1小时后',
                description: '取消将收取租金10%作为违约金',
                key: '837012',
              },
            ],
          },
        ],
      },
      {
        code: '1',
        sortNum: 3,
        title: '押金说明',
        content: [
          '押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541），取车时刷取信用卡预授权，还车后30-60天内退还',
        ],
        type: 2,
        table: [
          {
            title: '押金',
            showFree: false,
            description:
              '押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541），取车时刷取信用卡预授权，还车后30-60天内退还',
          },
        ],
      },
      {
        code: '1',
        sortNum: 4,
        title: '里程政策',
        content: ['该车辆没有里程限制。'],
        type: 3,
      },
      {
        code: '1',
        sortNum: 5,
        title: '能源政策',
        content: [
          '满油取还',
          '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
        ],
        type: 4,
      },
      {
        code: '1',
        sortNum: 6,
        title: '年龄要求',
        content: [
          '驾驶员年龄要求：25-65周岁',
          '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
        ],
        type: 5,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '里程政策',
            content: ['该车辆没有里程限制。'],
            type: 3,
          },
          {
            code: '2',
            sortNum: 2,
            title: '能源政策',
            content: [
              '满油取还',
              '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
            ],
            type: 4,
          },
          {
            code: '2',
            sortNum: 3,
            title: '年龄要求',
            content: [
              '驾驶员年龄要求：25-65周岁',
              '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
              '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
              '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
            ],
            type: 5,
          },
          {
            code: '2',
            sortNum: 4,
            title: '额外驾驶员',
            content: [
              '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
            ],
            type: 6,
          },
          {
            code: '2',
            sortNum: 5,
            title: '营业时间外取还车',
            content: [
              '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
            ],
            type: 7,
          },
          {
            code: '2',
            sortNum: 6,
            title: '当地费用',
            content: [
              '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
            ],
            type: 16,
          },
        ],
        title: '费用须知',
        content: [],
        type: 40,
        sortNum: 1,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '提前取车',
            content: ['1'],
            type: 17,
          },
          {
            code: '2',
            sortNum: 2,
            title: '延迟取车',
            content: ['3'],
            type: 18,
          },
          {
            code: '2',
            sortNum: 3,
            title: '提前还车',
            content: ['5'],
            type: 10,
          },
          {
            code: '2',
            sortNum: 4,
            title: '延迟还车',
            content: ['7'],
            type: 11,
          },
        ],
        title: '提前/延后取还车',
        content: [],
        type: 41,
        sortNum: 2,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '签合同前核对金额',
            content: [
              '办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。',
            ],
            type: 59,
          },
          {
            code: '2',
            sortNum: 2,
            title: '验车留存有效凭证',
            content: [
              '取车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身、油量里程、车损特写等）。 \n因各地车损处理方式不同，部分国家并非及时确认车损 ，需要您保留有效还车车况凭证（注意开启相机拍照的时间显示功能，需证明照片拍摄时间），否则后续产生车损争议平台可能因当地行业操作，以供应商凭证为准。',
            ],
            type: 60,
          },
        ],
        title: '取还车注意事项',
        content: [],
        type: 58,
        sortNum: 3,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 2,
            title: '管理费',
            content: [
              '如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。',
              '此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。',
            ],
            type: 22,
          },
        ],
        title: '租车保障',
        content: [],
        type: 20,
        sortNum: 4,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '额外设备',
            content: [
              '额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。',
            ],
            type: 24,
          },
          {
            code: '2',
            sortNum: 2,
            title: '税费',
            content: ['所有额外服务将会收取销售税费和当地费用。'],
            type: 25,
          },
        ],
        title: '附加服务',
        content: [],
        type: 23,
        sortNum: 5,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '修改',
            content: [
              '如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。',
            ],
            type: 31,
          },
          {
            code: '2',
            sortNum: 2,
            title: 'No show（未取车）',
            content: [
              'noshow 是指当你：',
              '计划提前取消订单但未告知我们，或者',
              '未在规定的时间内取车，或者',
              '无法提供取车时要出示的文件，或者',
              '无法提供主驾驶员名下有足够额度的信用卡',
              '以上情形，你之前预定的订单金额不会退还给你。',
            ],
            type: 32,
          },
        ],
        title: '取消，未取车和修改',
        content: [],
        type: 42,
        sortNum: 7,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '意外或故障',
            content: [
              '事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。',
            ],
            type: 34,
          },
          {
            code: '2',
            sortNum: 2,
            title: '道路救援',
            content: [
              '道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。',
            ],
            type: 35,
          },
          {
            code: '2',
            sortNum: 3,
            title: '遗失钥匙',
            content: ['遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用'],
            type: 36,
          },
          {
            code: '2',
            sortNum: 4,
            title: '安全带',
            content: [
              '安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。',
            ],
            type: 37,
          },
          {
            code: '2',
            sortNum: 5,
            title: '禁止吸烟',
            content: [
              '禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。',
            ],
            type: 38,
          },
          {
            code: '2',
            sortNum: 6,
            title: '价格计算',
            content: [
              '价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。',
              '取车的时候，客人可能会选择不同的车，而不是预订时候的车。',
              '如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。',
              '如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。',
            ],
            type: 39,
          },
          {
            code: '2',
            sortNum: 7,
            title: '违章条款',
            content: [
              '请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中可能产生的违章罚款费用以及供应商代为交涉产生的行政费用（也称“违章管理费”）。\n由于车辆登记在租车公司名下，因此产生违章时，交管局会直接通知租车公司，车行将会把承租人信息提供给当局。所有租车合同中均包含信用卡自动扣费授权条款，用于处理违章和路费欠缴事宜。提车时主驾驶人需签字确认该条款，因此在发生违章时，租车公司通常不会提前通知您，直接从信用卡中扣除当局确认所产生的行政费用（通常按违章次数进行收取）。\n由于隐私法律的限制，大部分车行无法代缴罚金，是由当地相关部门以邮件或纸质平邮的形式，通过您在租车合同上预留的邮箱或者地址通知您。',
            ],
            type: 13,
          },
        ],
        title: '租车公司重要信息',
        content: [],
        type: 33,
        sortNum: 8,
      },
    ],
    crossLocationsPolicy: {
      title: '旅行限制',
      crossLocationsInfos: [
        {
          crossType: 3,
          crossTypeName: '跨境政策',
          summaryPolicies: [
            '由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨境，建议您更换其它租车公司或车型组。',
          ],
        },
      ],
    },
  },
  vehicleInfo: {
    fuelNoteTitle: '汽油或柴油',
    seriesWarning: {
      introduce: {
        title: '同组车型',
        description:
          '同组车型是指按照车型功能、空间大小、座位数和车门数划分的紧凑型轿车、中型轿车、小型SUV等等这些分类。您在网上预订时选择的车并不能保证到店就一定取到一模一样的，可能会得到同组车型中的其他车。',
      },
      carProtection: {
        title: '取车保障',
        description:
          '在库存充足的情况下，车行会尽量提供您在预订时所选的车型\n如果这款车暂时没有库存，车行便会为您提供同车型组内的其他车型\n若因门店原因需要调整车型，不额外收取车型变更费用',
      },
      video: 'https://ak-v.tripcdn.com/videos/9D0g2r000001j0rrk1E0A.mp4',
      cover: 'https://ak-d.tripcdn.com/images/1tg1u12000dwdf1uw1DCD.png',
      cases: [
        {
          vehicleGroupName: '车型组',
          representativeVehicleName: '代表车型',
          vehicleGroupItems: '车组包含',
          vehicleGroupCode: 'default',
        },
        {
          vehicleGroupName: '紧凑型轿车',
          representativeVehicleName: '福特嘉年华',
          vehicleGroupItems: '丰田卡罗拉、现代雅绅特',
          vehicleGroupCode: 'D',
        },
        {
          vehicleGroupName: '中大型轿车',
          representativeVehicleName: '丰田凯美瑞',
          vehicleGroupItems: '雪佛兰迈锐宝、大众帕萨特',
          vehicleGroupCode: 'S',
        },
        {
          vehicleGroupName: '中大型SUV',
          representativeVehicleName: '大众途观',
          vehicleGroupItems: '丰田RAV4、吉普指南者',
          vehicleGroupCode: 'R',
        },
      ],
    },
    fuelType: 2416,
    vendorVehicleCode: 'ECAV_NissanAlmera_d139fe91',
    vehicleId: 543649,
    vehicleDisplacement: '',
    vehicleGroupName: '小型轿车',
    transmission: 'AT',
    esgInfo: {},
    passengerNum: 4,
    imageUrl: '',
    sippCode: 'ECAV',
    vehicleImgUrl: '',
    hasAC: false,
    fourDrive: false,
    luggageNum: 0,
    doorNum: 4,
    vehicleGroupId: 1001,
    special: false,
    transmissionType: 1,
    fuelNote:
      '门店会根据库存情况为您提供汽油车或者柴油车，具体燃油类型与加油型号以门店告知为准。',
    vehicleName: '日产 ',
  },
  pickupStore: {
    location: {
      city: {
        id: 359,
        name: '曼谷',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      locationType: 1,
      locationName: '素万那普国际机场',
      continent: {
        id: 1,
        name: '亚洲',
      },
      poiInfo: {
        type: 1,
        longitude: 100.750112,
        latitude: 13.689999,
      },
      locationCode: 'BKK',
      province: {
        id: 0,
      },
    },
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    storeCode: '2',
    storeLocation: '门店位于素万那普国际机场到达大厅',
    localDateTime: '2025-07-24 18:00:00',
    storeName: 'Suvarnabhumi Airport',
    outOfHourDescription:
      '18:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次¥444，费用及税费以实际支付为准。',
    storeBusinessTime: [
      {
        timeStr: '00:00 - 17:59',
        free: true,
      },
      {
        timeStr: '18:00 - 23:59',
        free: false,
      },
    ],
    latitude: 13.689999,
    storeOpenTimeDesc: '{"":"24小时营业"}',
    countryId: 4,
    guideStep: [
      {
        image:
          'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R4331200003to07133D1.png',
        content: 'Drive Car Rental柜台位于二楼的国际到达大厅，7-8号出口之间。',
      },
      {
        image:
          'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R43z1200003to073876B.jpg',
        content:
          '国际或国内到达的乘客：从行李领取区出来后需步行到国际到达大厅，然后直接前往7号出口，我们的柜台位于7-8号出口之间，写有蓝色的“Drive Car Rental”标志。',
      },
      {
        image:
          'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R4601200003to0dz75E5.jpg',
        content:
          '办理完手续后，下到一楼从7号出口出去，穿过马路到外侧道路，跟随写有“Car Rental”的指示牌前往停车场，在“Drive Car Rental"的指示标志处找工作人员取车。',
      },
    ],
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    localDateTimeDTO: {
      date: '2025年7月24日',
      time: '18:00',
    },
    businessTimeDesc:
      '{"正常营业时间：":"00:00 - 17:59","收费营业时间：":"18:00 - 23:59"}',
    longitude: 100.750112,
    storeWay: '步行可达',
    businessTimePolicy: {
      content: [
        '18:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次¥444，费用及税费以实际支付为准。',
      ],
      summaryContent: ['18:00取车，门店预估收取服务费¥444。'],
      table: [
        {
          tableTitle: '时间段|服务费/次',
          code: 'BusinessTimePolicy',
          title: '服务费标准',
          notices: [
            '注意：请按时到达，如需提前或延后取还车务必提前联系门店。服务费和税费以实际支付为准。',
          ],
          showFree: false,
          type: 400,
          subTitle: '取车',
          items: [
            {
              showFree: false,
              positiveStatus: true,
              title: '18:00-23:59',
              description: '¥444',
            },
          ],
        },
      ],
    },
    serviceType: '0',
    waitTimeDesc: '',
    userSearchLocation: '素万那普国际机场',
    storeID: 176772,
  },
  baseResponse: {
    code: '200',
    requestId: 'a3c01126-ee81-425e-8ab7-e60e29de8fc1',
    cost: 773,
    isSuccess: true,
    returnMsg: 'success',
  },
  flight: {
    flightDelayRule: {
      title: '航班延误保留政策',
      rules: [
        {
          title:
            '如您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性、且供应商政策不同，可能无法为您保留车辆或无法免费取消订单，具体条款请以供应商政策为准，敬请谅解。',
        },
        {
          title: '建议您填写航班号，如遇延误请携带机票前往取车',
        },
      ],
      description: '航班延误保留政策',
    },
    flightNumber: 'EU8773',
  },
  osdRefund: {
    refundProgressList: [],
  },
  checkResponseTime: 1750157614027.888,
  checkRequestTime: 1750157613222.364,
  continuePayInfo: {
    needContinuePay: false,
  },
  platformInsurance: {
    insPackageId: 1,
    extraDesc: [
      '',
      '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比<url>https://www.baidu.com</url>实际赔付范围与标准以门店合同为准-卡拉比',
    ],
    packageInfos: [
      {
        insPackageId: 1,
        naked: false,
        defaultBomCode: '',
        currencyCode: '',
        defaultPackageId: 390412,
        gapPrice: 0,
        isDefault: true,
        stepPrice: 0,
        lowestDailyPrice: 0,
        packageName: '基础套餐',
        guaranteeDegree: 0,
      },
    ],
    insuranceItems: [
      {
        productId: 390412,
        description: '保障车辆碰撞损失',
        converageExplain: {
          title: '承保范围',
          content: [
            '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后',
          ],
        },
        excessTable: {
          title: '起赔额详见门店合同',
          subObject: [
            {
              title: '车行承担',
              content: ['起赔额以上部分（起赔额详见门店合同）'],
            },
            {
              title: '客户或承租方承担',
              content: ['起赔额及以下部分（据实承担）'],
            },
          ],
        },
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '发生事故后报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        type: 7,
        title: '车辆碰撞保障',
        code: 'CDW',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额详见门店合同',
            excessShortDesc: '起赔额详见门店合同',
            currencyCode: '',
            coverageWithPlatformInsurance: '起赔额详见门店合同',
            packageId: 390412,
          },
        ],
        unConverageExplain: {
          title: '不承保范围',
          content: [
            '不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准',
          ],
        },
        isInclude: true,
        name: '车辆碰撞保障',
        isFromCtrip: false,
      },
    ],
    briefInsuranceItems: [
      {
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额详见门店合同',
            excessShortDesc: '起赔额详见门店合同',
            currencyCode: '',
            coverageWithPlatformInsurance: '起赔额详见门店合同',
            packageId: 390412,
          },
        ],
        code: 'CDW',
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '发生事故后报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        isInclude: true,
        description: '保障车辆碰撞损失',
        type: 7,
        name: '车辆碰撞保障',
        isFromCtrip: false,
      },
    ],
    claimsProcess: [],
    reminder: [
      {
        subText:
          '如签署合同时，发现额外收费或强制购买，请不要签字，当场联系携程客服协助解决。',
        text: '温馨提示：',
      },
    ],
    ctripInsDeclaration: [
      {
        purchasedDesc:
          '该客人的保险套餐已满足当地法律的最低要求，请避免强迫客人添加任何保险，否则可能导致我处对相关当局的投诉。',
        requirement:
          "如果认为某项保险是必须的，请写明'xx保险是必须的'并附上签名和日期。",
        partner: '亲爱的供应商伙伴：',
        statement: '携程声明',
      },
      {
        purchasedDesc:
          'แพ็คเกจประกันภัยของลูกค้าครอบคลุมตามข้อกำหนดขั้นต่ำของกฎหมายท้องถิ่นแล้ว โปรดหลีกเลี่ยงการกดดันให้ลูกค้าซื้อประกันภัยเพิ่มเติม การกระทำดังกล่าวอาจทำให้เกิดข้อร้องเรียนได้',
        requirement:
          'หากคุณเชื่อว่าต้องมีประกันภัยบางประเภท โปรดระบุเป็นลายลักษณ์อักษรว่า "ต้องมีประกันภัย xx" พร้อมลงลายมือชื่อของคุณและวันที่',
        partner: 'เรียน ซัพพลายเออร์พาร์ทเนอร์:',
        statement: 'ใบแจ้งยอดของ Trip.com',
      },
      {
        purchasedDesc:
          "The customer's insurance package already meets minimum local law requirements. Please avoid pressuring the customer to add any insurance. Doing so may result in complaints being raised.",
        requirement:
          'If you believe a certain type of insurance is required, please indicate in writing that "xx insurance is required" and include your signature and date.',
        partner: 'Dear supplier partner:',
        statement: 'Trip.com Statement',
      },
    ],
  },
  freeDeposit: {
    depositExplainV2: [
      {
        explain:
          '到店支付押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541）',
        badDebt: false,
      },
    ],
    depositStatus: 0,
    depositItems: [
      {
        depositTitle: '押金',
        depositStatus: 2,
        explain: '取车时刷取信用卡预授权，还车后30-60天内退还',
        depositDesc: '押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541）',
      },
      {
        depositTitle: '支持卡种',
        depositDesc:
          '带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店不支持卡面带银联标志',
        creditUrlList: [
          'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
          'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
        ],
      },
    ],
    depositItemName: '押金详情',
  },
  rentalEssential: {
    mustRead: [
      {
        title: '用车须知',
        subObject: [
          {
            title: '自驾政策',
            type: 0,
            url: 'https://m.ctrip.com/tangram/ODA0ODA=?ctm_ref=vactang_page_80480&isHideNavBar=YES&apppgid=10650039393&statusBarStyle=1&channelid=237777',
            note: '请提前了解当地交规、加油、停车等政策',
          },
          {
            title: '租车指南',
            type: 1,
            url: 'https://trip.fat1.qa.nt.ctripcorp.com/carhire/materialsMustRead?orderId=1128168993028463&uid=M2258803416&locale=zh-CN&tab=2&isHideNavBar=YES',
            note: '请提前了解验车、核对合同、事故处理等步骤',
          },
        ],
      },
    ],
    pickUpMaterial: [
      {
        title: '中国大陆护照',
      },
      {
        title: '中国驾照原件 + 驾照国际翻译认证件',
      },
      {
        title: '中国驾照原件 + 当地语言公证件',
      },
      {
        title: '国际信用卡(带芯片、卡号凸字、不带银联标记)',
      },
    ],
  },
  receipt: {
    desc: '境外租车无法提供国内发票。itinerary作为一种常用消费凭证，一般可以用于报销',
    url: 'https://gateway.m.fat10668.qa.nt.ctripcorp.com/webapp/carhire/xsd/osdinvoice?id=%s&hideHeader=true&subEnv=fat12014',
  },
  cancelRuleInfo: {
    longTitle: 'In the following circumstances, you will not receive a refund:',
    ruleList: [
      '- You pick up the car late or drop it off early',
      '- You do not pick up the car within the given timeframe',
      '- You do not provide the required documents when picking up the car',
      "- The main driver's credit card does not have sufficient funds",
    ],
    cancelTip: {
      cancelTip: 'This booking can be canceled',
      cancelTipColor: 1,
    },
    customerCurrency: 'CNY',
    topTips: [
      {
        title: '免费取消',
        type: 1,
        description: '当前时间7月24日17:00前',
      },
      {
        title: '取消需收费',
        type: 0,
        description: '当前时间7月24日17:00后',
      },
    ],
    osdCancelRuleInfo: {
      code: 'FreeCancel',
      title: '取消政策',
      complexSubTitle: {
        contentStyle: '1',
        stringObjs: [
          {
            content: '当地时间7月24日17:00前可免费取消',
            style: '5',
          },
          {
            content: '，',
            style: '6',
          },
          {
            content: '当地时间7月24日17:00前可免费取消',
            style: '6',
          },
        ],
      },
      showFree: true,
      type: 300,
      description: '注意：均为当地时间',
      subTitle: '当地时间7月24日17:00前可免费取消',
      items: [
        {
          showFree: true,
          lossFee: 0,
          title: '2025-07-24 17:00前',
          subTitle: '取车前1小时前',
          description: '可免费取消',
          key: '837005',
        },
        {
          showFree: false,
          lossFee: 21,
          title: '2025-07-24 17:00后',
          subTitle: '取车前1小时后',
          description: '取消将收取租金10%作为违约金',
          key: '837012',
        },
      ],
    },
  },
  authType: 0,
  orderPrice: {
    localCurrency: 'THB',
    localPayAmountOnArrival: 0,
    feeDetail: {
      chargesInfos: [
        {
          title: '车辆租金+基础套餐',
        },
        {
          code: 'Car',
          title: '基础租车费用',
          size: '约¥205×1天',
          currencyCode: 'CNY',
          currentTotalPrice: 205,
          description: '',
          type: 8,
          payMode: 2,
          items: [
            {
              code: 'Car',
              title: '基础租车费用',
              descList: [
                '满电取还, 满电取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 基本租车费用, 不限里程',
              ],
              description:
                '满电取还, 满电取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 基本租车费用, 不限里程',
              type: 8,
            },
            {
              title: '车辆碰撞保障',
              type: 1,
              code: 'Insurance',
            },
          ],
        },
      ],
      notIncludeCharges: {},
      rentalTerm: 1,
      chargesSummary: {
        code: 'Summary',
        title: '全额',
        currencyCode: 'CNY',
        notices: [],
        currentTotalPrice: 205,
        type: 103,
        items: [
          {
            code: '1',
            currentTotalPrice: 205,
            title: '在线预付',
            currencyCode: 'CNY',
          },
        ],
      },
      equipmentInfos: [],
      couponInfos: [],
    },
    customerPayAmount: 205,
    totalAmount: 205,
    customerPayAmountOnArrival: 0,
    priceDesc: {
      possibleChangeList: [
        {
          title: '里程政策',
          description: '该车辆没有里程限制。',
        },
        {
          title: '能源政策',
          description: '满油取还',
        },
        {
          title: '年龄要求',
          description: '驾驶员年龄要求：25-65周岁',
        },
        {
          title: '额外驾驶员',
          description:
            '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
        },
        {
          title: '营业时间外取还车',
          description:
            '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
        },
        {
          title: '当地费用',
          description:
            '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
        },
        {
          title:
            '到店支付押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541）',
        },
      ],
    },
    customerCurrency: 'CNY',
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 943,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 943,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1750157613221,
    afterFetch: 1750157614164,
  },
};

// 新契约  已取消订单 1128168993028463 18862/queryOrderSecondOpen 18862/queryPreloadingOrderDetail
export const yqxddsecondRes = {
  vehicleInfo: {
    passengerNum: 4,
    transmission: 'AT',
    imageUrl: '',
    doorNum: 4,
    vehicleGroupName: '小型轿车',
    vehicleName: '日产 ',
    vendorVehicleCode: 'ECAV_NissanAlmera_d139fe91',
  },
  pickupStore: {
    businessTimePolicy: {
      content: [
        '18:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次¥444，费用及税费以实际支付为准。',
      ],
      summaryContent: ['18:00取车，门店预估收取服务费¥444。'],
      table: [
        {
          tableTitle: '时间段|服务费/次',
          code: 'BusinessTimePolicy',
          title: '服务费标准',
          notices: [
            '注意：请按时到达，如需提前或延后取还车务必提前联系门店。服务费和税费以实际支付为准。',
          ],
          showFree: false,
          type: 400,
          subTitle: '取车',
          items: [
            {
              showFree: false,
              positiveStatus: true,
              title: '18:00-23:59',
              description: '¥444',
            },
          ],
        },
      ],
    },
    localDateTimeDTO: {
      date: '2025年7月24日',
      time: '18:00',
    },
    longitude: 100.750112,
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    localDateTime: '2025-07-24 18:00:00',
    latitude: 13.689999,
    storeID: 176772,
    serviceType: '0',
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    storeCode: '2',
    storeName: 'Suvarnabhumi Airport',
    businessTimeDesc:
      '{"正常营业时间：":"00:00 - 17:59","收费营业时间：":"18:00 - 23:59"}',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  flight: {
    flightDelayRule: {
      title: '航班延误保留政策',
      rules: [
        {
          title:
            '如您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性、且供应商政策不同，可能无法为您保留车辆或无法免费取消订单，具体条款请以供应商政策为准，敬请谅解。',
        },
        {
          title: '建议您填写航班号，如遇延误请携带机票前往取车',
        },
      ],
      description: '航班延误保留政策',
    },
    flightNumber: 'EU8773',
  },
  checkResponseTime: 1750159934698.631,
  checkRequestTime: 1750159934614.44,
  baseResponse: {
    code: '0',
    requestId: 'b2f384ff-982a-4ad9-a399-d88e0d2c2d71',
    cost: 53,
    isSuccess: true,
    returnMsg: 'success',
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1750159934796+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '2812281332301108942',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a789841-486155-138759',
      },
    ],
  },
  timeInterval: 84.191162109375,
  resBodySize: 3275,
  returnStore: {
    businessTimePolicy: {
      content: [''],
    },
    localDateTimeDTO: {
      date: '2025年7月25日',
      time: '18:00',
    },
    longitude: 100.750112,
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    localDateTime: '2025-07-25 18:00:00',
    latitude: 13.689999,
    storeID: 176772,
    serviceType: '0',
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    storeCode: '2',
    storeName: 'Suvarnabhumi Airport',
    businessTimeDesc: '{"正常营业时间：":"24小时营业"}',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  orderBaseInfo: {
    orderId: 1128168993028463,
    orderStatus: {
      orderStatusCtrip: 'CAR_CANCELLED',
      orderStatusDesc: '已取消',
    },
    orderTip: {
      tipContentArray: [
        '您的订单已免费取消。款项将于1-5个工作天內退还至原付款账户，实际时间受您的银行影响。',
      ],
    },
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 242,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 242,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1750159934613,
    afterFetch: 1750159934855,
  },
};

// 新契约  已取消订单 1128168993028463 18862/OSDQueryOrder  18862/queryOsdOrderDetail
export const yqxddRes = {
  timeInterval: 559.617919921875,
  returnStore: {
    storeLocation: '门店位于素万那普国际机场到达大厅',
    businessTimePolicy: {
      content: [''],
    },
    storeBusinessTime: [
      {
        timeStr: '24小时营业',
        free: true,
      },
    ],
    localDateTimeDTO: {
      date: '2025年7月25日',
      time: '18:00',
    },
    longitude: 100.750112,
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    localDateTime: '2025-07-25 18:00:00',
    latitude: 13.689999,
    storeWay: '步行可达',
    storeID: 176772,
    location: {
      city: {
        id: 359,
        name: '曼谷',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      locationType: 1,
      locationName: '素万那普国际机场',
      continent: {
        id: 1,
        name: '亚洲',
      },
      poiInfo: {
        type: 1,
        longitude: 100.750112,
        latitude: 13.689999,
      },
      locationCode: 'BKK',
      province: {
        id: 0,
      },
    },
    userSearchLocation: '素万那普国际机场',
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    storeCode: '2',
    storeName: 'Suvarnabhumi Airport',
    serviceType: '0',
    countryId: 4,
    businessTimeDesc: '{"正常营业时间：":"24小时营业"}',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  addPayments: [],
  coverage: [
    {
      code: 'ULM',
      title: '不限里程',
      type: 2,
      description: '租期内没有公里数限制。\n',
    },
    {
      title: '满电取还',
      type: 4,
      code: 'FRFB',
    },
    {
      title: '满电取还',
      type: 4,
      code: 'FRFB',
    },
  ],
  orderBaseInfo: {
    orderStatus: {
      orderStatus: 4,
      orderStatusName: '已取消',
      orderStatusDesc: '已取消',
      orderStatusCtrip: 'CAR_CANCELLED',
    },
    orderId: 1128168993028463,
    uid: 'M2258803416',
    orderTip: {
      tipContentArray: [
        '您的订单已免费取消。款项将于1-5个工作天內退还至原付款账户，实际时间受您的银行影响。',
      ],
    },
    attr: {
      osdConfirmNew: 'true',
      voucherVersionV2: 'B',
      isContractTemplates: 'true',
      fulfillmentVersion: 'D',
      isVehicle2: '1',
      voucherVersion: 'B',
      osdModifyOrderVersion: 'B',
      ctripInsuranceVersion: 'B',
      klbVersion: '1',
      osdDetailVersion: 'B',
    },
    osdModifyNewOrder: false,
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1750159950643+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '6243631130002017025',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a789841-486155-140163',
      },
    ],
  },
  carAssistantSummary: [
    {
      title: '查看具体要求',
      type: 1,
      url: 'https://www.fat10668.qa.nt.ctripcorp.com/carhire/materialsMustRead?orderId=1128168993028463&h5View=1&locale=zh-CN&orderStatus=CAR_CANCELLED&vendorId=14059&countryId=4&isHideNavBar=YES&subEnv=fat14605&transparentbar=1&newOsdVoucherV2=1&orderLocale=zh-CN,th-TH,en-US',
    },
  ],
  operation: {
    extOperation: [],
    orderOperation: [
      {
        operationId: 4,
        buttonName: '再次预订',
        enable: true,
      },
    ],
  },
  resBodySize: 31551,
  driverInfo: {
    areaCode: '86',
    lastName: 'LI',
    firstName: 'LIU',
    contactWayList: [],
    age: '34',
    optionalContactWayList: [
      {
        contactWayName: '微信',
        isCanModify: true,
        contactWayType: '1',
      },
      {
        contactWayName: 'WhatsApp',
        isCanModify: true,
        contactWayType: '4',
      },
      {
        contactWayName: 'LINE',
        isCanModify: true,
        contactWayType: '2',
      },
      {
        contactWayName: 'KakaoTalk',
        isCanModify: true,
        contactWayType: '3',
      },
      {
        contactWayName: '当地电话',
        isCanModify: true,
        contactWayType: '0',
      },
    ],
    email: 's**<EMAIL>',
    flightNo: 'EU8773',
    isChangeContact: false,
    name: 'LI/LIU',
    telphone: '139****3823',
  },
  osdPolicy: {
    carRentalMustRead: [
      {
        code: '4',
        sortNum: 1,
        title: '额外驾驶员',
        type: 6,
      },
      {
        code: '4',
        sortNum: 2,
        title: '营业时间外取还车',
        type: 7,
      },
      {
        code: '4',
        sortNum: 3,
        title: '提前/延后取还车',
        type: 41,
      },
      {
        code: '4',
        sortNum: 4,
        title: '费用须知',
        type: 40,
      },
      {
        code: '1',
        sortNum: 1,
        title: '确认政策',
        content: ['预订此车型后可快速确认订单。\n'],
        type: 0,
      },
      {
        code: '1',
        sortNum: 2,
        title: '取消政策',
        content: [
          '取车前1小时前可免费取消;取车前1小时后取消将收取租金10%作为违约金',
        ],
        type: 1,
        table: [
          {
            code: 'FreeCancel',
            title: '取消政策',
            complexSubTitle: {
              contentStyle: '1',
              stringObjs: [
                {
                  content: '当地时间7月24日17:00前可免费取消',
                  style: '5',
                },
                {
                  content: '，',
                  style: '6',
                },
                {
                  content: '当地时间7月24日17:00前可免费取消',
                  style: '6',
                },
              ],
            },
            showFree: true,
            type: 300,
            description: '注意：均为当地时间',
            subTitle: '当地时间7月24日17:00前可免费取消',
            items: [
              {
                showFree: true,
                title: '2025-07-24 17:00前',
                subTitle: '取车前1小时前',
                description: '可免费取消',
                key: '837005',
              },
              {
                showFree: false,
                title: '2025-07-24 17:00后',
                subTitle: '取车前1小时后',
                description: '取消将收取租金10%作为违约金',
                key: '837012',
              },
            ],
          },
        ],
      },
      {
        code: '1',
        sortNum: 3,
        title: '押金说明',
        content: [
          '押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541），取车时刷取信用卡预授权，还车后30-60天内退还',
        ],
        type: 2,
        table: [
          {
            title: '押金',
            showFree: false,
            description:
              '押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541），取车时刷取信用卡预授权，还车后30-60天内退还',
          },
        ],
      },
      {
        code: '1',
        sortNum: 4,
        title: '里程政策',
        content: ['该车辆没有里程限制。'],
        type: 3,
      },
      {
        code: '1',
        sortNum: 5,
        title: '能源政策',
        content: [
          '满油取还',
          '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
        ],
        type: 4,
      },
      {
        code: '1',
        sortNum: 6,
        title: '年龄要求',
        content: [
          '驾驶员年龄要求：25-65周岁',
          '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
        ],
        type: 5,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '里程政策',
            content: ['该车辆没有里程限制。'],
            type: 3,
          },
          {
            code: '2',
            sortNum: 2,
            title: '能源政策',
            content: [
              '满油取还',
              '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
            ],
            type: 4,
          },
          {
            code: '2',
            sortNum: 3,
            title: '年龄要求',
            content: [
              '驾驶员年龄要求：25-65周岁',
              '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
              '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
              '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
            ],
            type: 5,
          },
          {
            code: '2',
            sortNum: 4,
            title: '额外驾驶员',
            content: [
              '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
            ],
            type: 6,
          },
          {
            code: '2',
            sortNum: 5,
            title: '营业时间外取还车',
            content: [
              '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
            ],
            type: 7,
          },
          {
            code: '2',
            sortNum: 6,
            title: '当地费用',
            content: [
              '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
            ],
            type: 16,
          },
        ],
        title: '费用须知',
        content: [],
        type: 40,
        sortNum: 1,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '提前取车',
            content: ['1'],
            type: 17,
          },
          {
            code: '2',
            sortNum: 2,
            title: '延迟取车',
            content: ['3'],
            type: 18,
          },
          {
            code: '2',
            sortNum: 3,
            title: '提前还车',
            content: ['5'],
            type: 10,
          },
          {
            code: '2',
            sortNum: 4,
            title: '延迟还车',
            content: ['7'],
            type: 11,
          },
        ],
        title: '提前/延后取还车',
        content: [],
        type: 41,
        sortNum: 2,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '签合同前核对金额',
            content: [
              '办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。',
            ],
            type: 59,
          },
          {
            code: '2',
            sortNum: 2,
            title: '验车留存有效凭证',
            content: [
              '取车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身、油量里程、车损特写等）。 \n因各地车损处理方式不同，部分国家并非及时确认车损 ，需要您保留有效还车车况凭证（注意开启相机拍照的时间显示功能，需证明照片拍摄时间），否则后续产生车损争议平台可能因当地行业操作，以供应商凭证为准。',
            ],
            type: 60,
          },
        ],
        title: '取还车注意事项',
        content: [],
        type: 58,
        sortNum: 3,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 2,
            title: '管理费',
            content: [
              '如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。',
              '此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。',
            ],
            type: 22,
          },
        ],
        title: '租车保障',
        content: [],
        type: 20,
        sortNum: 4,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '额外设备',
            content: [
              '额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。',
            ],
            type: 24,
          },
          {
            code: '2',
            sortNum: 2,
            title: '税费',
            content: ['所有额外服务将会收取销售税费和当地费用。'],
            type: 25,
          },
        ],
        title: '附加服务',
        content: [],
        type: 23,
        sortNum: 5,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '修改',
            content: [
              '如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。',
            ],
            type: 31,
          },
          {
            code: '2',
            sortNum: 2,
            title: 'No show（未取车）',
            content: [
              'noshow 是指当你：',
              '计划提前取消订单但未告知我们，或者',
              '未在规定的时间内取车，或者',
              '无法提供取车时要出示的文件，或者',
              '无法提供主驾驶员名下有足够额度的信用卡',
              '以上情形，你之前预定的订单金额不会退还给你。',
            ],
            type: 32,
          },
        ],
        title: '取消，未取车和修改',
        content: [],
        type: 42,
        sortNum: 7,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '意外或故障',
            content: [
              '事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。',
            ],
            type: 34,
          },
          {
            code: '2',
            sortNum: 2,
            title: '道路救援',
            content: [
              '道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。',
            ],
            type: 35,
          },
          {
            code: '2',
            sortNum: 3,
            title: '遗失钥匙',
            content: ['遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用'],
            type: 36,
          },
          {
            code: '2',
            sortNum: 4,
            title: '安全带',
            content: [
              '安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。',
            ],
            type: 37,
          },
          {
            code: '2',
            sortNum: 5,
            title: '禁止吸烟',
            content: [
              '禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。',
            ],
            type: 38,
          },
          {
            code: '2',
            sortNum: 6,
            title: '价格计算',
            content: [
              '价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。',
              '取车的时候，客人可能会选择不同的车，而不是预订时候的车。',
              '如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。',
              '如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。',
            ],
            type: 39,
          },
          {
            code: '2',
            sortNum: 7,
            title: '违章条款',
            content: [
              '请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中可能产生的违章罚款费用以及供应商代为交涉产生的行政费用（也称“违章管理费”）。\n由于车辆登记在租车公司名下，因此产生违章时，交管局会直接通知租车公司，车行将会把承租人信息提供给当局。所有租车合同中均包含信用卡自动扣费授权条款，用于处理违章和路费欠缴事宜。提车时主驾驶人需签字确认该条款，因此在发生违章时，租车公司通常不会提前通知您，直接从信用卡中扣除当局确认所产生的行政费用（通常按违章次数进行收取）。\n由于隐私法律的限制，大部分车行无法代缴罚金，是由当地相关部门以邮件或纸质平邮的形式，通过您在租车合同上预留的邮箱或者地址通知您。',
            ],
            type: 13,
          },
        ],
        title: '租车公司重要信息',
        content: [],
        type: 33,
        sortNum: 8,
      },
    ],
    crossLocationsPolicy: {
      title: '旅行限制',
      crossLocationsInfos: [
        {
          crossType: 3,
          crossTypeName: '跨境政策',
          summaryPolicies: [
            '由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨境，建议您更换其它租车公司或车型组。',
          ],
        },
      ],
    },
  },
  vehicleInfo: {
    fuelNoteTitle: '汽油或柴油',
    seriesWarning: {
      introduce: {
        title: '同组车型',
        description:
          '同组车型是指按照车型功能、空间大小、座位数和车门数划分的紧凑型轿车、中型轿车、小型SUV等等这些分类。您在网上预订时选择的车并不能保证到店就一定取到一模一样的，可能会得到同组车型中的其他车。',
      },
      carProtection: {
        title: '取车保障',
        description:
          '在库存充足的情况下，车行会尽量提供您在预订时所选的车型\n如果这款车暂时没有库存，车行便会为您提供同车型组内的其他车型\n若因门店原因需要调整车型，不额外收取车型变更费用',
      },
      video: 'https://ak-v.tripcdn.com/videos/9D0g2r000001j0rrk1E0A.mp4',
      cover: 'https://ak-d.tripcdn.com/images/1tg1u12000dwdf1uw1DCD.png',
      cases: [
        {
          vehicleGroupName: '车型组',
          representativeVehicleName: '代表车型',
          vehicleGroupItems: '车组包含',
          vehicleGroupCode: 'default',
        },
        {
          vehicleGroupName: '紧凑型轿车',
          representativeVehicleName: '福特嘉年华',
          vehicleGroupItems: '丰田卡罗拉、现代雅绅特',
          vehicleGroupCode: 'D',
        },
        {
          vehicleGroupName: '中大型轿车',
          representativeVehicleName: '丰田凯美瑞',
          vehicleGroupItems: '雪佛兰迈锐宝、大众帕萨特',
          vehicleGroupCode: 'S',
        },
        {
          vehicleGroupName: '中大型SUV',
          representativeVehicleName: '大众途观',
          vehicleGroupItems: '丰田RAV4、吉普指南者',
          vehicleGroupCode: 'R',
        },
      ],
    },
    fuelType: 2416,
    vendorVehicleCode: 'ECAV_NissanAlmera_d139fe91',
    vehicleId: 543649,
    vehicleDisplacement: '',
    vehicleGroupName: '小型轿车',
    transmission: 'AT',
    esgInfo: {},
    passengerNum: 4,
    imageUrl: '',
    sippCode: 'ECAV',
    vehicleImgUrl: '',
    hasAC: false,
    fourDrive: false,
    luggageNum: 0,
    doorNum: 4,
    vehicleGroupId: 1001,
    special: false,
    transmissionType: 1,
    fuelNote:
      '门店会根据库存情况为您提供汽油车或者柴油车，具体燃油类型与加油型号以门店告知为准。',
    vehicleName: '日产 ',
  },
  pickupStore: {
    location: {
      city: {
        id: 359,
        name: '曼谷',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      locationType: 1,
      locationName: '素万那普国际机场',
      continent: {
        id: 1,
        name: '亚洲',
      },
      poiInfo: {
        type: 1,
        longitude: 100.750112,
        latitude: 13.689999,
      },
      locationCode: 'BKK',
      province: {
        id: 0,
      },
    },
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    storeCode: '2',
    storeLocation: '门店位于素万那普国际机场到达大厅',
    localDateTime: '2025-07-24 18:00:00',
    storeName: 'Suvarnabhumi Airport',
    outOfHourDescription:
      '18:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次¥444，费用及税费以实际支付为准。',
    storeBusinessTime: [
      {
        timeStr: '00:00 - 17:59',
        free: true,
      },
      {
        timeStr: '18:00 - 23:59',
        free: false,
      },
    ],
    latitude: 13.689999,
    storeOpenTimeDesc: '{"":"24小时营业"}',
    countryId: 4,
    guideStep: [
      {
        image:
          'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R4331200003to07133D1.png',
        content: 'Drive Car Rental柜台位于二楼的国际到达大厅，7-8号出口之间。',
      },
      {
        image:
          'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R43z1200003to073876B.jpg',
        content:
          '国际或国内到达的乘客：从行李领取区出来后需步行到国际到达大厅，然后直接前往7号出口，我们的柜台位于7-8号出口之间，写有蓝色的“Drive Car Rental”标志。',
      },
      {
        image:
          'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R4601200003to0dz75E5.jpg',
        content:
          '办理完手续后，下到一楼从7号出口出去，穿过马路到外侧道路，跟随写有“Car Rental”的指示牌前往停车场，在“Drive Car Rental"的指示标志处找工作人员取车。',
      },
    ],
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    localDateTimeDTO: {
      date: '2025年7月24日',
      time: '18:00',
    },
    businessTimeDesc:
      '{"正常营业时间：":"00:00 - 17:59","收费营业时间：":"18:00 - 23:59"}',
    longitude: 100.750112,
    storeWay: '步行可达',
    businessTimePolicy: {
      content: [
        '18:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次¥444，费用及税费以实际支付为准。',
      ],
      summaryContent: ['18:00取车，门店预估收取服务费¥444。'],
      table: [
        {
          tableTitle: '时间段|服务费/次',
          code: 'BusinessTimePolicy',
          title: '服务费标准',
          notices: [
            '注意：请按时到达，如需提前或延后取还车务必提前联系门店。服务费和税费以实际支付为准。',
          ],
          showFree: false,
          type: 400,
          subTitle: '取车',
          items: [
            {
              showFree: false,
              positiveStatus: true,
              title: '18:00-23:59',
              description: '¥444',
            },
          ],
        },
      ],
    },
    serviceType: '0',
    waitTimeDesc: '',
    userSearchLocation: '素万那普国际机场',
    storeID: 176772,
  },
  baseResponse: {
    code: '200',
    requestId: '4847df0d-13c8-41dc-938d-06bdf176941f',
    cost: 509,
    isSuccess: true,
    returnMsg: 'success',
  },
  flight: {
    flightDelayRule: {
      title: '航班延误保留政策',
      rules: [
        {
          title:
            '如您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性、且供应商政策不同，可能无法为您保留车辆或无法免费取消订单，具体条款请以供应商政策为准，敬请谅解。',
        },
        {
          title: '建议您填写航班号，如遇延误请携带机票前往取车',
        },
      ],
      description: '航班延误保留政策',
    },
    flightNumber: 'EU8773',
  },
  osdRefund: {
    refundProgressList: [
      {
        remark: '退款',
        dealStatus: 1,
        refundAmount: 205,
        billStatus: 'SUCCESS',
        dealTime: '/Date(*************+0800)/',
        paymentWayID: 'CashAccountPay',
        refundPeriodKey: '',
        paymentWayName: '现金余额',
        currency: 'CNY',
        createTime: '/Date(*************+0800)/',
        clientSubmitCardNoTime: '',
      },
    ],
  },
  checkResponseTime: *************.309,
  checkRequestTime: *************.6912,
  continuePayInfo: {
    needContinuePay: false,
  },
  platformInsurance: {
    insPackageId: 1,
    extraDesc: [
      '',
      '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比<url>https://www.baidu.com</url>实际赔付范围与标准以门店合同为准-卡拉比',
    ],
    packageInfos: [
      {
        insPackageId: 1,
        naked: false,
        defaultBomCode: '',
        currencyCode: '',
        defaultPackageId: 390412,
        gapPrice: 0,
        isDefault: true,
        stepPrice: 0,
        lowestDailyPrice: 0,
        packageName: '基础套餐',
        guaranteeDegree: 0,
      },
    ],
    insuranceItems: [
      {
        productId: 390412,
        description: '保障车辆碰撞损失',
        converageExplain: {
          title: '承保范围',
          content: [
            '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后',
          ],
        },
        excessTable: {
          title: '起赔额详见门店合同',
          subObject: [
            {
              title: '车行承担',
              content: ['起赔额以上部分（起赔额详见门店合同）'],
            },
            {
              title: '客户或承租方承担',
              content: ['起赔额及以下部分（据实承担）'],
            },
          ],
        },
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '发生事故后报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        type: 7,
        title: '车辆碰撞保障',
        code: 'CDW',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额详见门店合同',
            excessShortDesc: '起赔额详见门店合同',
            currencyCode: '',
            coverageWithPlatformInsurance: '起赔额详见门店合同',
            packageId: 390412,
          },
        ],
        unConverageExplain: {
          title: '不承保范围',
          content: [
            '不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准',
          ],
        },
        isInclude: true,
        name: '车辆碰撞保障',
        isFromCtrip: false,
      },
    ],
    briefInsuranceItems: [
      {
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额详见门店合同',
            excessShortDesc: '起赔额详见门店合同',
            currencyCode: '',
            coverageWithPlatformInsurance: '起赔额详见门店合同',
            packageId: 390412,
          },
        ],
        code: 'CDW',
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '发生事故后报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        isInclude: true,
        description: '保障车辆碰撞损失',
        type: 7,
        name: '车辆碰撞保障',
        isFromCtrip: false,
      },
    ],
    claimsProcess: [],
    reminder: [
      {
        subText:
          '如签署合同时，发现额外收费或强制购买，请不要签字，当场联系携程客服协助解决。',
        text: '温馨提示：',
      },
    ],
    ctripInsDeclaration: [
      {
        purchasedDesc:
          '该客人的保险套餐已满足当地法律的最低要求，请避免强迫客人添加任何保险，否则可能导致我处对相关当局的投诉。',
        requirement:
          "如果认为某项保险是必须的，请写明'xx保险是必须的'并附上签名和日期。",
        partner: '亲爱的供应商伙伴：',
        statement: '携程声明',
      },
      {
        purchasedDesc:
          'แพ็คเกจประกันภัยของลูกค้าครอบคลุมตามข้อกำหนดขั้นต่ำของกฎหมายท้องถิ่นแล้ว โปรดหลีกเลี่ยงการกดดันให้ลูกค้าซื้อประกันภัยเพิ่มเติม การกระทำดังกล่าวอาจทำให้เกิดข้อร้องเรียนได้',
        requirement:
          'หากคุณเชื่อว่าต้องมีประกันภัยบางประเภท โปรดระบุเป็นลายลักษณ์อักษรว่า "ต้องมีประกันภัย xx" พร้อมลงลายมือชื่อของคุณและวันที่',
        partner: 'เรียน ซัพพลายเออร์พาร์ทเนอร์:',
        statement: 'ใบแจ้งยอดของ Trip.com',
      },
      {
        purchasedDesc:
          "The customer's insurance package already meets minimum local law requirements. Please avoid pressuring the customer to add any insurance. Doing so may result in complaints being raised.",
        requirement:
          'If you believe a certain type of insurance is required, please indicate in writing that "xx insurance is required" and include your signature and date.',
        partner: 'Dear supplier partner:',
        statement: 'Trip.com Statement',
      },
    ],
  },
  freeDeposit: {
    depositExplainV2: [
      {
        explain:
          '到店支付押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541）',
        badDebt: false,
      },
    ],
    depositStatus: 0,
    depositItems: [
      {
        depositTitle: '押金',
        depositStatus: 2,
        explain: '取车时刷取信用卡预授权，还车后30-60天内退还',
        depositDesc: '押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541）',
      },
      {
        depositTitle: '支持卡种',
        depositDesc:
          '带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店不支持卡面带银联标志',
        creditUrlList: [
          'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
          'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
        ],
      },
    ],
    depositItemName: '押金详情',
  },
  rentalEssential: {
    mustRead: [],
    pickUpMaterial: [
      {
        title: '中国大陆护照',
      },
      {
        title: '中国驾照原件 + 驾照国际翻译认证件',
      },
      {
        title: '中国驾照原件 + 当地语言公证件',
      },
      {
        title: '国际信用卡(带芯片、卡号凸字、不带银联标记)',
      },
    ],
  },
  receipt: {
    desc: '境外租车无法提供国内发票。itinerary作为一种常用消费凭证，一般可以用于报销',
    url: 'https://gateway.m.fat10668.qa.nt.ctripcorp.com/webapp/carhire/xsd/osdinvoice?id=%s&hideHeader=true&subEnv=fat14855',
  },
  cancelRuleInfo: {
    longTitle: 'In the following circumstances, you will not receive a refund:',
    ruleList: [
      '- You pick up the car late or drop it off early',
      '- You do not pick up the car within the given timeframe',
      '- You do not provide the required documents when picking up the car',
      "- The main driver's credit card does not have sufficient funds",
    ],
    cancelTip: {
      cancelTip: 'Booking canceled',
      cancelTipColor: 0,
    },
    customerCurrency: 'CNY',
    topTips: [
      {
        title: '可免费取消',
        type: 1,
        description: '当前时间7月24日17:00前',
      },
      {
        title: '取消需收费',
        type: 0,
        description: '当前时间7月24日17:00后',
      },
    ],
    osdCancelRuleInfo: {
      code: 'FreeCancel',
      title: '取消政策',
      complexSubTitle: {
        contentStyle: '1',
        stringObjs: [
          {
            content: '当地时间7月24日17:00前可免费取消',
            style: '5',
          },
          {
            content: '，',
            style: '6',
          },
          {
            content: '当地时间7月24日17:00前可免费取消',
            style: '6',
          },
        ],
      },
      showFree: true,
      type: 300,
      description: '注意：均为当地时间',
      subTitle: '当地时间7月24日17:00前可免费取消',
      items: [
        {
          showFree: true,
          lossFee: 0,
          title: '2025-07-24 17:00前',
          subTitle: '取车前1小时前',
          description: '可免费取消',
          key: '837005',
        },
        {
          showFree: false,
          lossFee: 0,
          title: '2025-07-24 17:00后',
          subTitle: '取车前1小时后',
          description: '取消将收取租金10%作为违约金',
          key: '837012',
        },
      ],
    },
  },
  authType: 0,
  orderPrice: {
    localCurrency: 'THB',
    localPayAmountOnArrival: 0,
    feeDetail: {
      chargesInfos: [
        {
          title: '车辆租金+基础套餐',
        },
        {
          code: 'Car',
          title: '基础租车费用',
          size: '约¥205×1天',
          currencyCode: 'CNY',
          currentTotalPrice: 205,
          description: '',
          type: 8,
          payMode: 2,
          items: [
            {
              code: 'Car',
              title: '基础租车费用',
              descList: [
                '满电取还, 满电取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 基本租车费用, 不限里程',
              ],
              description:
                '满电取还, 满电取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 基本租车费用, 不限里程',
              type: 8,
            },
            {
              title: '车辆碰撞保障',
              type: 1,
              code: 'Insurance',
            },
          ],
        },
      ],
      notIncludeCharges: {},
      rentalTerm: 1,
      chargesSummary: {
        code: 'Summary',
        title: '全额',
        currencyCode: 'CNY',
        notices: [],
        currentTotalPrice: 205,
        type: 103,
        items: [
          {
            code: '1',
            currentTotalPrice: 205,
            title: '在线预付',
            currencyCode: 'CNY',
          },
        ],
      },
      equipmentInfos: [],
      couponInfos: [],
    },
    customerPayAmount: 205,
    totalAmount: 205,
    customerPayAmountOnArrival: 0,
    priceDesc: {
      possibleChangeList: [
        {
          title: '里程政策',
          description: '该车辆没有里程限制。',
        },
        {
          title: '能源政策',
          description: '满油取还',
        },
        {
          title: '年龄要求',
          description: '驾驶员年龄要求：25-65周岁',
        },
        {
          title: '额外驾驶员',
          description:
            '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
        },
        {
          title: '营业时间外取还车',
          description:
            '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
        },
        {
          title: '当地费用',
          description:
            '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
        },
        {
          title:
            '到店支付押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541）',
        },
      ],
    },
    customerCurrency: 'CNY',
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 571,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 571,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1750159949997,
    afterFetch: 1750159950568,
  },
};

// 新契约  已确认订单 1128168993028463 18862/queryOrderSecondOpen 18862/queryPreloadingOrderDetail
export const yqrddsecondRes = {
  vehicleInfo: {
    passengerNum: 4,
    transmission: 'AT',
    imageUrl: '',
    doorNum: 4,
    vehicleGroupName: '小型轿车',
    vehicleName: '日产 ',
    vendorVehicleCode: 'ECAV_NissanAlmera_d139fe91',
  },
  pickupStore: {
    businessTimePolicy: {
      content: [
        '18:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次¥444，费用及税费以实际支付为准。',
      ],
      summaryContent: ['18:00取车，门店预估收取服务费¥444。'],
      table: [
        {
          tableTitle: '时间段|服务费/次',
          code: 'BusinessTimePolicy',
          title: '服务费标准',
          notices: [
            '注意：请按时到达，如需提前或延后取还车务必提前联系门店。服务费和税费以实际支付为准。',
          ],
          showFree: false,
          type: 400,
          subTitle: '取车',
          items: [
            {
              showFree: false,
              positiveStatus: true,
              title: '18:00-23:59',
              description: '¥444',
            },
          ],
        },
      ],
    },
    localDateTimeDTO: {
      date: '2025年7月24日',
      time: '18:00',
    },
    longitude: 100.750112,
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    localDateTime: '2025-07-24 18:00:00',
    latitude: 13.689999,
    storeID: 176772,
    serviceType: '0',
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    storeCode: '2',
    storeName: 'Suvarnabhumi Airport',
    businessTimeDesc:
      '{"正常营业时间：":"00:00 - 17:59","收费营业时间：":"18:00 - 23:59"}',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  flight: {
    flightDelayRule: {
      title: '航班延误保留政策',
      rules: [
        {
          title:
            '如您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性、且供应商政策不同，可能无法为您保留车辆或无法免费取消订单，具体条款请以供应商政策为准，敬请谅解。',
        },
        {
          title: '建议您填写航班号，如遇延误请携带机票前往取车',
        },
      ],
      description: '航班延误保留政策',
    },
    flightNumber: '',
  },
  checkResponseTime: 1750161516500.086,
  checkRequestTime: 1750161516420.2761,
  baseResponse: {
    code: '0',
    requestId: 'e42b69b4-80c8-4da3-9527-581fae3c5d72',
    cost: 46,
    isSuccess: true,
    returnMsg: 'success',
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1750161516618+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '2077132877644348765',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a79d07a-486155-291845',
      },
    ],
  },
  timeInterval: 79.809814453125,
  resBodySize: 3124,
  returnStore: {
    businessTimePolicy: {
      content: [''],
    },
    localDateTimeDTO: {
      date: '2025年7月25日',
      time: '18:00',
    },
    longitude: 100.750112,
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    localDateTime: '2025-07-25 18:00:00',
    latitude: 13.689999,
    storeID: 176772,
    serviceType: '0',
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    storeCode: '2',
    storeName: 'Suvarnabhumi Airport',
    businessTimeDesc: '{"正常营业时间：":"24小时营业"}',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  orderBaseInfo: {
    orderId: 1128168993042720,
    orderStatus: {
      orderStatusCtrip: 'CAR_CONFIRMED',
      orderStatusDesc: '已确认',
    },
    orderTip: {},
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 295,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 295,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1750161516418,
    afterFetch: 1750161516713,
  },
};

// 新契约  已确认订单 1128168993028463 18862/OSDQueryOrder  18862/queryOsdOrderDetail
export const yqrddRes = {
  timeInterval: 701.***********,
  returnStore: {
    storeLocation: '门店位于素万那普国际机场到达大厅',
    businessTimePolicy: {
      content: [''],
    },
    storeBusinessTime: [
      {
        timeStr: '24小时营业',
        free: true,
      },
    ],
    localDateTimeDTO: {
      date: '2025年7月25日',
      time: '18:00',
    },
    longitude: 100.750112,
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    localDateTime: '2025-07-25 18:00:00',
    latitude: 13.689999,
    storeWay: '步行可达',
    storeID: 176772,
    location: {
      city: {
        id: 359,
        name: '曼谷',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      locationType: 1,
      locationName: '素万那普国际机场',
      continent: {
        id: 1,
        name: '亚洲',
      },
      poiInfo: {
        type: 1,
        longitude: 100.750112,
        latitude: 13.689999,
      },
      locationCode: 'BKK',
      province: {
        id: 0,
      },
    },
    userSearchLocation: '素万那普国际机场',
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    storeCode: '2',
    storeName: 'Suvarnabhumi Airport',
    serviceType: '0',
    countryId: 4,
    businessTimeDesc: '{"正常营业时间：":"24小时营业"}',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  addPayments: [],
  ces: {
    title: '本次预订租车的过程{tag}？ ',
    subTitle: '是否方便',
    url: 'https://trippoll.domestic.fat-1.qa.nt.ctripcorp.com/trippollweb/newpollanswer?popup=close&surveygUID=6f183252-8818-46df-82cb-1bf4cfa37f6d&locale=zh-cn&needlogin=1',
  },
  coverage: [
    {
      code: 'ULM',
      title: '不限里程',
      type: 2,
      description: '租期内没有公里数限制。\n',
    },
    {
      title: '满电取还',
      type: 4,
      code: 'FRFB',
    },
    {
      title: '满电取还',
      type: 4,
      code: 'FRFB',
    },
  ],
  orderBaseInfo: {
    orderStatus: {
      orderStatus: 2,
      orderStatusName: '已确认',
      orderStatusDesc: '已确认',
      orderStatusCtrip: 'CAR_CONFIRMED',
    },
    orderId: 1128168993042720,
    uid: 'M2258803416',
    orderTip: {
      tipContentsWithTag: {
        tagDesc: '车辆已为您保留，取车时请携带{tag}',
        url: 'https://www.fat10668.qa.nt.ctripcorp.com/carhire/pickupVoucher?orderId=null&h5View=1&locale=zh-CN&isHideNavBar=YES&orderStatus=CAR_CONFIRMED&countryId=null&subEnv=fat13115&transparentbar=1',
        tagContent: '提车凭证',
      },
    },
    attr: {
      osdConfirmNew: 'true',
      voucherVersionV2: 'B',
      isContractTemplates: 'true',
      fulfillmentVersion: 'D',
      isVehicle2: '1',
      voucherVersion: 'B',
      osdModifyOrderVersion: 'B',
      ctripInsuranceVersion: 'B',
      klbVersion: '1',
      mergeVoucherFlag: '1',
      osdDetailVersion: 'B',
    },
    osdModifyNewOrder: false,
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1750161517414+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '1862600022767981144',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a779e61-486155-275405',
      },
    ],
  },
  carAssistantSummary: [
    {
      title: '08/31泰国自驾租车群',
      subTitle: '当地交规 客服答疑 车友交流',
      type: 3,
      button: {
        weChatUrl:
          'ctrip://wireless/login/openMiniProgram?username=gh_36ada103ba97&type=0&path=&edata=93714f221d4f38c34a489f221360657211346b8c337885172ae91f64ec9b5cab33a1441d252e11ea5339714a77459d730c674f042d5cf13ab5347f4d79c06149ff54bf47ff801b9a30ecb5a1e6719b74',
        title: '立即入群',
        h5Url:
          'https://m.fat28.qa.nt.ctripcorp.com/webapp/cars/marketing/bridgeWx?&edata=93714f221d4f38c34a489f221360657211346b8c337885172ae91f64ec9b5cab33a1441d252e11ea5339714a77459d730c674f042d5cf13ab5347f4d79c06149ff54bf47ff801b9a30ecb5a1e6719b74',
        appWeChatUrl:
          'ctrip://wireless/login/openMiniProgram?username=gh_36ada103ba97&type=0&path=Y3RyaXA6Ly93aXJlbGVzcy9sb2dpbi9vcGVuTWluaVByb2dyYW0/dXNlcm5hbWU9Z2hfMzZhZGExMDNiYTk3JnR5cGU9MCZwYXRoPSZlZGF0YT05MzcxNGYyMjFkNGYzOGMzNGE0ODlmMjIxMzYwNjU3MjExMzQ2YjhjMzM3ODg1MTcyYWU5MWY2NGVjOWI1Y2FiMzNhMTQ0MWQyNTJlMTFlYTUzMzk3MTRhNzc0NTlkNzMwYzY3NGYwNDJkNWNmMTNhYjUzNDdmNGQ3OWMwNjE0OWZmNTRiZjQ3ZmY4MDFiOWEzMGVjYjVhMWU2NzE5Yjc0&channelId=1',
        statusType: 1,
      },
    },
    {
      title: '取还车拍照留证',
      type: 7,
    },
    {
      title: '查看具体要求',
      type: 1,
      url: 'https://www.fat10668.qa.nt.ctripcorp.com/carhire/materialsMustRead?orderId=1128168993042720&h5View=1&locale=zh-CN&orderStatus=CAR_CONFIRMED&vendorId=14059&countryId=4&isHideNavBar=YES&subEnv=fat14605&transparentbar=1&newOsdVoucherV2=1&orderLocale=zh-CN,th-TH,en-US',
    },
  ],
  operation: {
    extOperation: [
      {
        operationId: 17,
        buttonName: 'Resend Confirmation Email',
        enable: true,
      },
    ],
    orderOperation: [
      {
        operationId: 2,
        enable: true,
        buttonName: '取消订单',
        contents: [
          {
            description:
              '租车价格库存随时存在波动，重新预定价格可能会上涨，请你再次确认是否取消？',
          },
        ],
      },
      {
        operationId: 5,
        enable: true,
        buttonName: '提车凭证',
        url: 'https://m.fat10668.qa.nt.ctripcorp.com/carhire/pickupVoucher2?orderId=1128168993042720&locale=zh-CN&orderLocale=zh-CN,th-TH,en-US&newOsdVoucherV2=1&transparentbar=1&isHideNavBar=YES&orderStatus=CAR_CONFIRMED&countryId=4&subEnv=fat13115',
      },
    ],
  },
  resBodySize: 33872,
  driverInfo: {
    areaCode: '86',
    lastName: 'LI',
    firstName: 'LIU',
    contactWayList: [],
    age: '34',
    optionalContactWayList: [
      {
        contactWayName: '微信',
        isCanModify: true,
        contactWayType: '1',
      },
      {
        contactWayName: 'WhatsApp',
        isCanModify: true,
        contactWayType: '4',
      },
      {
        contactWayName: 'LINE',
        isCanModify: true,
        contactWayType: '2',
      },
      {
        contactWayName: 'KakaoTalk',
        isCanModify: true,
        contactWayType: '3',
      },
      {
        contactWayName: '当地电话',
        isCanModify: true,
        contactWayType: '0',
      },
    ],
    email: 's**<EMAIL>',
    flightNo: '',
    isChangeContact: false,
    name: 'LI/LIU',
    telphone: '139****3823',
  },
  osdPolicy: {
    carRentalMustRead: [
      {
        code: '4',
        sortNum: 1,
        title: '额外驾驶员',
        type: 6,
      },
      {
        code: '4',
        sortNum: 2,
        title: '营业时间外取还车',
        type: 7,
      },
      {
        code: '4',
        sortNum: 3,
        title: '提前/延后取还车',
        type: 41,
      },
      {
        code: '4',
        sortNum: 4,
        title: '费用须知',
        type: 40,
      },
      {
        code: '1',
        sortNum: 1,
        title: '确认政策',
        content: ['预订此车型后可快速确认订单。\n'],
        type: 0,
      },
      {
        code: '1',
        sortNum: 2,
        title: '取消政策',
        content: [
          '取车前1小时前可免费取消;取车前1小时后取消将收取租金10%作为违约金',
        ],
        type: 1,
        table: [
          {
            code: 'FreeCancel',
            title: '取消政策',
            complexSubTitle: {
              contentStyle: '1',
              stringObjs: [
                {
                  content: '当地时间7月24日17:00前可免费取消',
                  style: '5',
                },
                {
                  content: '，',
                  style: '6',
                },
                {
                  content: '当地时间7月24日17:00前可免费取消',
                  style: '6',
                },
              ],
            },
            showFree: true,
            type: 300,
            description: '注意：均为当地时间',
            subTitle: '当地时间7月24日17:00前可免费取消',
            items: [
              {
                showFree: true,
                title: '2025-07-24 17:00前',
                subTitle: '取车前1小时前',
                description: '可免费取消',
                key: '837789',
              },
              {
                showFree: false,
                title: '2025-07-24 17:00后',
                subTitle: '取车前1小时后',
                description: '取消将收取租金10%作为违约金',
                key: '837796',
              },
            ],
          },
        ],
      },
      {
        code: '1',
        sortNum: 3,
        title: '押金说明',
        content: [
          '押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541），取车时刷取信用卡预授权，还车后30-60天内退还',
        ],
        type: 2,
        table: [
          {
            title: '押金',
            showFree: false,
            description:
              '押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541），取车时刷取信用卡预授权，还车后30-60天内退还',
          },
        ],
      },
      {
        code: '1',
        sortNum: 4,
        title: '里程政策',
        content: ['该车辆没有里程限制。'],
        type: 3,
      },
      {
        code: '1',
        sortNum: 5,
        title: '能源政策',
        content: [
          '满油取还',
          '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
        ],
        type: 4,
      },
      {
        code: '1',
        sortNum: 6,
        title: '年龄要求',
        content: [
          '驾驶员年龄要求：25-65周岁',
          '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
        ],
        type: 5,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '里程政策',
            content: ['该车辆没有里程限制。'],
            type: 3,
          },
          {
            code: '2',
            sortNum: 2,
            title: '能源政策',
            content: [
              '满油取还',
              '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
            ],
            type: 4,
          },
          {
            code: '2',
            sortNum: 3,
            title: '年龄要求',
            content: [
              '驾驶员年龄要求：25-65周岁',
              '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
              '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
              '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
            ],
            type: 5,
          },
          {
            code: '2',
            sortNum: 4,
            title: '额外驾驶员',
            content: [
              '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
            ],
            type: 6,
          },
          {
            code: '2',
            sortNum: 5,
            title: '营业时间外取还车',
            content: [
              '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
            ],
            type: 7,
          },
          {
            code: '2',
            sortNum: 6,
            title: '当地费用',
            content: [
              '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
            ],
            type: 16,
          },
        ],
        title: '费用须知',
        content: [],
        type: 40,
        sortNum: 1,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '提前取车',
            content: ['1'],
            type: 17,
          },
          {
            code: '2',
            sortNum: 2,
            title: '延迟取车',
            content: ['3'],
            type: 18,
          },
          {
            code: '2',
            sortNum: 3,
            title: '提前还车',
            content: ['5'],
            type: 10,
          },
          {
            code: '2',
            sortNum: 4,
            title: '延迟还车',
            content: ['7'],
            type: 11,
          },
        ],
        title: '提前/延后取还车',
        content: [],
        type: 41,
        sortNum: 2,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '签合同前核对金额',
            content: [
              '办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。',
            ],
            type: 59,
          },
          {
            code: '2',
            sortNum: 2,
            title: '验车留存有效凭证',
            content: [
              '取车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身、油量里程、车损特写等）。 \n因各地车损处理方式不同，部分国家并非及时确认车损 ，需要您保留有效还车车况凭证（注意开启相机拍照的时间显示功能，需证明照片拍摄时间），否则后续产生车损争议平台可能因当地行业操作，以供应商凭证为准。',
            ],
            type: 60,
          },
        ],
        title: '取还车注意事项',
        content: [],
        type: 58,
        sortNum: 3,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 2,
            title: '管理费',
            content: [
              '如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。',
              '此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。',
            ],
            type: 22,
          },
        ],
        title: '租车保障',
        content: [],
        type: 20,
        sortNum: 4,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '额外设备',
            content: [
              '额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。',
            ],
            type: 24,
          },
          {
            code: '2',
            sortNum: 2,
            title: '税费',
            content: ['所有额外服务将会收取销售税费和当地费用。'],
            type: 25,
          },
        ],
        title: '附加服务',
        content: [],
        type: 23,
        sortNum: 5,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '修改',
            content: [
              '如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。',
            ],
            type: 31,
          },
          {
            code: '2',
            sortNum: 2,
            title: 'No show（未取车）',
            content: [
              'noshow 是指当你：',
              '计划提前取消订单但未告知我们，或者',
              '未在规定的时间内取车，或者',
              '无法提供取车时要出示的文件，或者',
              '无法提供主驾驶员名下有足够额度的信用卡',
              '以上情形，你之前预定的订单金额不会退还给你。',
            ],
            type: 32,
          },
        ],
        title: '取消，未取车和修改',
        content: [],
        type: 42,
        sortNum: 7,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '意外或故障',
            content: [
              '事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。',
            ],
            type: 34,
          },
          {
            code: '2',
            sortNum: 2,
            title: '道路救援',
            content: [
              '道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。',
            ],
            type: 35,
          },
          {
            code: '2',
            sortNum: 3,
            title: '遗失钥匙',
            content: ['遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用'],
            type: 36,
          },
          {
            code: '2',
            sortNum: 4,
            title: '安全带',
            content: [
              '安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。',
            ],
            type: 37,
          },
          {
            code: '2',
            sortNum: 5,
            title: '禁止吸烟',
            content: [
              '禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。',
            ],
            type: 38,
          },
          {
            code: '2',
            sortNum: 6,
            title: '价格计算',
            content: [
              '价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。',
              '取车的时候，客人可能会选择不同的车，而不是预订时候的车。',
              '如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。',
              '如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。',
            ],
            type: 39,
          },
          {
            code: '2',
            sortNum: 7,
            title: '违章条款',
            content: [
              '请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中可能产生的违章罚款费用以及供应商代为交涉产生的行政费用（也称“违章管理费”）。\n由于车辆登记在租车公司名下，因此产生违章时，交管局会直接通知租车公司，车行将会把承租人信息提供给当局。所有租车合同中均包含信用卡自动扣费授权条款，用于处理违章和路费欠缴事宜。提车时主驾驶人需签字确认该条款，因此在发生违章时，租车公司通常不会提前通知您，直接从信用卡中扣除当局确认所产生的行政费用（通常按违章次数进行收取）。\n由于隐私法律的限制，大部分车行无法代缴罚金，是由当地相关部门以邮件或纸质平邮的形式，通过您在租车合同上预留的邮箱或者地址通知您。',
            ],
            type: 13,
          },
        ],
        title: '租车公司重要信息',
        content: [],
        type: 33,
        sortNum: 8,
      },
    ],
    crossLocationsPolicy: {
      title: '旅行限制',
      crossLocationsInfos: [
        {
          crossType: 3,
          crossTypeName: '跨境政策',
          summaryPolicies: [
            '由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨境，建议您更换其它租车公司或车型组。',
          ],
        },
      ],
    },
  },
  vehicleInfo: {
    fuelNoteTitle: '汽油或柴油',
    seriesWarning: {
      introduce: {
        title: '同组车型',
        description:
          '同组车型是指按照车型功能、空间大小、座位数和车门数划分的紧凑型轿车、中型轿车、小型SUV等等这些分类。您在网上预订时选择的车并不能保证到店就一定取到一模一样的，可能会得到同组车型中的其他车。',
      },
      carProtection: {
        title: '取车保障',
        description:
          '在库存充足的情况下，车行会尽量提供您在预订时所选的车型\n如果这款车暂时没有库存，车行便会为您提供同车型组内的其他车型\n若因门店原因需要调整车型，不额外收取车型变更费用',
      },
      video: 'https://ak-v.tripcdn.com/videos/9D0g2r000001j0rrk1E0A.mp4',
      cover: 'https://ak-d.tripcdn.com/images/1tg1u12000dwdf1uw1DCD.png',
      cases: [
        {
          vehicleGroupName: '车型组',
          representativeVehicleName: '代表车型',
          vehicleGroupItems: '车组包含',
          vehicleGroupCode: 'default',
        },
        {
          vehicleGroupName: '紧凑型轿车',
          representativeVehicleName: '福特嘉年华',
          vehicleGroupItems: '丰田卡罗拉、现代雅绅特',
          vehicleGroupCode: 'D',
        },
        {
          vehicleGroupName: '中大型轿车',
          representativeVehicleName: '丰田凯美瑞',
          vehicleGroupItems: '雪佛兰迈锐宝、大众帕萨特',
          vehicleGroupCode: 'S',
        },
        {
          vehicleGroupName: '中大型SUV',
          representativeVehicleName: '大众途观',
          vehicleGroupItems: '丰田RAV4、吉普指南者',
          vehicleGroupCode: 'R',
        },
      ],
    },
    fuelType: 2416,
    vendorVehicleCode: 'ECAV_NissanAlmera_d139fe91',
    vehicleId: 543649,
    vehicleDisplacement: '',
    vehicleGroupName: '小型轿车',
    transmission: 'AT',
    esgInfo: {},
    passengerNum: 4,
    imageUrl: '',
    sippCode: 'ECAV',
    vehicleImgUrl: '',
    hasAC: false,
    fourDrive: false,
    luggageNum: 0,
    doorNum: 4,
    vehicleGroupId: 1001,
    special: false,
    transmissionType: 1,
    fuelNote:
      '门店会根据库存情况为您提供汽油车或者柴油车，具体燃油类型与加油型号以门店告知为准。',
    vehicleName: '日产 ',
  },
  pickupStore: {
    location: {
      city: {
        id: 359,
        name: '曼谷',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      locationType: 1,
      locationName: '素万那普国际机场',
      continent: {
        id: 1,
        name: '亚洲',
      },
      poiInfo: {
        type: 1,
        longitude: 100.750112,
        latitude: 13.689999,
      },
      locationCode: 'BKK',
      province: {
        id: 0,
      },
    },
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    storeCode: '2',
    storeLocation: '门店位于素万那普国际机场到达大厅',
    localDateTime: '2025-07-24 18:00:00',
    storeName: 'Suvarnabhumi Airport',
    outOfHourDescription:
      '18:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次¥444，费用及税费以实际支付为准。',
    storeBusinessTime: [
      {
        timeStr: '00:00 - 17:59',
        free: true,
      },
      {
        timeStr: '18:00 - 23:59',
        free: false,
      },
    ],
    latitude: 13.689999,
    storeOpenTimeDesc: '{"":"24小时营业"}',
    countryId: 4,
    guideStep: [
      {
        image:
          'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R4331200003to07133D1.png',
        content: 'Drive Car Rental柜台位于二楼的国际到达大厅，7-8号出口之间。',
      },
      {
        image:
          'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R43z1200003to073876B.jpg',
        content:
          '国际或国内到达的乘客：从行李领取区出来后需步行到国际到达大厅，然后直接前往7号出口，我们的柜台位于7-8号出口之间，写有蓝色的“Drive Car Rental”标志。',
      },
      {
        image:
          'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R4601200003to0dz75E5.jpg',
        content:
          '办理完手续后，下到一楼从7号出口出去，穿过马路到外侧道路，跟随写有“Car Rental”的指示牌前往停车场，在“Drive Car Rental"的指示标志处找工作人员取车。',
      },
    ],
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    localDateTimeDTO: {
      date: '2025年7月24日',
      time: '18:00',
    },
    businessTimeDesc:
      '{"正常营业时间：":"00:00 - 17:59","收费营业时间：":"18:00 - 23:59"}',
    longitude: 100.750112,
    storeWay: '步行可达',
    businessTimePolicy: {
      content: [
        '18:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次¥444，费用及税费以实际支付为准。',
      ],
      summaryContent: ['18:00取车，门店预估收取服务费¥444。'],
      table: [
        {
          tableTitle: '时间段|服务费/次',
          code: 'BusinessTimePolicy',
          title: '服务费标准',
          notices: [
            '注意：请按时到达，如需提前或延后取还车务必提前联系门店。服务费和税费以实际支付为准。',
          ],
          showFree: false,
          type: 400,
          subTitle: '取车',
          items: [
            {
              showFree: false,
              positiveStatus: true,
              title: '18:00-23:59',
              description: '¥444',
            },
          ],
        },
      ],
    },
    serviceType: '0',
    waitTimeDesc: '',
    userSearchLocation: '素万那普国际机场',
    storeID: 176772,
  },
  baseResponse: {
    code: '200',
    requestId: '65e82bd4-9c4f-40ba-8aa0-fd17c5f6c195',
    cost: 570,
    isSuccess: true,
    returnMsg: 'success',
  },
  flight: {
    flightDelayRule: {
      title: '航班延误保留政策',
      rules: [
        {
          title:
            '如您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性、且供应商政策不同，可能无法为您保留车辆或无法免费取消订单，具体条款请以供应商政策为准，敬请谅解。',
        },
        {
          title: '建议您填写航班号，如遇延误请携带机票前往取车',
        },
      ],
      description: '航班延误保留政策',
    },
    flightNumber: '',
  },
  osdRefund: {
    refundProgressList: [],
  },
  checkResponseTime: 1750161517347.079,
  checkRequestTime: 1750161516645.5278,
  continuePayInfo: {
    needContinuePay: false,
  },
  platformInsurance: {
    insPackageId: 1,
    extraDesc: [
      '',
      '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比<url>https://www.baidu.com</url>实际赔付范围与标准以门店合同为准-卡拉比',
    ],
    packageInfos: [
      {
        insPackageId: 1,
        naked: false,
        defaultBomCode: '',
        currencyCode: '',
        defaultPackageId: 390412,
        gapPrice: 0,
        isDefault: true,
        stepPrice: 0,
        lowestDailyPrice: 0,
        packageName: '基础套餐',
        guaranteeDegree: 0,
      },
    ],
    insuranceItems: [
      {
        productId: 390412,
        description: '保障车辆碰撞损失',
        converageExplain: {
          title: '承保范围',
          content: [
            '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后',
          ],
        },
        excessTable: {
          title: '起赔额详见门店合同',
          subObject: [
            {
              title: '车行承担',
              content: ['起赔额以上部分（起赔额详见门店合同）'],
            },
            {
              title: '客户或承租方承担',
              content: ['起赔额及以下部分（据实承担）'],
            },
          ],
        },
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '发生事故后报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        type: 7,
        title: '车辆碰撞保障',
        code: 'CDW',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额详见门店合同',
            excessShortDesc: '起赔额详见门店合同',
            currencyCode: '',
            coverageWithPlatformInsurance: '起赔额详见门店合同',
            packageId: 390412,
          },
        ],
        unConverageExplain: {
          title: '不承保范围',
          content: [
            '不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准',
          ],
        },
        isInclude: true,
        name: '车辆碰撞保障',
        isFromCtrip: false,
      },
    ],
    briefInsuranceItems: [
      {
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额详见门店合同',
            excessShortDesc: '起赔额详见门店合同',
            currencyCode: '',
            coverageWithPlatformInsurance: '起赔额详见门店合同',
            packageId: 390412,
          },
        ],
        code: 'CDW',
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '发生事故后报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        isInclude: true,
        description: '保障车辆碰撞损失',
        type: 7,
        name: '车辆碰撞保障',
        isFromCtrip: false,
      },
    ],
    claimsProcess: [],
    reminder: [
      {
        subText:
          '如签署合同时，发现额外收费或强制购买，请不要签字，当场联系携程客服协助解决。',
        text: '温馨提示：',
      },
    ],
    ctripInsDeclaration: [
      {
        purchasedDesc:
          '该客人的保险套餐已满足当地法律的最低要求，请避免强迫客人添加任何保险，否则可能导致我处对相关当局的投诉。',
        requirement:
          "如果认为某项保险是必须的，请写明'xx保险是必须的'并附上签名和日期。",
        partner: '亲爱的供应商伙伴：',
        statement: '携程声明',
      },
      {
        purchasedDesc:
          'แพ็คเกจประกันภัยของลูกค้าครอบคลุมตามข้อกำหนดขั้นต่ำของกฎหมายท้องถิ่นแล้ว โปรดหลีกเลี่ยงการกดดันให้ลูกค้าซื้อประกันภัยเพิ่มเติม การกระทำดังกล่าวอาจทำให้เกิดข้อร้องเรียนได้',
        requirement:
          'หากคุณเชื่อว่าต้องมีประกันภัยบางประเภท โปรดระบุเป็นลายลักษณ์อักษรว่า "ต้องมีประกันภัย xx" พร้อมลงลายมือชื่อของคุณและวันที่',
        partner: 'เรียน ซัพพลายเออร์พาร์ทเนอร์:',
        statement: 'ใบแจ้งยอดของ Trip.com',
      },
      {
        purchasedDesc:
          "The customer's insurance package already meets minimum local law requirements. Please avoid pressuring the customer to add any insurance. Doing so may result in complaints being raised.",
        requirement:
          'If you believe a certain type of insurance is required, please indicate in writing that "xx insurance is required" and include your signature and date.',
        partner: 'Dear supplier partner:',
        statement: 'Trip.com Statement',
      },
    ],
  },
  freeDeposit: {
    depositExplainV2: [
      {
        explain:
          '到店支付押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541）',
        badDebt: false,
      },
    ],
    depositStatus: 0,
    depositItems: [
      {
        depositTitle: '押金',
        depositStatus: 2,
        explain: '取车时刷取信用卡预授权，还车后30-60天内退还',
        depositDesc: '押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541）',
      },
      {
        depositTitle: '支持卡种',
        depositDesc:
          '带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店不支持卡面带银联标志',
        creditUrlList: [
          'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
          'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
        ],
      },
    ],
    depositItemName: '押金详情',
  },
  rentalEssential: {
    mustRead: [
      {
        title: '用车须知',
        subObject: [
          {
            title: '自驾政策',
            type: 0,
            url: 'https://m.ctrip.com/tangram/ODA0ODA=?ctm_ref=vactang_page_80480&isHideNavBar=YES&apppgid=10650039393&statusBarStyle=1&channelid=237777',
            note: '请提前了解当地交规、加油、停车等政策',
          },
          {
            title: '租车指南',
            type: 1,
            url: 'https://trip.fat1.qa.nt.ctripcorp.com/carhire/materialsMustRead?orderId=1128168993042720&uid=M2258803416&locale=zh-CN&tab=2&isHideNavBar=YES',
            note: '请提前了解验车、核对合同、事故处理等步骤',
          },
        ],
      },
    ],
    pickUpMaterial: [
      {
        title: '中国大陆护照',
      },
      {
        title: '中国驾照原件 + 驾照国际翻译认证件',
      },
      {
        title: '中国驾照原件 + 当地语言公证件',
      },
      {
        title: '国际信用卡(带芯片、卡号凸字、不带银联标记)',
      },
    ],
  },
  receipt: {
    desc: '境外租车无法提供国内发票。itinerary作为一种常用消费凭证，一般可以用于报销',
    url: 'https://gateway.m.fat10668.qa.nt.ctripcorp.com/webapp/carhire/xsd/osdinvoice?id=%s&hideHeader=true&subEnv=fat14855',
  },
  cancelRuleInfo: {
    longTitle: 'In the following circumstances, you will not receive a refund:',
    ruleList: [
      '- You pick up the car late or drop it off early',
      '- You do not pick up the car within the given timeframe',
      '- You do not provide the required documents when picking up the car',
      "- The main driver's credit card does not have sufficient funds",
    ],
    cancelTip: {
      cancelTip: 'This booking can be canceled',
      cancelTipColor: 1,
    },
    customerCurrency: 'CNY',
    topTips: [
      {
        title: '可免费取消',
        type: 1,
        description: '当地时间7月24日17:00前',
      },
      {
        title: '取消需收费',
        type: 0,
        description: '当地时间7月24日17:00后',
      },
    ],
    osdCancelRuleInfo: {
      code: 'FreeCancel',
      title: '取消政策',
      complexSubTitle: {
        contentStyle: '1',
        stringObjs: [
          {
            content: '当地时间7月24日17:00前可免费取消',
            style: '5',
          },
          {
            content: '，',
            style: '6',
          },
          {
            content: '当地时间7月24日17:00前可免费取消',
            style: '6',
          },
        ],
      },
      showFree: true,
      type: 300,
      description: '注意：均为当地时间',
      subTitle: '当地时间7月24日17:00前可免费取消',
      items: [
        {
          showFree: true,
          lossFee: 0,
          title: '2025-07-24 17:00前',
          subTitle: '取车前1小时前',
          description: '可免费取消',
          key: '837789',
        },
        {
          showFree: false,
          lossFee: 21,
          title: '2025-07-24 17:00后',
          subTitle: '取车前1小时后',
          description: '取消将收取租金10%作为违约金',
          key: '837796',
        },
      ],
    },
  },
  authType: 0,
  orderPrice: {
    localCurrency: 'THB',
    localPayAmountOnArrival: 0,
    feeDetail: {
      chargesInfos: [
        {
          title: '车辆租金+基础套餐',
        },
        {
          code: 'Car',
          title: '基础租车费用',
          size: '约¥205×1天',
          currencyCode: 'CNY',
          currentTotalPrice: 205,
          description: '',
          type: 8,
          payMode: 2,
          items: [
            {
              code: 'Car',
              title: '基础租车费用',
              descList: [
                '满电取还, 满电取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 基本租车费用, 不限里程',
              ],
              description:
                '满电取还, 满电取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 基本租车费用, 不限里程',
              type: 8,
            },
            {
              title: '车辆碰撞保障',
              type: 1,
              code: 'Insurance',
            },
          ],
        },
      ],
      notIncludeCharges: {},
      rentalTerm: 1,
      chargesSummary: {
        code: 'Summary',
        title: '全额',
        currencyCode: 'CNY',
        notices: [],
        currentTotalPrice: 205,
        type: 103,
        items: [
          {
            code: '1',
            currentTotalPrice: 205,
            title: '在线预付',
            currencyCode: 'CNY',
          },
        ],
      },
      equipmentInfos: [],
      couponInfos: [],
    },
    customerPayAmount: 205,
    totalAmount: 205,
    customerPayAmountOnArrival: 0,
    priceDesc: {
      possibleChangeList: [
        {
          title: '里程政策',
          description: '该车辆没有里程限制。',
        },
        {
          title: '能源政策',
          description: '满油取还',
        },
        {
          title: '年龄要求',
          description: '驾驶员年龄要求：25-65周岁',
        },
        {
          title: '额外驾驶员',
          description:
            '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
        },
        {
          title: '营业时间外取还车',
          description:
            '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
        },
        {
          title: '当地费用',
          description:
            '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
        },
        {
          title:
            '到店支付押金至少为租车费用+US$ 500-US$ 3,000（约¥3,591-¥21,541）',
        },
      ],
    },
    customerCurrency: 'CNY',
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 1084,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 1084,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1750161516645,
    afterFetch: 1750161517729,
  },
};

// 新契约  取消中订单 1128168993042720 18862/queryOrderSecondOpen 18862/queryPreloadingOrderDetail
export const qxzddsecondRes = {
  vehicleInfo: {
    passengerNum: 4,
    transmission: 'AT',
    imageUrl: '',
    doorNum: 4,
    vehicleGroupName: '小型轿车',
    vehicleName: '日产 ',
    vendorVehicleCode: 'ECAV_NissanAlmera_d139fe91',
  },
  pickupStore: {
    businessTimePolicy: {
      content: [
        '18:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次¥444，费用及税费以实际支付为准。',
      ],
      summaryContent: ['18:00取车，门店预估收取服务费¥444。'],
      table: [
        {
          tableTitle: '时间段|服务费/次',
          code: 'BusinessTimePolicy',
          title: '服务费标准',
          notices: [
            '注意：请按时到达，如需提前或延后取还车务必提前联系门店。服务费和税费以实际支付为准。',
          ],
          showFree: false,
          type: 400,
          subTitle: '取车',
          items: [
            {
              showFree: false,
              positiveStatus: true,
              title: '18:00-23:59',
              description: '¥444',
            },
          ],
        },
      ],
    },
    localDateTimeDTO: {
      date: '2025年7月24日',
      time: '18:00',
    },
    longitude: 100.750112,
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    localDateTime: '2025-07-24 18:00:00',
    latitude: 13.689999,
    storeID: 176772,
    serviceType: '0',
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    storeCode: '2',
    storeName: 'Suvarnabhumi Airport',
    businessTimeDesc:
      '{"正常营业时间：":"00:00 - 17:59","收费营业时间：":"18:00 - 23:59"}',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  flight: {
    flightDelayRule: {
      title: '航班延误保留政策',
      rules: [
        {
          title:
            '如您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性、且供应商政策不同，可能无法为您保留车辆或无法免费取消订单，具体条款请以供应商政策为准，敬请谅解。',
        },
        {
          title: '建议您填写航班号，如遇延误请携带机票前往取车',
        },
      ],
      description: '航班延误保留政策',
    },
    flightNumber: '',
  },
  checkResponseTime: 1750211009580.583,
  checkRequestTime: 1750211009449.079,
  baseResponse: {
    code: '0',
    requestId: '9193b913-9e45-4c77-ad82-24e15419dc3b',
    cost: 95,
    isSuccess: true,
    returnMsg: 'success',
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1750211009734+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '4001225821720868512',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a789841-486169-37533',
      },
    ],
  },
  timeInterval: 131.50390625,
  resBodySize: 3110,
  returnStore: {
    businessTimePolicy: {
      content: [''],
    },
    localDateTimeDTO: {
      date: '2025年7月25日',
      time: '18:00',
    },
    longitude: 100.750112,
    storeAddress:
      'International Arrival Hall, Between gate 7 & 8，999 Moo 1, Nong Prue, Bang Phli District, Samut Prakan 10540,Bangkok',
    localDateTime: '2025-07-25 18:00:00',
    latitude: 13.689999,
    storeID: 176772,
    serviceType: '0',
    storeTel:
      '+886-***********/+86-***********/+86-***********/+66-************,45/+86-************/+86-12345321',
    storeCode: '2',
    storeName: 'Suvarnabhumi Airport',
    businessTimeDesc: '{"正常营业时间：":"24小时营业"}',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  orderBaseInfo: {
    orderId: 1128168993042720,
    orderStatus: {
      orderStatusCtrip: 'CAR_CANCELLING',
      orderStatusDesc: '取消中',
    },
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 281,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 281,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1750211009448,
    afterFetch: 1750211009729,
  },
};

// 新契约  取消中订单 1128168993042720 18862/OSDQueryOrder  18862/queryOsdOrderDetail
export const qxzddRes = {
  timeInterval: 2247.376953125,
  returnStore: {
    storeLocation: '自行到店, 门店距曼谷华南蓬火车站驾车11公里',
    businessTimePolicy: {
      content: [
        '22:01 - 23:00, 22:01 - 23:00取还车，请提前联系门店，门店预估收取服务费每次¥10，费用及税费以实际支付为准。',
      ],
      table: [
        {
          tableTitle: '时间段|服务费/次',
          code: 'BusinessTimePolicy',
          title: '服务费标准',
          notices: ['注意：请按时到达，如需提前或延后取还车务必提前联系门店。'],
          showFree: false,
          type: 400,
          subTitle: '还车',
          items: [
            {
              title: '22:01-23:00',
              showFree: false,
              description: '¥10',
            },
            {
              title: '22:01-23:00',
              showFree: false,
              description: '¥10',
            },
          ],
        },
      ],
    },
    storeBusinessTime: [
      {
        timeStr: '05:00 - 22:00, 05:00 - 22:00',
        free: true,
      },
    ],
    localDateTimeDTO: {
      date: '2025年6月19日',
      time: '18:00',
    },
    longitude: 100.574271,
    storeAddress:
      '19/23 Building A, Royal City Avenue, New Petchburi Rd., Bangkok',
    localDateTime: '2025-06-19 18:00:00',
    latitude: 13.753655,
    countryId: 4,
    storeID: 228771,
    location: {
      city: {
        id: 359,
        name: '曼谷',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      locationType: 2,
      locationName: '曼谷华南蓬火车站',
      continent: {
        id: 1,
        name: '亚洲',
      },
      poiInfo: {
        type: 2,
        longitude: 100.516795,
        latitude: 13.739336,
      },
      locationCode: '85994',
      province: {
        id: 0,
      },
    },
    userSearchLocation: '曼谷华南蓬火车站',
    storeTel: '+86-************/+************',
    storeCode: 'BK4',
    storeName: 'Bangkok Downtown Rama 9\n',
    outOfHourDescription:
      '22:01 - 23:00, 22:01 - 23:00取还车，请提前联系门店，门店预估收取服务费每次¥10，费用及税费以实际支付为准。',
    serviceType: '0',
    businessTimeDesc: '{"正常营业时间：":"05:00 - 22:00, 05:00 - 22:00"}',
    storeOpenTimeDesc: '{"":"05:00 - 22:00,05:00 - 22:00"}',
  },
  vendorInfo: {
    vendorImageUrl:
      'https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png',
    platformName: '',
    platformCode: '',
    vendorName: 'Budget Thai',
  },
  addPayments: [
    {
      amount: 500,
      orderId: 1128168993128230,
      reason: '费用扣款',
      payTime: 1750325303136,
      payStatus: 1,
      additionalPaymentId: 96573,
    },
  ],
  coverage: [
    {
      code: 'ULM',
      title: '不限里程',
      type: 2,
      description: '租期内没有公里数限制。\n',
    },
    {
      title: '额外驾驶员',
      type: 3,
      code: 'ADD1',
    },
    {
      title: '满电取还',
      type: 4,
      code: 'FRFB',
    },
    {
      title: '满电取还',
      type: 4,
      code: 'FRFB',
    },
  ],
  orderBaseInfo: {
    orderStatus: {
      orderStatus: 3,
      orderStatusName: '已完成',
      orderStatusDesc: '已完成',
      orderStatusCtrip: 'CAR_COMPLETED',
    },
    orderId: 1128168993128230,
    uid: 'M269387928',
    orderTip: {
      tipContentArray: ['行程已结束，感谢您的使用'],
    },
    attr: {
      osdConfirmNew: 'true',
      voucherVersionV2: 'B',
      fulfillmentVersion: 'D',
      voucherVersion: 'B',
      isVehicle2: '1',
      osdModifyOrderVersion: 'B',
      ctripInsuranceVersion: 'B',
      klbVersion: '1',
      mergeVoucherFlag: '1',
      osdDetailVersion: 'B',
    },
    osdModifyNewOrder: false,
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1750398658900+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '907333067387803653',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a76ec27-486221-91385',
      },
    ],
  },
  carAssistantSummary: [
    {
      title: '取还车拍照留证',
      type: 7,
    },
    {
      title: '查看具体要求',
      type: 1,
      url: 'https://www.fat10668.qa.nt.ctripcorp.com/carhire/materialsMustRead?orderId=1128168993128230&h5View=1&locale=zh-CN&orderStatus=CAR_COMPLETED&vendorId=14088277&countryId=4&isHideNavBar=YES&subEnv=fat14605&transparentbar=1&newOsdVoucherV2=1&orderLocale=zh-CN,th-TH,en-US',
    },
    {
      title: '免费办理翻译件',
      type: 8,
      url: 'https://m.ctrip.com/webapp/carhire/xsd/xsdnewinterlicensePage',
    },
  ],
  operation: {
    extOperation: [],
    orderOperation: [
      {
        operationId: 3,
        enable: true,
        buttonName: '我要点评',
        url: 'https://m.ctrip.fat67.qa.nt.ctripcorp.com/webapp/vacations/order/public/order_comment?channel=car-rental&orderid=1128168993128230&sourceFrom=OSD_C_APP',
      },
      {
        operationId: 4,
        buttonName: '再次预订',
        enable: true,
      },
      {
        operationId: 6,
        enable: true,
        buttonName: '报销凭证',
        url: 'https://gateway.m.fat10668.qa.nt.ctripcorp.com/webapp/carhire/xsd/osdinvoice?id=%s&hideHeader=true&subEnv=fat14766',
        contents: [
          {
            description:
              '在线支付的费用可以在订单完成后，进入订详页面自助下载相关报销凭证；到店支付的费用，请在门店向供应商索要相关Receipt凭证。',
          },
        ],
      },
    ],
  },
  resBodySize: 53369,
  driverInfo: {
    areaCode: '86',
    lastName: 'anna',
    firstName: 'lee',
    contactWayList: [],
    age: '32',
    optionalContactWayList: [
      {
        contactWayName: '微信',
        isCanModify: true,
        contactWayType: '1',
      },
      {
        contactWayName: 'WhatsApp',
        isCanModify: true,
        contactWayType: '4',
      },
      {
        contactWayName: 'LINE',
        isCanModify: true,
        contactWayType: '2',
      },
      {
        contactWayName: 'KakaoTalk',
        isCanModify: true,
        contactWayType: '3',
      },
      {
        contactWayName: '当地电话',
        isCanModify: true,
        contactWayType: '0',
      },
    ],
    email: 'sh***<EMAIL>',
    flightNo: '',
    isChangeContact: false,
    name: 'anna/lee',
    telphone: '189****9000',
  },
  osdPolicy: {
    carRentalMustRead: [
      {
        code: '4',
        sortNum: 1,
        title: '额外驾驶员',
        type: 6,
      },
      {
        code: '4',
        sortNum: 2,
        title: '营业时间外取还车',
        type: 7,
      },
      {
        code: '4',
        sortNum: 3,
        title: '提前/延后取还车',
        type: 41,
      },
      {
        code: '4',
        sortNum: 4,
        title: '费用须知',
        type: 40,
      },
      {
        code: '1',
        sortNum: 1,
        title: '确认政策',
        content: [
          '根据该车行近期订单表现，预订此产品后平均需要1分钟确认订单是否预订成功。\n',
        ],
        type: 0,
      },
      {
        code: '1',
        sortNum: 2,
        title: '取消政策',
        content: ['订单确认后取消将收取全部租金作为违约金，最多收取¥10,000'],
        type: 1,
        table: [
          {
            code: 'FreeCancel',
            title: '取消政策',
            complexSubTitle: {
              contentStyle: '1',
              stringObjs: [
                {
                  content: '该订单确认后有损取消',
                  style: '5',
                },
                {
                  content: '，',
                  style: '6',
                },
                {
                  content: '该订单确认后有损取消',
                  style: '6',
                },
              ],
            },
            showFree: false,
            type: 300,
            description: '注意：均为当地时间，订单确认前可免费取消',
            subTitle: '该订单确认后有损取消',
            items: [
              {
                showFree: false,
                title: '',
                subTitle: '订单确认后',
                description: '取消将收取全部租金作为违约金，最多收取¥10,000',
                key: '843431',
              },
            ],
          },
        ],
      },
      {
        code: '1',
        sortNum: 3,
        title: '押金说明',
        content: ['该门店不收取押金'],
        type: 2,
        table: [
          {
            title: '押金',
            showFree: false,
            description: '该门店不收取押金',
          },
        ],
      },
      {
        code: '1',
        sortNum: 4,
        title: '里程政策',
        content: ['该车辆没有里程限制。'],
        type: 3,
      },
      {
        code: '1',
        sortNum: 5,
        title: '能源政策',
        content: [
          '满油取还',
          '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
        ],
        type: 4,
      },
      {
        code: '1',
        sortNum: 6,
        title: '年龄要求',
        content: [
          '驾驶员年龄要求：25-65周岁',
          '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
        ],
        type: 5,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '里程政策',
            content: ['该车辆没有里程限制。'],
            type: 3,
          },
          {
            code: '2',
            sortNum: 2,
            title: '能源政策',
            content: [
              '满油取还',
              '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
            ],
            type: 4,
          },
          {
            code: '2',
            sortNum: 3,
            title: '年龄要求',
            content: [
              '驾驶员年龄要求：25-65周岁',
              '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
              '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
              '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
            ],
            type: 5,
          },
          {
            code: '2',
            sortNum: 4,
            title: '额外驾驶员',
            content: [
              '1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
            ],
            type: 6,
          },
          {
            code: '2',
            sortNum: 5,
            title: '营业时间外取还车',
            content: [
              '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
            ],
            type: 7,
          },
          {
            code: '2',
            sortNum: 6,
            title: '当地费用',
            content: [
              '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
            ],
            type: 16,
          },
        ],
        title: '费用须知',
        content: [],
        type: 40,
        sortNum: 1,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '提前取车',
            content: [
              '若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。',
            ],
            type: 17,
          },
          {
            code: '2',
            sortNum: 2,
            title: '延迟取车',
            content: [
              '若您延迟到门店取车，请在预订取车时间前提前联系门店（或平台）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。',
            ],
            type: 18,
          },
          {
            code: '2',
            sortNum: 3,
            title: '提前还车',
            content: [
              '提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。',
            ],
            type: 10,
          },
          {
            code: '2',
            sortNum: 4,
            title: '延迟还车',
            content: [
              '若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。',
            ],
            type: 11,
          },
        ],
        title: '提前/延后取还车',
        content: [],
        type: 41,
        sortNum: 2,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '签合同前核对金额',
            content: [
              '办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。',
            ],
            type: 59,
          },
          {
            code: '2',
            sortNum: 2,
            title: '验车留存有效凭证',
            content: [
              '取车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身、油量里程、车损特写等）。 \n因各地车损处理方式不同，部分国家并非及时确认车损 ，需要您保留有效还车车况凭证（注意开启相机拍照的时间显示功能，需证明照片拍摄时间），否则后续产生车损争议平台可能因当地行业操作，以供应商凭证为准。',
            ],
            type: 60,
          },
        ],
        title: '取还车注意事项',
        content: [],
        type: 58,
        sortNum: 3,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 2,
            title: '管理费',
            content: [
              '如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。',
              '此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。',
            ],
            type: 22,
          },
        ],
        title: '租车保障',
        content: [],
        type: 20,
        sortNum: 4,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '额外设备',
            content: [
              '额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。',
            ],
            type: 24,
          },
          {
            code: '2',
            sortNum: 2,
            title: '税费',
            content: ['所有额外服务将会收取销售税费和当地费用。'],
            type: 25,
          },
        ],
        title: '附加服务',
        content: [],
        type: 23,
        sortNum: 5,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '修改',
            content: [
              '如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。',
            ],
            type: 31,
          },
          {
            code: '2',
            sortNum: 2,
            title: 'No show（未取车）',
            content: [
              'noshow 是指当你：',
              '计划提前取消订单但未告知我们，或者',
              '未在规定的时间内取车，或者',
              '无法提供取车时要出示的文件，或者',
              '无法提供主驾驶员名下有足够额度的信用卡',
              '以上情形，你之前预定的订单金额不会退还给你。',
            ],
            type: 32,
          },
        ],
        title: '取消，未取车和修改',
        content: [],
        type: 42,
        sortNum: 7,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '意外或故障',
            content: [
              '事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。',
            ],
            type: 34,
          },
          {
            code: '2',
            sortNum: 2,
            title: '道路救援',
            content: [
              '道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。',
            ],
            type: 35,
          },
          {
            code: '2',
            sortNum: 3,
            title: '遗失钥匙',
            content: ['遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用'],
            type: 36,
          },
          {
            code: '2',
            sortNum: 4,
            title: '安全带',
            content: [
              '安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。',
            ],
            type: 37,
          },
          {
            code: '2',
            sortNum: 5,
            title: '禁止吸烟',
            content: [
              '禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。',
            ],
            type: 38,
          },
          {
            code: '2',
            sortNum: 6,
            title: '价格计算',
            content: [
              '价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。',
              '取车的时候，客人可能会选择不同的车，而不是预订时候的车。',
              '如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。',
              '如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。',
            ],
            type: 39,
          },
          {
            code: '2',
            sortNum: 7,
            title: '违章条款',
            content: [
              '请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中可能产生的违章罚款费用以及供应商代为交涉产生的行政费用（也称“违章管理费”）。\n由于车辆登记在租车公司名下，因此产生违章时，交管局会直接通知租车公司，车行将会把承租人信息提供给当局。所有租车合同中均包含信用卡自动扣费授权条款，用于处理违章和路费欠缴事宜。提车时主驾驶人需签字确认该条款，因此在发生违章时，租车公司通常不会提前通知您，直接从信用卡中扣除当局确认所产生的行政费用（通常按违章次数进行收取）。\n由于隐私法律的限制，大部分车行无法代缴罚金，是由当地相关部门以邮件或纸质平邮的形式，通过您在租车合同上预留的邮箱或者地址通知您。',
            ],
            type: 13,
          },
        ],
        title: '租车公司重要信息',
        content: [],
        type: 33,
        sortNum: 8,
      },
    ],
    crossLocationsPolicy: {
      notes: [
        '若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。除特别说明的情况外，“跨境费用”为前往单个国家所需的费用。',
      ],
      crossLocationsInfos: [
        {
          subTitle: '门店支持在以下区域跨境使用车辆：',
          crossTypeName: '跨境政策',
          title: '选择计划前往的国家',
          summaryPolicies: ['若您的行程中涉及跨境，请提前选择'],
          crossType: 3,
          locations: [
            {
              status: 3,
              isSelected: false,
              regionId: '7',
              name: '阿富汗',
              statusName: '不允许跨境',
              firstChar: 'A',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '9',
              name: '阿联酋',
              statusName: '不允许跨境',
              firstChar: 'A',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '145',
              name: '阿塞拜疆',
              statusName: '不允许跨境',
              firstChar: 'A',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '150',
              name: '阿曼',
              statusName: '不允许跨境',
              firstChar: 'A',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '205',
              name: '巴勒斯坦',
              statusName: '不允许跨境',
              firstChar: 'B',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '17',
              name: '巴基斯坦',
              statusName: '不允许跨境',
              firstChar: 'B',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '212',
              name: '不丹',
              statusName: '不允许跨境',
              firstChar: 'B',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '118',
              name: '巴林',
              statusName: '不允许跨境',
              firstChar: 'B',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '26',
              name: '朝鲜',
              statusName: '不允许跨境',
              firstChar: 'C',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '215',
              name: '东帝汶',
              statusName: '不允许跨境',
              firstChar: 'D',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '32',
              name: '菲律宾',
              statusName: '不允许跨境',
              firstChar: 'F',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '42',
              name: '韩国',
              statusName: '不允许跨境',
              firstChar: 'H',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '176',
              name: '哈萨克斯坦',
              statusName: '不允许跨境',
              firstChar: 'H',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '143',
              name: '吉尔吉斯斯坦',
              statusName: '不允许跨境',
              firstChar: 'J',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '50',
              name: '柬埔寨',
              statusName: '不允许跨境',
              firstChar: 'J',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '163',
              name: '卡塔尔',
              statusName: '不允许跨境',
              firstChar: 'K',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '53',
              name: '科威特',
              statusName: '不允许跨境',
              firstChar: 'K',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '55',
              name: '老挝',
              statusName: '不允许跨境',
              firstChar: 'L',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '56',
              name: '黎巴嫩',
              statusName: '不允许跨境',
              firstChar: 'L',
            },
            {
              status: 2,
              policy: '跨境政策中文版',
              regionId: '2',
              isSelected: false,
              statusName: '条件跨境',
              name: '马来西亚',
              firstChar: 'M',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '67',
              name: '蒙古',
              statusName: '不允许跨境',
              firstChar: 'M',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '68',
              name: '孟加拉国',
              statusName: '不允许跨境',
              firstChar: 'M',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '70',
              name: '缅甸',
              statusName: '不允许跨境',
              firstChar: 'M',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '146',
              name: '马尔代夫',
              statusName: '不允许跨境',
              firstChar: 'M',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '74',
              name: '尼泊尔',
              statusName: '不允许跨境',
              firstChar: 'N',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '78',
              name: '日本',
              statusName: '不允许跨境',
              firstChar: 'R',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '82',
              name: '沙特阿拉伯',
              statusName: '不允许跨境',
              firstChar: 'S',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '83',
              name: '斯里兰卡',
              statusName: '不允许跨境',
              firstChar: 'S',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '195',
              name: '土库曼斯坦',
              statusName: '不允许跨境',
              firstChar: 'T',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '89',
              name: '土耳其',
              statusName: '不允许跨境',
              firstChar: 'T',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '250',
              name: '塔吉克斯坦',
              statusName: '不允许跨境',
              firstChar: 'T',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '260',
              name: '文莱达鲁萨兰国',
              statusName: '不允许跨境',
              firstChar: 'W',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '144',
              name: '乌兹别克斯坦',
              statusName: '不允许跨境',
              firstChar: 'W',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '3',
              name: '新加坡',
              statusName: '不允许跨境',
              firstChar: 'X',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '100',
              name: '叙利亚',
              statusName: '不允许跨境',
              firstChar: 'X',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '102',
              name: '也门',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '103',
              name: '伊拉克',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '104',
              name: '伊朗',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '105',
              name: '以色列',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '107',
              name: '印度',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '108',
              name: '印度尼西亚',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '110',
              name: '约旦',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '175',
              name: '亚美尼亚',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '111',
              name: '越南',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '1',
              name: '中国',
              statusName: '不允许跨境',
              firstChar: 'Z',
            },
          ],
        },
      ],
      title: '旅行限制',
    },
  },
  vehicleInfo: {
    fuelNoteTitle: '汽油或柴油',
    seriesWarning: {
      introduce: {
        title: '同组车型',
        description:
          '同组车型是指按照车型功能、空间大小、座位数和车门数划分的紧凑型轿车、中型轿车、小型SUV等等这些分类。您在网上预订时选择的车并不能保证到店就一定取到一模一样的，可能会得到同组车型中的其他车。',
      },
      carProtection: {
        title: '取车保障',
        description:
          '在库存充足的情况下，车行会尽量提供您在预订时所选的车型\n如果这款车暂时没有库存，车行便会为您提供同车型组内的其他车型\n若因门店原因需要调整车型，不额外收取车型变更费用',
      },
      video: 'https://ak-v.tripcdn.com/videos/9D0g2r000001j0rrk1E0A.mp4',
      cover: 'https://ak-d.tripcdn.com/images/1tg1u12000dwdf1uw1DCD.png',
      cases: [
        {
          vehicleGroupName: '车型组',
          representativeVehicleName: '代表车型',
          vehicleGroupItems: '车组包含',
          vehicleGroupCode: 'default',
        },
        {
          vehicleGroupName: '紧凑型轿车',
          representativeVehicleName: '福特嘉年华',
          vehicleGroupItems: '丰田卡罗拉、现代雅绅特',
          vehicleGroupCode: 'D',
        },
        {
          vehicleGroupName: '中大型轿车',
          representativeVehicleName: '丰田凯美瑞',
          vehicleGroupItems: '雪佛兰迈锐宝、大众帕萨特',
          vehicleGroupCode: 'S',
        },
        {
          vehicleGroupName: '中大型SUV',
          representativeVehicleName: '大众途观',
          vehicleGroupItems: '丰田RAV4、吉普指南者',
          vehicleGroupCode: 'R',
        },
      ],
    },
    fuelType: 2416,
    vendorVehicleCode: '',
    vehicleId: 531861,
    vehicleDisplacement: '',
    vehicleGroupName: '中型轿车',
    transmission: 'AT',
    esgInfo: {},
    passengerNum: 5,
    imageUrl: '',
    sippCode: 'MCAR',
    vehicleImgUrl: '',
    hasAC: false,
    fourDrive: false,
    luggageNum: 0,
    doorNum: 4,
    vehicleGroupId: 13,
    special: false,
    transmissionType: 1,
    fuelNote:
      '门店会根据库存情况为您提供汽油车或者柴油车，具体燃油类型与加油型号以门店告知为准。',
    vehicleName: '丰田 ',
  },
  pickupStore: {
    location: {
      city: {
        id: 359,
        name: '曼谷',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      locationType: 2,
      locationName: '曼谷华南蓬火车站',
      continent: {
        id: 1,
        name: '亚洲',
      },
      poiInfo: {
        type: 2,
        longitude: 100.516795,
        latitude: 13.739336,
      },
      locationCode: '85994',
      province: {
        id: 0,
      },
    },
    storeAddress:
      '19/23 Building A, Royal City Avenue, New Petchburi Rd., Bangkok',
    storeCode: 'BK4',
    storeLocation: '自行到店, 门店距曼谷华南蓬火车站驾车11公里',
    localDateTime: '2025-06-19 14:00:00',
    storeName: 'Bangkok Downtown Rama 9\n',
    storeBusinessTime: [
      {
        timeStr: '05:00 - 22:00, 05:00 - 22:00',
        free: true,
      },
    ],
    latitude: 13.753655,
    storeOpenTimeDesc: '{"":"05:00 - 22:00,05:00 - 22:00"}',
    countryId: 4,
    guideStep: [
      {
        image: '',
        content:
          '如需取车，请前往位于拉玛九路（Rama 9 Road）的 Budget 办事处，从高速公路出口出来后直行，经过 Golden Tulip Sovereign Hotel。左转进入 Royal City Avenue。办事处位于右侧约 50 米处。在这里，我们友好的工作人员将在现场完成您的租赁协议和取车流程。                                                                                                                                                                                                                         \n',
      },
    ],
    storeTel: '+86-************/+************',
    localDateTimeDTO: {
      date: '2025年6月19日',
      time: '14:00',
    },
    businessTimeDesc: '{"正常营业时间：":"05:00 - 22:00, 05:00 - 22:00"}',
    longitude: 100.574271,
    businessTimePolicy: {
      content: [''],
    },
    waitTimeDesc: '',
    serviceType: '0',
    userSearchLocation: '曼谷华南蓬火车站',
    storeID: 228771,
  },
  baseResponse: {
    code: '200',
    requestId: '5776b6aa-6d66-4b6f-af89-a7185946ee10',
    cost: 2158,
    isSuccess: true,
    returnMsg: 'success',
  },
  flight: {
    flightNumber: '',
  },
  osdRefund: {
    refundProgressList: [],
  },
  checkResponseTime: 1750398658782.864,
  checkRequestTime: 1750398656535.487,
  continuePayInfo: {
    needContinuePay: false,
  },
  platformInsurance: {
    insPackageId: 5,
    extraDesc: [
      '',
      '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比<url>https://www.baidu.com</url>实际赔付范围与标准以门店合同为准-卡拉比',
      '',
      '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比<url>https://www.baidu.com</url>实际赔付范围与标准以门店合同为准-卡拉比',
    ],
    packageInfos: [
      {
        insPackageId: 5,
        naked: false,
        defaultBomCode: '',
        currencyCode: 'THB',
        defaultPackageId: 393422,
        gapPrice: 0,
        isDefault: true,
        stepPrice: 0,
        lowestDailyPrice: 0,
        packageName: '尊享套餐',
        guaranteeDegree: 0,
      },
    ],
    insuranceItems: [
      {
        productId: 393422,
        description: '保障车辆碰撞损失',
        converageExplain: {
          title: '承保范围',
          content: [
            '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后',
          ],
        },
        excessTable: {
          title: '起赔额 0起赔额',
          content: [
            '*套餐中包含国内保险公司提供的安心补充险，更多保障内容可点击安心补充险查看',
          ],
          subObject: [
            {
              title: '车行承担',
              content: ['THB 10000以上部分'],
            },
            {
              title: '国内保险公司承担',
              content: ['THB 10000及以下部分（据实承担）'],
            },
            {
              title: '客户或承租方承担',
              content: ['THB 0'],
            },
          ],
        },
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '发生事故后报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        type: 7,
        title: '车辆碰撞保障',
        code: 'CDW',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额THB 10000',
            coverageWithoutPlatformInsurance: '原起赔额THB 10000可赔',
            currencyCode: 'THB',
            coverageWithPlatformInsuranceV2:
              '*起赔额及以下部分由国内保险公司提供',
            packageId: 393422,
            maxExcess: 0,
            minExcess: 0,
          },
        ],
        unConverageExplain: {
          title: '不承保范围',
          content: [
            '不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准',
          ],
        },
        isAddFulCoverage: true,
        isInclude: true,
        name: '车辆碰撞保障',
        isFromCtrip: false,
      },
      {
        productId: 393422,
        description: '保障第三方车辆或人员伤害损失',
        converageExplain: {
          title: '承保范围',
          content: [
            '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
          ],
        },
        excessTable: {
          title: '起赔额 0起赔额',
          content: [
            '*套餐中包含国内保险公司提供的安心补充险，更多保障内容可点击安心补充险查看',
          ],
          subObject: [
            {
              title: '车行承担',
              content: ['THB 10000以上部分'],
            },
            {
              title: '国内保险公司承担',
              content: ['THB 10000及以下部分（据实承担）'],
            },
            {
              title: '客户或承租方承担',
              content: ['THB 0'],
            },
          ],
        },
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况',
              ],
            },
            {
              title: '还车时向车行提交理赔',
              content: [
                '在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        type: 7,
        title: '第三者责任保障',
        code: 'TPL',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额THB 10000',
            coverageWithoutPlatformInsurance: '原起赔额THB 10000可赔',
            currencyCode: 'THB',
            coverageWithPlatformInsuranceV2:
              '*起赔额及以下部分由国内保险公司提供',
            packageId: 393422,
            maxExcess: 0,
            minExcess: 0,
          },
        ],
        isAddFulCoverage: true,
        isInclude: true,
        name: '第三者责任保障',
        isFromCtrip: false,
      },
      {
        description: '驾乘意外险短描述',
        converageExplain: {
          title: '承保范围',
          content: ['驾乘意外险长描述'],
        },
        coverDetailList: [
          {
            title: '最高保额',
            subTitle: '保障范围',
          },
          {
            title: '$1000',
            descList: ['驾乘意外险保障范围描述1', '驾乘意外险保障范围描述2'],
          },
          {
            title: '$2000',
            descList: [
              '驾乘意外险保障范围描述11',
              '驾乘意外险保障范围描述22',
              '驾乘意外险保障范围描述33',
            ],
          },
        ],
        insuranceNotice:
          'https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111',
        insuranceStatusName: {
          title: '已出保',
          color: 'green',
        },
        insuranceStatus: 63003,
        insuranceClauses: 'https://pages.c-ctrip.com/tour/pdf1712/23.pdf',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '（国内保险公司提供）',
            coverageWithPlatformInsuranceV2: '¥75/天',
            coverageWithoutPlatformInsurance: '国内保险公司提供¥75/天',
          },
        ],
        title: '驾乘意外险',
        code: 'MP18021541PK00080231',
        unConverageExplain: {
          title: '不承保范围',
          content: ['驾乘意外险不保障内容'],
        },
        giveUp: false,
        itemUrl:
          'https://dmzstg1.pa18.com/pa18shopnst%2Fdo%2Fera%2FPasCommon%2FdownloadPolicyStream%3FpolicyNo%3DTxhYxTsmb%252BPVc4UuSBmCilewrChsJBza3QJCVrQ66yCk6jrDDs0nrImpojpS0zoSZx8gUmixjUb3N433J1Zvy2T40wXwytcEhdA5Lf4ksPLKr2OuDUEVVPjxbElIVdjZEmTMLGyboT20aadqZU7Skh%252FJCY97wvcNhuCqFNFaYAc%253D%2F%26partnerCode%3DP_CTRIP_GA',
        isInclude: true,
        name: '驾乘意外险',
        isFromCtrip: true,
      },
      {
        description:
          '赔偿车损/盗抢的自付部分，以及玻璃、轮胎、底盘破损，补偿道路救援费',
        converageExplain: {
          title: '承保范围',
          content: [
            '由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方：',
          ],
        },
        coverDetailList: [
          {
            title: '最高保额',
            subTitle: '保障范围',
          },
          {
            title: '50000',
            descList: [
              '车行车损和盗抢保障的自付部分',
              '玻璃、轮胎、底盘损失的自付部分',
              '道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分',
              '涉及的税费及管理费用（此项以1500元为限）',
              '车行车损和盗抢保障的自付部分',
              '玻璃、轮胎、底盘损失的自付部分',
              '道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分',
              '涉及的税费及管理费用（此项以1500元为限）',
            ],
          },
          {
            title: '50000',
            descList: [
              '旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金',
              '旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金',
            ],
          },
          {
            title: '3000',
            descList: [
              '机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失',
              '证件被盗抢或丟失导致无法取车的租车费用损失',
              '车辆停运费或被迫更换费用',
              '航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用',
              '机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失',
              '证件被盗抢或丟失导致无法取车的租车费用损失',
              '车辆停运费或被迫更换费用',
              '航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用',
            ],
          },
          {
            title: '1000',
            descList: [
              '违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%',
              '违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%',
            ],
          },
          {
            title: '4000',
            descList: [
              '因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用',
              '因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用',
            ],
          },
        ],
        insuranceNotice:
          '1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。',
        insuranceStatusName: {
          title: '已支付',
          color: 'green',
        },
        insuranceStatus: 63002,
        insuranceClauses: 'https://pages.c-ctrip.com/tour/pdf1907/32.pdf',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '（国内保险公司提供）',
            coverageWithPlatformInsuranceV2: '¥25/天',
            coverageWithoutPlatformInsurance: '国内保险公司提供¥25/天',
          },
        ],
        title: '安心补充险',
        code: 'MP18022460PK00131282',
        unConverageExplain: {
          title: '不承保范围',
          content: [
            '1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;',
          ],
        },
        giveUp: false,
        isInclude: true,
        name: '安心补充险',
        isFromCtrip: true,
      },
    ],
    briefInsuranceItems: [
      {
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额THB 10000',
            coverageWithoutPlatformInsurance: '原起赔额THB 10000可赔',
            currencyCode: 'THB',
            coverageWithPlatformInsuranceV2:
              '*起赔额及以下部分由国内保险公司提供',
            packageId: 393422,
            maxExcess: 0,
            minExcess: 0,
          },
        ],
        code: 'CDW',
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '发生事故后报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        isAddFulCoverage: true,
        isInclude: true,
        description: '保障车辆碰撞损失',
        type: 7,
        name: '车辆碰撞保障',
        isFromCtrip: false,
      },
      {
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额THB 10000',
            coverageWithoutPlatformInsurance: '原起赔额THB 10000可赔',
            currencyCode: 'THB',
            coverageWithPlatformInsuranceV2:
              '*起赔额及以下部分由国内保险公司提供',
            packageId: 393422,
            maxExcess: 0,
            minExcess: 0,
          },
        ],
        code: 'TPL',
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况',
              ],
            },
            {
              title: '还车时向车行提交理赔',
              content: [
                '在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        isAddFulCoverage: true,
        isInclude: true,
        description: '保障第三方车辆或人员伤害损失',
        type: 7,
        name: '第三者责任保障',
        isFromCtrip: false,
      },
      {
        giveUp: false,
        insuranceStatus: 63003,
        itemUrl:
          'https://dmzstg1.pa18.com/pa18shopnst%2Fdo%2Fera%2FPasCommon%2FdownloadPolicyStream%3FpolicyNo%3DTxhYxTsmb%252BPVc4UuSBmCilewrChsJBza3QJCVrQ66yCk6jrDDs0nrImpojpS0zoSZx8gUmixjUb3N433J1Zvy2T40wXwytcEhdA5Lf4ksPLKr2OuDUEVVPjxbElIVdjZEmTMLGyboT20aadqZU7Skh%252FJCY97wvcNhuCqFNFaYAc%253D%2F%26partnerCode%3DP_CTRIP_GA',
        code: 'MP18021541PK00080231',
        insuranceStatusName: {
          title: '已出保',
          color: 'green',
        },
        isInclude: true,
        description: '驾乘意外险短描述',
        isFromCtrip: true,
        name: '驾乘意外险',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '（国内保险公司提供）',
            coverageWithPlatformInsuranceV2: '¥75/天',
            coverageWithoutPlatformInsurance: '国内保险公司提供¥75/天',
          },
        ],
      },
      {
        giveUp: false,
        insuranceStatus: 63002,
        code: 'MP18022460PK00131282',
        insuranceStatusName: {
          title: '已支付',
          color: 'green',
        },
        isInclude: true,
        description:
          '赔偿车损/盗抢的自付部分，以及玻璃、轮胎、底盘破损，补偿道路救援费',
        isFromCtrip: true,
        name: '安心补充险',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '（国内保险公司提供）',
            coverageWithPlatformInsuranceV2: '¥25/天',
            coverageWithoutPlatformInsurance: '国内保险公司提供¥25/天',
          },
        ],
      },
    ],
    claimsProcess: [
      {
        subTitle:
          '您购买的套餐含国内保险公司提供的驾乘意外险，请参照如下理赔流程申请理赔',
        url: 'https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf',
        subObject: [
          {
            title: '驾乘意外险理赔流程步骤一名称',
            content: ['驾乘意外险理赔流程步骤一描述'],
            type: 1,
          },
          {
            title: '驾乘意外险理赔流程步骤二名称',
            content: ['驾乘意外险理赔流程步骤二描述'],
            type: 2,
          },
        ],
      },
      {
        subTitle:
          '您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔',
        url: ' https://pages.c-ctrip.com/tour/InsuranceClause/bcqxlpcl.pdf',
        subObject: [
          {
            title: '报案',
            content: [
              '发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>',
            ],
            type: 1,
          },
          {
            title: '车损重要理赔材料',
            content: ['车损照片、维修费用支付凭证、维修/车损清单、警方报告等'],
            type: 2,
          },
          {
            title: '理赔审核',
            content: [
              '材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充',
            ],
            type: 3,
          },
          {
            title: '支付赔款',
            content: ['理赔款会以银行转账方式支付到被保险人提供的境内银行账户'],
            type: 4,
          },
          {
            title: '报案',
            content: [
              '发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>',
            ],
            type: 5,
          },
          {
            title: '车损重要理赔材料',
            content: ['车损照片、维修费用支付凭证、维修/车损清单、警方报告等'],
            type: 6,
          },
          {
            title: '理赔审核',
            content: [
              '材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充',
            ],
            type: 7,
          },
          {
            title: '支付赔款',
            content: ['理赔款会以银行转账方式支付到被保险人提供的境内银行账户'],
            type: 8,
          },
        ],
      },
    ],
    reminder: [
      {
        subText:
          '如签署合同时，发现额外收费或强制购买，请不要签字，当场联系携程客服协助解决。',
        text: '温馨提示：',
      },
      {
        link: {
          urlDesc:
            'We would like to inform you that this customer has already purchased excess wavier and roadside service waiver through Ctrip. To prevent customers from accidentally purchasing duplicate coverage, we kindly request that you refrain from suggesting similar insurance packages at the counter. Failure to do so may result in a complaint filed with the appropriate authority.',
          text: '英文说明',
        },
        text: '已购驾乘意外险, 安心补充险，由于车行系统无法查看国内保险公司提供的保险，如被店员推销类似保险，请明确无需购买，可出示保单或{tag}',
      },
    ],
    ctripInsDeclaration: [
      {
        purchased: '该客人已经通过Trip.com购买了保障，涵盖',
        purchasedDesc:
          '客人已知晓需垫付相关费用后再向Trip.com提交理赔。为了防止客人购买重复保障，我们请贵处不要向客人推销相似的保险，否则可能会产生向相关当局的投诉。',
        partner: '亲爱的供应商伙伴：',
        statement: '携程声明',
      },
      {
        purchased:
          'ลูกค้าได้ซื้อความคุ้มครองประกันภัยผ่าน Trip.com แล้ว ซึ่งครอบคลุม',
        purchasedDesc:
          'ลูกค้าทราบว่าต้องชำระค่าธรรมเนียมที่เกี่ยวข้องก่อนยื่นเคลมต่อ Trip.com เพื่อป้องกันไม่ให้ลูกค้าซื้อประกันภัยซ้ำ โปรดอย่าเสนอขายกรมธรรม์ประกันภัยที่คล้ายกันให้กับลูกค้า การกระทำดังกล่าวอาจทำให้เกิดการร้องเรียนได้',
        partner: 'เรียน ซัพพลายเออร์พาร์ทเนอร์:',
        statement: 'ใบแจ้งยอดของ Trip.com',
      },
      {
        purchased:
          'The customer has already purchased insurance protection through Trip.com, including',
        purchasedDesc:
          'The customer is aware they need to pay the relevant fees before submitting a claim to Trip.com. To prevent the customer from purchasing duplicate insurance, please do not promote similar insurance policies to the customer. Doing so may result in complaints being raised.',
        partner: 'Dear supplier partner:',
        statement: 'Trip.com Statement',
      },
    ],
  },
  freeDeposit: {},
  rentalEssential: {
    pickUpMaterial: [
      {
        title: '中国台湾护照',
      },
      {
        title: '驾驶员本国驾照 + 国际驾照',
      },
      {
        title: '国际信用卡(带芯片、卡号凸字、不带银联标记)',
      },
    ],
    mustRead: [
      {
        title: '还车后注意',
        type: 2,
        subObject: [
          {
            title: '不明扣费咨询',
            type: 4,
            url: 'https://trip.fat1.qa.nt.ctripcorp.com/carhire/materialsMustRead?orderId=1128168993128230&uid=M269387928&locale=zh-CN&tab=2&activeTo=3&isHideNavBar=YES',
            note: '若您收到不明扣费，请先与门店索要账单',
          },
          {
            title: '查看报销凭证',
            type: 5,
            url: 'https://gateway.m.fat10668.qa.nt.ctripcorp.com/webapp/carhire/xsd/osdinvoice?id=%s&hideHeader=true&subEnv=fat14766',
            note: '海外租车无法开具发票，仅提供报销凭证',
          },
        ],
      },
    ],
    displayPickUpMaterial: false,
    displayMustRead: true,
  },
  receipt: {
    desc: '境外租车无法提供国内发票。itinerary作为一种常用消费凭证，一般可以用于报销',
    url: 'https://gateway.m.fat10668.qa.nt.ctripcorp.com/webapp/carhire/xsd/osdinvoice?id=%s&hideHeader=true&subEnv=fat14766',
  },
  cancelRuleInfo: {
    longTitle: 'In the following circumstances, you will not receive a refund:',
    ruleList: [
      '- You pick up the car late or drop it off early',
      '- You do not pick up the car within the given timeframe',
      '- You do not provide the required documents when picking up the car',
      "- The main driver's credit card does not have sufficient funds",
    ],
    cancelTip: {
      cancelTip: 'Car picked up - booking can no longer be canceled',
      cancelTipColor: 0,
    },
    customerCurrency: 'CNY',
    topTips: [
      {
        type: 0,
        description: '订单取消需收费',
      },
    ],
    osdCancelRuleInfo: {
      code: 'FreeCancel',
      title: '取消政策',
      complexSubTitle: {
        contentStyle: '1',
        stringObjs: [
          {
            content: '该订单确认后有损取消',
            style: '5',
          },
          {
            content: '，',
            style: '6',
          },
          {
            content: '该订单确认后有损取消',
            style: '6',
          },
        ],
      },
      showFree: false,
      type: 300,
      description: '注意：均为当地时间，订单确认前可免费取消',
      subTitle: '该订单确认后有损取消',
      items: [
        {
          showFree: false,
          lossFee: 873,
          title: '',
          subTitle: '订单确认后',
          description: '取消将收取全部租金作为违约金，最多收取¥10,000',
          key: '843431',
        },
      ],
    },
  },
  authType: 0,
  orderPrice: {
    localCurrency: 'THB',
    localPayAmountOnArrival: 0,
    feeDetail: {
      chargesInfos: [
        {
          title: '车辆租金+尊享套餐',
        },
        {
          code: 'Car',
          title: '基础租车费用',
          size: '约¥175×5天',
          currencyCode: 'CNY',
          currentTotalPrice: 873,
          description: '',
          type: 8,
          payMode: 2,
          items: [
            {
              code: 'Car',
              title: '基础租车费用',
              descList: [
                '额外驾驶员, 满电取还, 满电取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 增值税, 不限里程',
              ],
              description:
                '额外驾驶员, 满电取还, 满电取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 增值税, 不限里程',
              type: 8,
            },
            {
              title: '车辆碰撞保障',
              type: 1,
              code: 'Insurance',
            },
            {
              title: '第三者责任保障',
              type: 1,
              code: 'Insurance',
            },
          ],
        },
        {
          code: 'MP18021541PK00080231',
          size: '¥75×5天',
          title: '驾乘意外险',
          currencyCode: 'CNY',
          currentTotalPrice: 375,
        },
        {
          code: 'MP18022460PK00131282',
          size: '¥25×5天',
          title: '安心补充险',
          currencyCode: 'CNY',
          currentTotalPrice: 125,
        },
      ],
      notIncludeCharges: {},
      rentalTerm: 5,
      chargesSummary: {
        code: 'Summary',
        title: '全额',
        currencyCode: 'CNY',
        notices: [],
        currentTotalPrice: 1373,
        type: 103,
        items: [
          {
            code: '1',
            currentTotalPrice: 1373,
            title: '在线预付',
            currencyCode: 'CNY',
          },
        ],
      },
      equipmentInfos: [],
      couponInfos: [],
    },
    customerPayAmount: 1373,
    totalAmount: 1373,
    customerPayAmountOnArrival: 0,
    priceDesc: {
      possibleChangeList: [
        {
          title: '里程政策',
          description: '该车辆没有里程限制。',
        },
        {
          title: '能源政策',
          description: '满油取还',
        },
        {
          title: '年龄要求',
          description: '驾驶员年龄要求：25-65周岁',
        },
        {
          title: '额外驾驶员',
          description:
            '1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
        },
        {
          title: '营业时间外取还车',
          description:
            '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
        },
        {
          title: '当地费用',
          description:
            '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
        },
      ],
    },
    customerCurrency: 'CNY',
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 2257,
    environmentCost: 1,
    cacheFetchCost: 0,
    fetchCost: 2257,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1750398656534,
    afterFetch: 1750398658791,
  },
};

// 新契约 已完成订单 1128168993042720 18862/queryOrderSecondOpen 18862/queryPreloadingOrderDetail
export const ywcddsecondRes = {
  vehicleInfo: {
    passengerNum: 5,
    transmission: 'AT',
    imageUrl: '',
    doorNum: 4,
    vehicleGroupName: '中型轿车',
    vehicleName: '丰田 ',
    vendorVehicleCode: '',
  },
  pickupStore: {
    businessTimePolicy: {
      content: [''],
    },
    localDateTimeDTO: {
      date: '2025年6月19日',
      time: '14:00',
    },
    longitude: 100.574271,
    storeAddress:
      '19/23 Building A, Royal City Avenue, New Petchburi Rd., Bangkok',
    localDateTime: '2025-06-19 14:00:00',
    latitude: 13.753655,
    storeID: 228771,
    serviceType: '0',
    storeTel: '+86-************/+************',
    storeCode: 'BK4',
    storeName: 'Bangkok Downtown Rama 9\n',
    businessTimeDesc: '{"正常营业时间：":"05:00 - 22:00, 05:00 - 22:00"}',
    storeOpenTimeDesc: '{"":"05:00 - 22:00,05:00 - 22:00"}',
  },
  flight: {
    flightNumber: '',
  },
  checkResponseTime: 1750398372996.1282,
  checkRequestTime: 1750398372905.389,
  baseResponse: {
    code: '0',
    requestId: '55ef1db9-cd82-4838-82d6-e4d3476fef43',
    cost: 61,
    isSuccess: true,
    returnMsg: 'success',
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1750398373166+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '8700189803471789842',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a76ec27-486221-80942',
      },
    ],
  },
  timeInterval: 90.7392578125,
  resBodySize: 2384,
  returnStore: {
    businessTimePolicy: {
      content: [
        '22:01 - 23:00, 22:01 - 23:00取还车，请提前联系门店，门店预估收取服务费每次¥10，费用及税费以实际支付为准。',
      ],
      table: [
        {
          tableTitle: '时间段|服务费/次',
          code: 'BusinessTimePolicy',
          title: '服务费标准',
          notices: ['注意：请按时到达，如需提前或延后取还车务必提前联系门店。'],
          showFree: false,
          type: 400,
          subTitle: '还车',
          items: [
            {
              title: '22:01-23:00',
              showFree: false,
              description: '¥10',
            },
            {
              title: '22:01-23:00',
              showFree: false,
              description: '¥10',
            },
          ],
        },
      ],
    },
    localDateTimeDTO: {
      date: '2025年6月19日',
      time: '18:00',
    },
    longitude: 100.574271,
    storeAddress:
      '19/23 Building A, Royal City Avenue, New Petchburi Rd., Bangkok',
    localDateTime: '2025-06-19 18:00:00',
    latitude: 13.753655,
    storeID: 228771,
    serviceType: '0',
    storeTel: '+86-************/+************',
    storeCode: 'BK4',
    storeName: 'Bangkok Downtown Rama 9\n',
    businessTimeDesc: '{"正常营业时间：":"05:00 - 22:00, 05:00 - 22:00"}',
    storeOpenTimeDesc: '{"":"05:00 - 22:00,05:00 - 22:00"}',
  },
  orderBaseInfo: {
    orderId: 1128168993128230,
    orderStatus: {
      orderStatusCtrip: 'CAR_COMPLETED',
      orderStatusDesc: '已完成',
    },
    orderTip: {
      tipContentArray: ['行程已结束，感谢您的使用'],
    },
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 283,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 283,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1750398372904,
    afterFetch: 1750398373187,
  },
};

// 新契约  已完成订单 1128168993042720 18862/OSDQueryOrder  18862/queryOsdOrderDetail
export const ywcddRes = {
  timeInterval: 2247.376953125,
  returnStore: {
    storeLocation: '自行到店, 门店距曼谷华南蓬火车站驾车11公里',
    businessTimePolicy: {
      content: [
        '22:01 - 23:00, 22:01 - 23:00取还车，请提前联系门店，门店预估收取服务费每次¥10，费用及税费以实际支付为准。',
      ],
      table: [
        {
          tableTitle: '时间段|服务费/次',
          code: 'BusinessTimePolicy',
          title: '服务费标准',
          notices: ['注意：请按时到达，如需提前或延后取还车务必提前联系门店。'],
          showFree: false,
          type: 400,
          subTitle: '还车',
          items: [
            {
              title: '22:01-23:00',
              showFree: false,
              description: '¥10',
            },
            {
              title: '22:01-23:00',
              showFree: false,
              description: '¥10',
            },
          ],
        },
      ],
    },
    storeBusinessTime: [
      {
        timeStr: '05:00 - 22:00, 05:00 - 22:00',
        free: true,
      },
    ],
    localDateTimeDTO: {
      date: '2025年6月19日',
      time: '18:00',
    },
    longitude: 100.574271,
    storeAddress:
      '19/23 Building A, Royal City Avenue, New Petchburi Rd., Bangkok',
    localDateTime: '2025-06-19 18:00:00',
    latitude: 13.753655,
    countryId: 4,
    storeID: 228771,
    location: {
      city: {
        id: 359,
        name: '曼谷',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      locationType: 2,
      locationName: '曼谷华南蓬火车站',
      continent: {
        id: 1,
        name: '亚洲',
      },
      poiInfo: {
        type: 2,
        longitude: 100.516795,
        latitude: 13.739336,
      },
      locationCode: '85994',
      province: {
        id: 0,
      },
    },
    userSearchLocation: '曼谷华南蓬火车站',
    storeTel: '+86-************/+************',
    storeCode: 'BK4',
    storeName: 'Bangkok Downtown Rama 9\n',
    outOfHourDescription:
      '22:01 - 23:00, 22:01 - 23:00取还车，请提前联系门店，门店预估收取服务费每次¥10，费用及税费以实际支付为准。',
    serviceType: '0',
    businessTimeDesc: '{"正常营业时间：":"05:00 - 22:00, 05:00 - 22:00"}',
    storeOpenTimeDesc: '{"":"05:00 - 22:00,05:00 - 22:00"}',
  },
  vendorInfo: {
    vendorImageUrl:
      'https://dimg04.c-ctrip.com/images/20p0k12000ejfqidoA40D.png',
    platformName: '',
    platformCode: '',
    vendorName: 'Budget Thai',
  },
  addPayments: [
    {
      amount: 500,
      orderId: 1128168993128230,
      reason: '费用扣款',
      payTime: 1750325303136,
      payStatus: 1,
      additionalPaymentId: 96573,
    },
  ],
  coverage: [
    {
      code: 'ULM',
      title: '不限里程',
      type: 2,
      description: '租期内没有公里数限制。\n',
    },
    {
      title: '额外驾驶员',
      type: 3,
      code: 'ADD1',
    },
    {
      title: '满电取还',
      type: 4,
      code: 'FRFB',
    },
    {
      title: '满电取还',
      type: 4,
      code: 'FRFB',
    },
  ],
  orderBaseInfo: {
    orderStatus: {
      orderStatus: 3,
      orderStatusName: '已完成',
      orderStatusDesc: '已完成',
      orderStatusCtrip: 'CAR_COMPLETED',
    },
    orderId: 1128168993128230,
    uid: 'M269387928',
    orderTip: {
      tipContentArray: ['行程已结束，感谢您的使用'],
    },
    attr: {
      osdConfirmNew: 'true',
      voucherVersionV2: 'B',
      fulfillmentVersion: 'D',
      voucherVersion: 'B',
      isVehicle2: '1',
      osdModifyOrderVersion: 'B',
      ctripInsuranceVersion: 'B',
      klbVersion: '1',
      mergeVoucherFlag: '1',
      osdDetailVersion: 'B',
    },
    osdModifyNewOrder: false,
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1750398658900+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '907333067387803653',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a76ec27-486221-91385',
      },
    ],
  },
  carAssistantSummary: [
    {
      title: '取还车拍照留证',
      type: 7,
    },
    {
      title: '查看具体要求',
      type: 1,
      url: 'https://www.fat10668.qa.nt.ctripcorp.com/carhire/materialsMustRead?orderId=1128168993128230&h5View=1&locale=zh-CN&orderStatus=CAR_COMPLETED&vendorId=14088277&countryId=4&isHideNavBar=YES&subEnv=fat14605&transparentbar=1&newOsdVoucherV2=1&orderLocale=zh-CN,th-TH,en-US',
    },
    {
      title: '免费办理翻译件',
      type: 8,
      url: 'https://m.ctrip.com/webapp/carhire/xsd/xsdnewinterlicensePage',
    },
  ],
  operation: {
    extOperation: [],
    orderOperation: [
      {
        operationId: 3,
        enable: true,
        buttonName: '我要点评',
        url: 'https://m.ctrip.fat67.qa.nt.ctripcorp.com/webapp/vacations/order/public/order_comment?channel=car-rental&orderid=1128168993128230&sourceFrom=OSD_C_APP',
      },
      {
        operationId: 4,
        buttonName: '再次预订',
        enable: true,
      },
      {
        operationId: 6,
        enable: true,
        buttonName: '报销凭证',
        url: 'https://gateway.m.fat10668.qa.nt.ctripcorp.com/webapp/carhire/xsd/osdinvoice?id=%s&hideHeader=true&subEnv=fat14766',
        contents: [
          {
            description:
              '在线支付的费用可以在订单完成后，进入订详页面自助下载相关报销凭证；到店支付的费用，请在门店向供应商索要相关Receipt凭证。',
          },
        ],
      },
    ],
  },
  resBodySize: 53369,
  driverInfo: {
    areaCode: '86',
    lastName: 'anna',
    firstName: 'lee',
    contactWayList: [],
    age: '32',
    optionalContactWayList: [
      {
        contactWayName: '微信',
        isCanModify: true,
        contactWayType: '1',
      },
      {
        contactWayName: 'WhatsApp',
        isCanModify: true,
        contactWayType: '4',
      },
      {
        contactWayName: 'LINE',
        isCanModify: true,
        contactWayType: '2',
      },
      {
        contactWayName: 'KakaoTalk',
        isCanModify: true,
        contactWayType: '3',
      },
      {
        contactWayName: '当地电话',
        isCanModify: true,
        contactWayType: '0',
      },
    ],
    email: 'sh***<EMAIL>',
    flightNo: '',
    isChangeContact: false,
    name: 'anna/lee',
    telphone: '189****9000',
  },
  osdPolicy: {
    carRentalMustRead: [
      {
        code: '4',
        sortNum: 1,
        title: '额外驾驶员',
        type: 6,
      },
      {
        code: '4',
        sortNum: 2,
        title: '营业时间外取还车',
        type: 7,
      },
      {
        code: '4',
        sortNum: 3,
        title: '提前/延后取还车',
        type: 41,
      },
      {
        code: '4',
        sortNum: 4,
        title: '费用须知',
        type: 40,
      },
      {
        code: '1',
        sortNum: 1,
        title: '确认政策',
        content: [
          '根据该车行近期订单表现，预订此产品后平均需要1分钟确认订单是否预订成功。\n',
        ],
        type: 0,
      },
      {
        code: '1',
        sortNum: 2,
        title: '取消政策',
        content: ['订单确认后取消将收取全部租金作为违约金，最多收取¥10,000'],
        type: 1,
        table: [
          {
            code: 'FreeCancel',
            title: '取消政策',
            complexSubTitle: {
              contentStyle: '1',
              stringObjs: [
                {
                  content: '该订单确认后有损取消',
                  style: '5',
                },
                {
                  content: '，',
                  style: '6',
                },
                {
                  content: '该订单确认后有损取消',
                  style: '6',
                },
              ],
            },
            showFree: false,
            type: 300,
            description: '注意：均为当地时间，订单确认前可免费取消',
            subTitle: '该订单确认后有损取消',
            items: [
              {
                showFree: false,
                title: '',
                subTitle: '订单确认后',
                description: '取消将收取全部租金作为违约金，最多收取¥10,000',
                key: '843431',
              },
            ],
          },
        ],
      },
      {
        code: '1',
        sortNum: 3,
        title: '押金说明',
        content: ['该门店不收取押金'],
        type: 2,
        table: [
          {
            title: '押金',
            showFree: false,
            description: '该门店不收取押金',
          },
        ],
      },
      {
        code: '1',
        sortNum: 4,
        title: '里程政策',
        content: ['该车辆没有里程限制。'],
        type: 3,
      },
      {
        code: '1',
        sortNum: 5,
        title: '能源政策',
        content: [
          '满油取还',
          '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
        ],
        type: 4,
      },
      {
        code: '1',
        sortNum: 6,
        title: '年龄要求',
        content: [
          '驾驶员年龄要求：25-65周岁',
          '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
          '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
        ],
        type: 5,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '里程政策',
            content: ['该车辆没有里程限制。'],
            type: 3,
          },
          {
            code: '2',
            sortNum: 2,
            title: '能源政策',
            content: [
              '满油取还',
              '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
            ],
            type: 4,
          },
          {
            code: '2',
            sortNum: 3,
            title: '年龄要求',
            content: [
              '驾驶员年龄要求：25-65周岁',
              '18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
              '65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。',
              '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
            ],
            type: 5,
          },
          {
            code: '2',
            sortNum: 4,
            title: '额外驾驶员',
            content: [
              '1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
            ],
            type: 6,
          },
          {
            code: '2',
            sortNum: 5,
            title: '营业时间外取还车',
            content: [
              '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
            ],
            type: 7,
          },
          {
            code: '2',
            sortNum: 6,
            title: '当地费用',
            content: [
              '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
            ],
            type: 16,
          },
        ],
        title: '费用须知',
        content: [],
        type: 40,
        sortNum: 1,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '提前取车',
            content: [
              '若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。',
            ],
            type: 17,
          },
          {
            code: '2',
            sortNum: 2,
            title: '延迟取车',
            content: [
              '若您延迟到门店取车，请在预订取车时间前提前联系门店（或平台）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。',
            ],
            type: 18,
          },
          {
            code: '2',
            sortNum: 3,
            title: '提前还车',
            content: [
              '提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。',
            ],
            type: 10,
          },
          {
            code: '2',
            sortNum: 4,
            title: '延迟还车',
            content: [
              '若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。',
            ],
            type: 11,
          },
        ],
        title: '提前/延后取还车',
        content: [],
        type: 41,
        sortNum: 2,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '签合同前核对金额',
            content: [
              '办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。',
            ],
            type: 59,
          },
          {
            code: '2',
            sortNum: 2,
            title: '验车留存有效凭证',
            content: [
              '取车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身、油量里程、车损特写等）。 \n因各地车损处理方式不同，部分国家并非及时确认车损 ，需要您保留有效还车车况凭证（注意开启相机拍照的时间显示功能，需证明照片拍摄时间），否则后续产生车损争议平台可能因当地行业操作，以供应商凭证为准。',
            ],
            type: 60,
          },
        ],
        title: '取还车注意事项',
        content: [],
        type: 58,
        sortNum: 3,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 2,
            title: '管理费',
            content: [
              '如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。',
              '此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。',
            ],
            type: 22,
          },
        ],
        title: '租车保障',
        content: [],
        type: 20,
        sortNum: 4,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '额外设备',
            content: [
              '额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。',
            ],
            type: 24,
          },
          {
            code: '2',
            sortNum: 2,
            title: '税费',
            content: ['所有额外服务将会收取销售税费和当地费用。'],
            type: 25,
          },
        ],
        title: '附加服务',
        content: [],
        type: 23,
        sortNum: 5,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '修改',
            content: [
              '如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。',
            ],
            type: 31,
          },
          {
            code: '2',
            sortNum: 2,
            title: 'No show（未取车）',
            content: [
              'noshow 是指当你：',
              '计划提前取消订单但未告知我们，或者',
              '未在规定的时间内取车，或者',
              '无法提供取车时要出示的文件，或者',
              '无法提供主驾驶员名下有足够额度的信用卡',
              '以上情形，你之前预定的订单金额不会退还给你。',
            ],
            type: 32,
          },
        ],
        title: '取消，未取车和修改',
        content: [],
        type: 42,
        sortNum: 7,
      },
      {
        code: '2',
        subObject: [
          {
            code: '2',
            sortNum: 1,
            title: '意外或故障',
            content: [
              '事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。',
            ],
            type: 34,
          },
          {
            code: '2',
            sortNum: 2,
            title: '道路救援',
            content: [
              '道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。',
            ],
            type: 35,
          },
          {
            code: '2',
            sortNum: 3,
            title: '遗失钥匙',
            content: ['遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用'],
            type: 36,
          },
          {
            code: '2',
            sortNum: 4,
            title: '安全带',
            content: [
              '安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。',
            ],
            type: 37,
          },
          {
            code: '2',
            sortNum: 5,
            title: '禁止吸烟',
            content: [
              '禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。',
            ],
            type: 38,
          },
          {
            code: '2',
            sortNum: 6,
            title: '价格计算',
            content: [
              '价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。',
              '取车的时候，客人可能会选择不同的车，而不是预订时候的车。',
              '如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。',
              '如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。',
            ],
            type: 39,
          },
          {
            code: '2',
            sortNum: 7,
            title: '违章条款',
            content: [
              '请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中可能产生的违章罚款费用以及供应商代为交涉产生的行政费用（也称“违章管理费”）。\n由于车辆登记在租车公司名下，因此产生违章时，交管局会直接通知租车公司，车行将会把承租人信息提供给当局。所有租车合同中均包含信用卡自动扣费授权条款，用于处理违章和路费欠缴事宜。提车时主驾驶人需签字确认该条款，因此在发生违章时，租车公司通常不会提前通知您，直接从信用卡中扣除当局确认所产生的行政费用（通常按违章次数进行收取）。\n由于隐私法律的限制，大部分车行无法代缴罚金，是由当地相关部门以邮件或纸质平邮的形式，通过您在租车合同上预留的邮箱或者地址通知您。',
            ],
            type: 13,
          },
        ],
        title: '租车公司重要信息',
        content: [],
        type: 33,
        sortNum: 8,
      },
    ],
    crossLocationsPolicy: {
      notes: [
        '若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。除特别说明的情况外，“跨境费用”为前往单个国家所需的费用。',
      ],
      crossLocationsInfos: [
        {
          subTitle: '门店支持在以下区域跨境使用车辆：',
          crossTypeName: '跨境政策',
          title: '选择计划前往的国家',
          summaryPolicies: ['若您的行程中涉及跨境，请提前选择'],
          crossType: 3,
          locations: [
            {
              status: 3,
              isSelected: false,
              regionId: '7',
              name: '阿富汗',
              statusName: '不允许跨境',
              firstChar: 'A',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '9',
              name: '阿联酋',
              statusName: '不允许跨境',
              firstChar: 'A',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '145',
              name: '阿塞拜疆',
              statusName: '不允许跨境',
              firstChar: 'A',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '150',
              name: '阿曼',
              statusName: '不允许跨境',
              firstChar: 'A',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '205',
              name: '巴勒斯坦',
              statusName: '不允许跨境',
              firstChar: 'B',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '17',
              name: '巴基斯坦',
              statusName: '不允许跨境',
              firstChar: 'B',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '212',
              name: '不丹',
              statusName: '不允许跨境',
              firstChar: 'B',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '118',
              name: '巴林',
              statusName: '不允许跨境',
              firstChar: 'B',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '26',
              name: '朝鲜',
              statusName: '不允许跨境',
              firstChar: 'C',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '215',
              name: '东帝汶',
              statusName: '不允许跨境',
              firstChar: 'D',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '32',
              name: '菲律宾',
              statusName: '不允许跨境',
              firstChar: 'F',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '42',
              name: '韩国',
              statusName: '不允许跨境',
              firstChar: 'H',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '176',
              name: '哈萨克斯坦',
              statusName: '不允许跨境',
              firstChar: 'H',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '143',
              name: '吉尔吉斯斯坦',
              statusName: '不允许跨境',
              firstChar: 'J',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '50',
              name: '柬埔寨',
              statusName: '不允许跨境',
              firstChar: 'J',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '163',
              name: '卡塔尔',
              statusName: '不允许跨境',
              firstChar: 'K',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '53',
              name: '科威特',
              statusName: '不允许跨境',
              firstChar: 'K',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '55',
              name: '老挝',
              statusName: '不允许跨境',
              firstChar: 'L',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '56',
              name: '黎巴嫩',
              statusName: '不允许跨境',
              firstChar: 'L',
            },
            {
              status: 2,
              policy: '跨境政策中文版',
              regionId: '2',
              isSelected: false,
              statusName: '条件跨境',
              name: '马来西亚',
              firstChar: 'M',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '67',
              name: '蒙古',
              statusName: '不允许跨境',
              firstChar: 'M',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '68',
              name: '孟加拉国',
              statusName: '不允许跨境',
              firstChar: 'M',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '70',
              name: '缅甸',
              statusName: '不允许跨境',
              firstChar: 'M',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '146',
              name: '马尔代夫',
              statusName: '不允许跨境',
              firstChar: 'M',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '74',
              name: '尼泊尔',
              statusName: '不允许跨境',
              firstChar: 'N',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '78',
              name: '日本',
              statusName: '不允许跨境',
              firstChar: 'R',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '82',
              name: '沙特阿拉伯',
              statusName: '不允许跨境',
              firstChar: 'S',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '83',
              name: '斯里兰卡',
              statusName: '不允许跨境',
              firstChar: 'S',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '195',
              name: '土库曼斯坦',
              statusName: '不允许跨境',
              firstChar: 'T',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '89',
              name: '土耳其',
              statusName: '不允许跨境',
              firstChar: 'T',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '250',
              name: '塔吉克斯坦',
              statusName: '不允许跨境',
              firstChar: 'T',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '260',
              name: '文莱达鲁萨兰国',
              statusName: '不允许跨境',
              firstChar: 'W',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '144',
              name: '乌兹别克斯坦',
              statusName: '不允许跨境',
              firstChar: 'W',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '3',
              name: '新加坡',
              statusName: '不允许跨境',
              firstChar: 'X',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '100',
              name: '叙利亚',
              statusName: '不允许跨境',
              firstChar: 'X',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '102',
              name: '也门',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '103',
              name: '伊拉克',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '104',
              name: '伊朗',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '105',
              name: '以色列',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '107',
              name: '印度',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '108',
              name: '印度尼西亚',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '110',
              name: '约旦',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '175',
              name: '亚美尼亚',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '111',
              name: '越南',
              statusName: '不允许跨境',
              firstChar: 'Y',
            },
            {
              status: 3,
              isSelected: false,
              regionId: '1',
              name: '中国',
              statusName: '不允许跨境',
              firstChar: 'Z',
            },
          ],
        },
      ],
      title: '旅行限制',
    },
  },
  vehicleInfo: {
    fuelNoteTitle: '汽油或柴油',
    seriesWarning: {
      introduce: {
        title: '同组车型',
        description:
          '同组车型是指按照车型功能、空间大小、座位数和车门数划分的紧凑型轿车、中型轿车、小型SUV等等这些分类。您在网上预订时选择的车并不能保证到店就一定取到一模一样的，可能会得到同组车型中的其他车。',
      },
      carProtection: {
        title: '取车保障',
        description:
          '在库存充足的情况下，车行会尽量提供您在预订时所选的车型\n如果这款车暂时没有库存，车行便会为您提供同车型组内的其他车型\n若因门店原因需要调整车型，不额外收取车型变更费用',
      },
      video: 'https://ak-v.tripcdn.com/videos/9D0g2r000001j0rrk1E0A.mp4',
      cover: 'https://ak-d.tripcdn.com/images/1tg1u12000dwdf1uw1DCD.png',
      cases: [
        {
          vehicleGroupName: '车型组',
          representativeVehicleName: '代表车型',
          vehicleGroupItems: '车组包含',
          vehicleGroupCode: 'default',
        },
        {
          vehicleGroupName: '紧凑型轿车',
          representativeVehicleName: '福特嘉年华',
          vehicleGroupItems: '丰田卡罗拉、现代雅绅特',
          vehicleGroupCode: 'D',
        },
        {
          vehicleGroupName: '中大型轿车',
          representativeVehicleName: '丰田凯美瑞',
          vehicleGroupItems: '雪佛兰迈锐宝、大众帕萨特',
          vehicleGroupCode: 'S',
        },
        {
          vehicleGroupName: '中大型SUV',
          representativeVehicleName: '大众途观',
          vehicleGroupItems: '丰田RAV4、吉普指南者',
          vehicleGroupCode: 'R',
        },
      ],
    },
    fuelType: 2416,
    vendorVehicleCode: '',
    vehicleId: 531861,
    vehicleDisplacement: '',
    vehicleGroupName: '中型轿车',
    transmission: 'AT',
    esgInfo: {},
    passengerNum: 5,
    imageUrl: '',
    sippCode: 'MCAR',
    vehicleImgUrl: '',
    hasAC: false,
    fourDrive: false,
    luggageNum: 0,
    doorNum: 4,
    vehicleGroupId: 13,
    special: false,
    transmissionType: 1,
    fuelNote:
      '门店会根据库存情况为您提供汽油车或者柴油车，具体燃油类型与加油型号以门店告知为准。',
    vehicleName: '丰田 ',
  },
  pickupStore: {
    location: {
      city: {
        id: 359,
        name: '曼谷',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      locationType: 2,
      locationName: '曼谷华南蓬火车站',
      continent: {
        id: 1,
        name: '亚洲',
      },
      poiInfo: {
        type: 2,
        longitude: 100.516795,
        latitude: 13.739336,
      },
      locationCode: '85994',
      province: {
        id: 0,
      },
    },
    storeAddress:
      '19/23 Building A, Royal City Avenue, New Petchburi Rd., Bangkok',
    storeCode: 'BK4',
    storeLocation: '自行到店, 门店距曼谷华南蓬火车站驾车11公里',
    localDateTime: '2025-06-19 14:00:00',
    storeName: 'Bangkok Downtown Rama 9\n',
    storeBusinessTime: [
      {
        timeStr: '05:00 - 22:00, 05:00 - 22:00',
        free: true,
      },
    ],
    latitude: 13.753655,
    storeOpenTimeDesc: '{"":"05:00 - 22:00,05:00 - 22:00"}',
    countryId: 4,
    guideStep: [
      {
        image: '',
        content:
          '如需取车，请前往位于拉玛九路（Rama 9 Road）的 Budget 办事处，从高速公路出口出来后直行，经过 Golden Tulip Sovereign Hotel。左转进入 Royal City Avenue。办事处位于右侧约 50 米处。在这里，我们友好的工作人员将在现场完成您的租赁协议和取车流程。                                                                                                                                                                                                                         \n',
      },
    ],
    storeTel: '+86-************/+************',
    localDateTimeDTO: {
      date: '2025年6月19日',
      time: '14:00',
    },
    businessTimeDesc: '{"正常营业时间：":"05:00 - 22:00, 05:00 - 22:00"}',
    longitude: 100.574271,
    businessTimePolicy: {
      content: [''],
    },
    waitTimeDesc: '',
    serviceType: '0',
    userSearchLocation: '曼谷华南蓬火车站',
    storeID: 228771,
  },
  baseResponse: {
    code: '200',
    requestId: '5776b6aa-6d66-4b6f-af89-a7185946ee10',
    cost: 2158,
    isSuccess: true,
    returnMsg: 'success',
  },
  flight: {
    flightNumber: '',
  },
  osdRefund: {
    refundProgressList: [],
  },
  checkResponseTime: 1750398658782.864,
  checkRequestTime: 1750398656535.487,
  continuePayInfo: {
    needContinuePay: false,
  },
  platformInsurance: {
    insPackageId: 5,
    extraDesc: [
      '',
      '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比<url>https://www.baidu.com</url>实际赔付范围与标准以门店合同为准-卡拉比',
      '',
      '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比<url>https://www.baidu.com</url>实际赔付范围与标准以门店合同为准-卡拉比',
    ],
    packageInfos: [
      {
        insPackageId: 5,
        naked: false,
        defaultBomCode: '',
        currencyCode: 'THB',
        defaultPackageId: 393422,
        gapPrice: 0,
        isDefault: true,
        stepPrice: 0,
        lowestDailyPrice: 0,
        packageName: '尊享套餐',
        guaranteeDegree: 0,
      },
    ],
    insuranceItems: [
      {
        productId: 393422,
        description: '保障车辆碰撞损失',
        converageExplain: {
          title: '承保范围',
          content: [
            '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后',
          ],
        },
        excessTable: {
          title: '起赔额 0起赔额',
          content: [
            '*套餐中包含国内保险公司提供的安心补充险，更多保障内容可点击安心补充险查看',
          ],
          subObject: [
            {
              title: '车行承担',
              content: ['THB 10000以上部分'],
            },
            {
              title: '国内保险公司承担',
              content: ['THB 10000及以下部分（据实承担）'],
            },
            {
              title: '客户或承租方承担',
              content: ['THB 0'],
            },
          ],
        },
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '发生事故后报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        type: 7,
        title: '车辆碰撞保障',
        code: 'CDW',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额THB 10000',
            coverageWithoutPlatformInsurance: '原起赔额THB 10000可赔',
            currencyCode: 'THB',
            coverageWithPlatformInsuranceV2:
              '*起赔额及以下部分由国内保险公司提供',
            packageId: 393422,
            maxExcess: 0,
            minExcess: 0,
          },
        ],
        unConverageExplain: {
          title: '不承保范围',
          content: [
            '不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准',
          ],
        },
        isAddFulCoverage: true,
        isInclude: true,
        name: '车辆碰撞保障',
        isFromCtrip: false,
      },
      {
        productId: 393422,
        description: '保障第三方车辆或人员伤害损失',
        converageExplain: {
          title: '承保范围',
          content: [
            '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
          ],
        },
        excessTable: {
          title: '起赔额 0起赔额',
          content: [
            '*套餐中包含国内保险公司提供的安心补充险，更多保障内容可点击安心补充险查看',
          ],
          subObject: [
            {
              title: '车行承担',
              content: ['THB 10000以上部分'],
            },
            {
              title: '国内保险公司承担',
              content: ['THB 10000及以下部分（据实承担）'],
            },
            {
              title: '客户或承租方承担',
              content: ['THB 0'],
            },
          ],
        },
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况',
              ],
            },
            {
              title: '还车时向车行提交理赔',
              content: [
                '在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        type: 7,
        title: '第三者责任保障',
        code: 'TPL',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额THB 10000',
            coverageWithoutPlatformInsurance: '原起赔额THB 10000可赔',
            currencyCode: 'THB',
            coverageWithPlatformInsuranceV2:
              '*起赔额及以下部分由国内保险公司提供',
            packageId: 393422,
            maxExcess: 0,
            minExcess: 0,
          },
        ],
        isAddFulCoverage: true,
        isInclude: true,
        name: '第三者责任保障',
        isFromCtrip: false,
      },
      {
        description: '驾乘意外险短描述',
        converageExplain: {
          title: '承保范围',
          content: ['驾乘意外险长描述'],
        },
        coverDetailList: [
          {
            title: '最高保额',
            subTitle: '保障范围',
          },
          {
            title: '$1000',
            descList: ['驾乘意外险保障范围描述1', '驾乘意外险保障范围描述2'],
          },
          {
            title: '$2000',
            descList: [
              '驾乘意外险保障范围描述11',
              '驾乘意外险保障范围描述22',
              '驾乘意外险保障范围描述33',
            ],
          },
        ],
        insuranceNotice:
          'https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111',
        insuranceStatusName: {
          title: '已出保',
          color: 'green',
        },
        insuranceStatus: 63003,
        insuranceClauses: 'https://pages.c-ctrip.com/tour/pdf1712/23.pdf',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '（国内保险公司提供）',
            coverageWithPlatformInsuranceV2: '¥75/天',
            coverageWithoutPlatformInsurance: '国内保险公司提供¥75/天',
          },
        ],
        title: '驾乘意外险',
        code: 'MP18021541PK00080231',
        unConverageExplain: {
          title: '不承保范围',
          content: ['驾乘意外险不保障内容'],
        },
        giveUp: false,
        itemUrl:
          'https://dmzstg1.pa18.com/pa18shopnst%2Fdo%2Fera%2FPasCommon%2FdownloadPolicyStream%3FpolicyNo%3DTxhYxTsmb%252BPVc4UuSBmCilewrChsJBza3QJCVrQ66yCk6jrDDs0nrImpojpS0zoSZx8gUmixjUb3N433J1Zvy2T40wXwytcEhdA5Lf4ksPLKr2OuDUEVVPjxbElIVdjZEmTMLGyboT20aadqZU7Skh%252FJCY97wvcNhuCqFNFaYAc%253D%2F%26partnerCode%3DP_CTRIP_GA',
        isInclude: true,
        name: '驾乘意外险',
        isFromCtrip: true,
      },
      {
        description:
          '赔偿车损/盗抢的自付部分，以及玻璃、轮胎、底盘破损，补偿道路救援费',
        converageExplain: {
          title: '承保范围',
          content: [
            '由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方：',
          ],
        },
        coverDetailList: [
          {
            title: '最高保额',
            subTitle: '保障范围',
          },
          {
            title: '50000',
            descList: [
              '车行车损和盗抢保障的自付部分',
              '玻璃、轮胎、底盘损失的自付部分',
              '道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分',
              '涉及的税费及管理费用（此项以1500元为限）',
              '车行车损和盗抢保障的自付部分',
              '玻璃、轮胎、底盘损失的自付部分',
              '道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分',
              '涉及的税费及管理费用（此项以1500元为限）',
            ],
          },
          {
            title: '50000',
            descList: [
              '旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金',
              '旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金',
            ],
          },
          {
            title: '3000',
            descList: [
              '机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失',
              '证件被盗抢或丟失导致无法取车的租车费用损失',
              '车辆停运费或被迫更换费用',
              '航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用',
              '机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失',
              '证件被盗抢或丟失导致无法取车的租车费用损失',
              '车辆停运费或被迫更换费用',
              '航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用',
            ],
          },
          {
            title: '1000',
            descList: [
              '违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%',
              '违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%',
            ],
          },
          {
            title: '4000',
            descList: [
              '因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用',
              '因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用',
            ],
          },
        ],
        insuranceNotice:
          '1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。',
        insuranceStatusName: {
          title: '已支付',
          color: 'green',
        },
        insuranceStatus: 63002,
        insuranceClauses: 'https://pages.c-ctrip.com/tour/pdf1907/32.pdf',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '（国内保险公司提供）',
            coverageWithPlatformInsuranceV2: '¥25/天',
            coverageWithoutPlatformInsurance: '国内保险公司提供¥25/天',
          },
        ],
        title: '安心补充险',
        code: 'MP18022460PK00131282',
        unConverageExplain: {
          title: '不承保范围',
          content: [
            '1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;',
          ],
        },
        giveUp: false,
        isInclude: true,
        name: '安心补充险',
        isFromCtrip: true,
      },
    ],
    briefInsuranceItems: [
      {
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额THB 10000',
            coverageWithoutPlatformInsurance: '原起赔额THB 10000可赔',
            currencyCode: 'THB',
            coverageWithPlatformInsuranceV2:
              '*起赔额及以下部分由国内保险公司提供',
            packageId: 393422,
            maxExcess: 0,
            minExcess: 0,
          },
        ],
        code: 'CDW',
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '发生事故后报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        isAddFulCoverage: true,
        isInclude: true,
        description: '保障车辆碰撞损失',
        type: 7,
        name: '车辆碰撞保障',
        isFromCtrip: false,
      },
      {
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '起赔额THB 10000',
            coverageWithoutPlatformInsurance: '原起赔额THB 10000可赔',
            currencyCode: 'THB',
            coverageWithPlatformInsuranceV2:
              '*起赔额及以下部分由国内保险公司提供',
            packageId: 393422,
            maxExcess: 0,
            minExcess: 0,
          },
        ],
        code: 'TPL',
        claimProcess: {
          title: '理赔流程',
          subObject: [
            {
              title: '报警并联系车行',
              content: [
                '发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况',
              ],
            },
            {
              title: '还车时向车行提交理赔',
              content: [
                '在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料',
              ],
            },
            {
              title: '等待最终定审(45-60个工作日左右)',
              content: [
                '车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
              ],
            },
          ],
        },
        isAddFulCoverage: true,
        isInclude: true,
        description: '保障第三方车辆或人员伤害损失',
        type: 7,
        name: '第三者责任保障',
        isFromCtrip: false,
      },
      {
        giveUp: false,
        insuranceStatus: 63003,
        itemUrl:
          'https://dmzstg1.pa18.com/pa18shopnst%2Fdo%2Fera%2FPasCommon%2FdownloadPolicyStream%3FpolicyNo%3DTxhYxTsmb%252BPVc4UuSBmCilewrChsJBza3QJCVrQ66yCk6jrDDs0nrImpojpS0zoSZx8gUmixjUb3N433J1Zvy2T40wXwytcEhdA5Lf4ksPLKr2OuDUEVVPjxbElIVdjZEmTMLGyboT20aadqZU7Skh%252FJCY97wvcNhuCqFNFaYAc%253D%2F%26partnerCode%3DP_CTRIP_GA',
        code: 'MP18021541PK00080231',
        insuranceStatusName: {
          title: '已出保',
          color: 'green',
        },
        isInclude: true,
        description: '驾乘意外险短描述',
        isFromCtrip: true,
        name: '驾乘意外险',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '（国内保险公司提供）',
            coverageWithPlatformInsuranceV2: '¥75/天',
            coverageWithoutPlatformInsurance: '国内保险公司提供¥75/天',
          },
        ],
      },
      {
        giveUp: false,
        insuranceStatus: 63002,
        code: 'MP18022460PK00131282',
        insuranceStatusName: {
          title: '已支付',
          color: 'green',
        },
        isInclude: true,
        description:
          '赔偿车损/盗抢的自付部分，以及玻璃、轮胎、底盘破损，补偿道路救援费',
        isFromCtrip: true,
        name: '安心补充险',
        insuranceDetail: [
          {
            coverageWithoutPlatformInsuranceV2: '（国内保险公司提供）',
            coverageWithPlatformInsuranceV2: '¥25/天',
            coverageWithoutPlatformInsurance: '国内保险公司提供¥25/天',
          },
        ],
      },
    ],
    claimsProcess: [
      {
        subTitle:
          '您购买的套餐含国内保险公司提供的驾乘意外险，请参照如下理赔流程申请理赔',
        url: 'https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf',
        subObject: [
          {
            title: '驾乘意外险理赔流程步骤一名称',
            content: ['驾乘意外险理赔流程步骤一描述'],
            type: 1,
          },
          {
            title: '驾乘意外险理赔流程步骤二名称',
            content: ['驾乘意外险理赔流程步骤二描述'],
            type: 2,
          },
        ],
      },
      {
        subTitle:
          '您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔',
        url: ' https://pages.c-ctrip.com/tour/InsuranceClause/bcqxlpcl.pdf',
        subObject: [
          {
            title: '报案',
            content: [
              '发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>',
            ],
            type: 1,
          },
          {
            title: '车损重要理赔材料',
            content: ['车损照片、维修费用支付凭证、维修/车损清单、警方报告等'],
            type: 2,
          },
          {
            title: '理赔审核',
            content: [
              '材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充',
            ],
            type: 3,
          },
          {
            title: '支付赔款',
            content: ['理赔款会以银行转账方式支付到被保险人提供的境内银行账户'],
            type: 4,
          },
          {
            title: '报案',
            content: [
              '发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>',
            ],
            type: 5,
          },
          {
            title: '车损重要理赔材料',
            content: ['车损照片、维修费用支付凭证、维修/车损清单、警方报告等'],
            type: 6,
          },
          {
            title: '理赔审核',
            content: [
              '材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充',
            ],
            type: 7,
          },
          {
            title: '支付赔款',
            content: ['理赔款会以银行转账方式支付到被保险人提供的境内银行账户'],
            type: 8,
          },
        ],
      },
    ],
    reminder: [
      {
        subText:
          '如签署合同时，发现额外收费或强制购买，请不要签字，当场联系携程客服协助解决。',
        text: '温馨提示：',
      },
      {
        link: {
          urlDesc:
            'We would like to inform you that this customer has already purchased excess wavier and roadside service waiver through Ctrip. To prevent customers from accidentally purchasing duplicate coverage, we kindly request that you refrain from suggesting similar insurance packages at the counter. Failure to do so may result in a complaint filed with the appropriate authority.',
          text: '英文说明',
        },
        text: '已购驾乘意外险, 安心补充险，由于车行系统无法查看国内保险公司提供的保险，如被店员推销类似保险，请明确无需购买，可出示保单或{tag}',
      },
    ],
    ctripInsDeclaration: [
      {
        purchased: '该客人已经通过Trip.com购买了保障，涵盖',
        purchasedDesc:
          '客人已知晓需垫付相关费用后再向Trip.com提交理赔。为了防止客人购买重复保障，我们请贵处不要向客人推销相似的保险，否则可能会产生向相关当局的投诉。',
        partner: '亲爱的供应商伙伴：',
        statement: '携程声明',
      },
      {
        purchased:
          'ลูกค้าได้ซื้อความคุ้มครองประกันภัยผ่าน Trip.com แล้ว ซึ่งครอบคลุม',
        purchasedDesc:
          'ลูกค้าทราบว่าต้องชำระค่าธรรมเนียมที่เกี่ยวข้องก่อนยื่นเคลมต่อ Trip.com เพื่อป้องกันไม่ให้ลูกค้าซื้อประกันภัยซ้ำ โปรดอย่าเสนอขายกรมธรรม์ประกันภัยที่คล้ายกันให้กับลูกค้า การกระทำดังกล่าวอาจทำให้เกิดการร้องเรียนได้',
        partner: 'เรียน ซัพพลายเออร์พาร์ทเนอร์:',
        statement: 'ใบแจ้งยอดของ Trip.com',
      },
      {
        purchased:
          'The customer has already purchased insurance protection through Trip.com, including',
        purchasedDesc:
          'The customer is aware they need to pay the relevant fees before submitting a claim to Trip.com. To prevent the customer from purchasing duplicate insurance, please do not promote similar insurance policies to the customer. Doing so may result in complaints being raised.',
        partner: 'Dear supplier partner:',
        statement: 'Trip.com Statement',
      },
    ],
  },
  freeDeposit: {},
  rentalEssential: {
    pickUpMaterial: [
      {
        title: '中国台湾护照',
      },
      {
        title: '驾驶员本国驾照 + 国际驾照',
      },
      {
        title: '国际信用卡(带芯片、卡号凸字、不带银联标记)',
      },
    ],
    mustRead: [
      {
        title: '还车后注意',
        type: 2,
        subObject: [
          {
            title: '不明扣费咨询',
            type: 4,
            url: 'https://trip.fat1.qa.nt.ctripcorp.com/carhire/materialsMustRead?orderId=1128168993128230&uid=M269387928&locale=zh-CN&tab=2&activeTo=3&isHideNavBar=YES',
            note: '若您收到不明扣费，请先与门店索要账单',
          },
          {
            title: '查看报销凭证',
            type: 5,
            url: 'https://gateway.m.fat10668.qa.nt.ctripcorp.com/webapp/carhire/xsd/osdinvoice?id=%s&hideHeader=true&subEnv=fat14766',
            note: '海外租车无法开具发票，仅提供报销凭证',
          },
        ],
      },
    ],
    displayPickUpMaterial: false,
    displayMustRead: true,
  },
  receipt: {
    desc: '境外租车无法提供国内发票。itinerary作为一种常用消费凭证，一般可以用于报销',
    url: 'https://gateway.m.fat10668.qa.nt.ctripcorp.com/webapp/carhire/xsd/osdinvoice?id=%s&hideHeader=true&subEnv=fat14766',
  },
  cancelRuleInfo: {
    longTitle: 'In the following circumstances, you will not receive a refund:',
    ruleList: [
      '- You pick up the car late or drop it off early',
      '- You do not pick up the car within the given timeframe',
      '- You do not provide the required documents when picking up the car',
      "- The main driver's credit card does not have sufficient funds",
    ],
    cancelTip: {
      cancelTip: 'Car picked up - booking can no longer be canceled',
      cancelTipColor: 0,
    },
    customerCurrency: 'CNY',
    topTips: [
      {
        type: 0,
        description: '订单取消需收费',
      },
    ],
    osdCancelRuleInfo: {
      code: 'FreeCancel',
      title: '取消政策',
      complexSubTitle: {
        contentStyle: '1',
        stringObjs: [
          {
            content: '该订单确认后有损取消',
            style: '5',
          },
          {
            content: '，',
            style: '6',
          },
          {
            content: '该订单确认后有损取消',
            style: '6',
          },
        ],
      },
      showFree: false,
      type: 300,
      description: '注意：均为当地时间，订单确认前可免费取消',
      subTitle: '该订单确认后有损取消',
      items: [
        {
          showFree: false,
          lossFee: 873,
          title: '',
          subTitle: '订单确认后',
          description: '取消将收取全部租金作为违约金，最多收取¥10,000',
          key: '843431',
        },
      ],
    },
  },
  authType: 0,
  orderPrice: {
    localCurrency: 'THB',
    localPayAmountOnArrival: 0,
    feeDetail: {
      chargesInfos: [
        {
          title: '车辆租金+尊享套餐',
        },
        {
          code: 'Car',
          title: '基础租车费用',
          size: '约¥175×5天',
          currencyCode: 'CNY',
          currentTotalPrice: 873,
          description: '',
          type: 8,
          payMode: 2,
          items: [
            {
              code: 'Car',
              title: '基础租车费用',
              descList: [
                '额外驾驶员, 满电取还, 满电取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 增值税, 不限里程',
              ],
              description:
                '额外驾驶员, 满电取还, 满电取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 增值税, 不限里程',
              type: 8,
            },
            {
              title: '车辆碰撞保障',
              type: 1,
              code: 'Insurance',
            },
            {
              title: '第三者责任保障',
              type: 1,
              code: 'Insurance',
            },
          ],
        },
        {
          code: 'MP18021541PK00080231',
          size: '¥75×5天',
          title: '驾乘意外险',
          currencyCode: 'CNY',
          currentTotalPrice: 375,
        },
        {
          code: 'MP18022460PK00131282',
          size: '¥25×5天',
          title: '安心补充险',
          currencyCode: 'CNY',
          currentTotalPrice: 125,
        },
      ],
      notIncludeCharges: {},
      rentalTerm: 5,
      chargesSummary: {
        code: 'Summary',
        title: '全额',
        currencyCode: 'CNY',
        notices: [],
        currentTotalPrice: 1373,
        type: 103,
        items: [
          {
            code: '1',
            currentTotalPrice: 1373,
            title: '在线预付',
            currencyCode: 'CNY',
          },
        ],
      },
      equipmentInfos: [],
      couponInfos: [],
    },
    customerPayAmount: 1373,
    totalAmount: 1373,
    customerPayAmountOnArrival: 0,
    priceDesc: {
      possibleChangeList: [
        {
          title: '里程政策',
          description: '该车辆没有里程限制。',
        },
        {
          title: '能源政策',
          description: '满油取还',
        },
        {
          title: '年龄要求',
          description: '驾驶员年龄要求：25-65周岁',
        },
        {
          title: '额外驾驶员',
          description:
            '1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
        },
        {
          title: '营业时间外取还车',
          description:
            '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
        },
        {
          title: '当地费用',
          description:
            '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
        },
      ],
    },
    customerCurrency: 'CNY',
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 2257,
    environmentCost: 1,
    cacheFetchCost: 0,
    fetchCost: 2257,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1750398656534,
    afterFetch: 1750398658791,
  },
};

// 已确认订单 1128138820081243 18862/OSDQueryOrder
export const qrddRes = {
  modifyInfoDto: {},
  returnStore: {
    location: {
      city: { id: 359, name: '曼谷' },
      country: { id: 4, name: '泰国' },
      locationType: 1,
      locationName: '素万那普国际机场',
      continent: { id: 4, name: '泰国' },
      poiInfo: { type: 1, longitude: 100.750112, latitude: 13.689999 },
      locationCode: 'BKK',
      province: { id: 0 },
    },
    storeAddress:
      '2nd floor Arrival Hall Terminal , Gate 7 - 8 , 999, Bangna-Trad Rd., KM.15 Moo 10, Rachathewa Distritct, Bangpli, Samutprakan 10540',
    countryName: '泰国',
    storeCode: 'BKK53',
    storeLocation: '门店位于素万那普国际机场到达大厅',
    localDateTime: '2025-07-11 10:00:00',
    storeName: 'Suvarnabhumi International Airport',
    latitude: 13.689989,
    storeOpenTimeDesc: '{"":"24小时营业"}',
    storeGuide:
      '还车请前往素万那普机场的停车场，可按照机场附近写有【Car Park】的标志牌指引前往。找到写有Hertz的指示标志即可还车。',
    countryId: 4,
    storeTel: '+************/+************',
    localDateTimeDTO: { date: '2025年7月11日', time: '10:00' },
    businessTimeDesc: '{"正常营业时间：":"24小时营业"}',
    longitude: 100.750112,
    storeWay: '抵达后联系店员',
    businessTimePolicy: { content: [''] },
    serviceType: '0',
    userSearchLocation: '素万那普国际机场',
    storeID: 228754,
    disclaimer: 'The above information is provided by the branch',
    cityName: '曼谷',
  },
  orderPriceInfo: {
    currentTotalPrice: 88,
    localCurrencyCode: 'THB',
    currentCurrencyCode: 'CNY',
    packageType: 0,
    payMode: 2,
    localTotalPrice: 690,
    payModeDesc: '在线支付',
    localPrice: { title: '', totalPrice: 0, currencyCode: 'THB' },
    payAmount: 88,
    prepayPriceDetails: [
      { title: 'shark|common_carHireFee', totalPrice: 88, currencyCode: 'CNY' },
    ],
    coupons: [
      {
        promotionId: 841370114,
        deductionAmount: 152,
        displayName: '立减券',
        couponCode: 'lbctmzcrit',
      },
    ],
    priceDetails: [
      {
        title: 'shark|common_carHireFee',
        totalPrice: 152,
        currencyCode: 'CNY',
      },
    ],
    prepayPrice: { title: '', totalPrice: 88, currencyCode: 'CNY' },
    localPriceDetails: [],
    couponAmount: 152,
  },
  productDetails: [
    {
      productInfoList: [
        {
          minPackageItmes: [
            {
              code: 'ULM',
              title: '不限里程',
              type: 2,
              description: '租期内没有公里数限制。\n',
            },
            {
              code: 'FRFB',
              title: '满油取还',
              type: 4,
              description:
                '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
            },
          ],
        },
      ],
      claimsProcess: [
        {
          subObject: [
            {
              title: '报警并联系门店',
              content: [
                '请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。切勿自行安排维修事宜，或者与第三方私下处理。',
              ],
              type: 1,
            },
            {
              title: '还车时向车行提交事故材料',
              content: [
                '用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。',
              ],
              type: 2,
            },
          ],
        },
        {
          subTitle:
            '您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔',
          url: 'https://pages.c-ctrip.com/tour/carpolicy/%E7%90%86%E8%B5%94%E6%9D%90%E6%96%99.pdf',
          subObject: [
            {
              title: '报案',
              content: [
                '发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>',
              ],
              type: 1,
            },
            {
              title: '提供理赔材料',
              content: [
                '车损照片、维修费用支付凭证、维修/车损清单、警方报告等',
              ],
              type: 2,
            },
            {
              title: '理赔审核（1-3个工作日内反馈）',
              content: [
                '材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充',
              ],
              type: 3,
            },
            {
              title: '支付赔款（审核通过后3个工作日内）',
              content: [
                '理赔款会以银行转账方式支付到被保险人提供的境内银行账户',
              ],
              type: 4,
            },
          ],
        },
        {
          subTitle:
            '您购买的套餐含国内保险公司提供的驾乘意外险，请参照如下理赔流程申请理赔',
          url: 'https://pages.ctrip.com/tour/carpolicy/%E7%90%86%E8%B5%94%E6%9D%90%E6%96%991.pdf',
          subObject: [
            {
              title: '报案',
              content: [
                '发生事故后，请优先保障您的人身安全和健康，垫付相关费用，待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>',
              ],
              type: 1,
            },
            {
              title: '提供理赔材料',
              content: [
                '意外/医疗：门急诊/住院病历、医疗费用发票/收据原件、医院检查报告、事故证明等；财物损失：损失清单、购货凭证、损失证明等',
              ],
              type: 2,
            },
            {
              title: '理赔审核',
              content: [
                '材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充',
              ],
              type: 3,
            },
            {
              title: '支付赔款',
              content: [
                '理赔款会以银行转账方式支付到被保险人提供的境内银行账户',
              ],
              type: 4,
            },
          ],
        },
      ],
      briefInsuranceItems: [
        {
          insuranceDetail: [
            {
              coverageWithoutPlatformInsuranceV2: '起赔额THB 10000',
              coverageWithoutPlatformInsurance: '原起赔额THB 10000可赔',
              currencyCode: 'THB',
              coverageWithPlatformInsuranceV2:
                '*起赔额及以下部分由国内保险公司提供',
              packageId: 284257,
              maxExcess: 0,
              minExcess: 0,
            },
          ],
          code: 'CDW',
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '发生事故后报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          excessTable: {
            title: '起赔额 0起赔额',
            content: [
              '*套餐中包含国内保险公司提供的安心补充险，更多保障内容可点击安心补充险查看',
            ],
            subObject: [
              { title: '车行承担', content: ['THB 10000以上部分'] },
              {
                title: '国内保险公司承担',
                content: ['THB 10000及以下部分（据实承担）'],
              },
              { title: '客户或承租方承担', content: ['THB 0'] },
            ],
          },
          isAddFulCoverage: true,
          isInclude: true,
          description: '保障因意外事故对租赁车辆的车身造成的损伤',
          type: 7,
          name: '车辆碰撞保障',
          isFromCtrip: false,
        },
        {
          description: '道路救援费用，车行第三者保障的起赔额（如有）',
          insuranceNotice:
            '1、本计划的投保人为18周岁及以上，且常住地在中华人民共和国境内、临时赴境外旅行、并满足当地对驾驶年龄的法定要求\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。',
          insuranceStatusName: { title: '已出保', color: 'green' },
          insuranceStatus: 63003,
          insuranceClauses:
            'https://pages.c-ctrip.com/tour/carpolicy/%E4%BF%9D%E9%99%A9%E6%9D%A1%E6%AC%BE.pdf',
          insuranceDetail: [
            {
              coverageWithoutPlatformInsuranceV2: '（国内保险公司提供）',
              coverageWithPlatformInsuranceV2: '¥58/天',
              coverageWithoutPlatformInsurance: '国内保险公司提供¥58/天',
            },
          ],
          giveUp: false,
          code: 'MP18022460PK00131278',
          itemUrl:
            'https://baoxian.pingan.com/pa18shopnst%2Fdo%2Fera%2FPasCommon%2FdownloadPolicyStream%3FpolicyNo%3DTb4Gj1iX8rtTGB9xOggzWbuSbz0pvSF1z0a4j4FPAqaZSgnJgZOMvuek6yFyq638fU%252B4o%252BQ3PIo3g3%252F1LM1wvIqSj5k9DIHsJbRFVSLVhQFtEIs7PBTYsLGGLxLBPijN%252FnVu6o9S8elyUXw7b%252BAMaDr5ZiVXICA3ustzhM1skx4%253D%2F%26partnerCode%3DP_CTRIP_GA',
          isInclude: true,
          name: '安心补充险',
          isFromCtrip: true,
        },
        {
          description: '保障全车人员意外伤害及财物损失',
          insuranceNotice:
            '1、本计划的投保人为18周岁及以上，并满足当地对驾驶年龄的法定要求；被保险人年龄为0-100周岁。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。其中，71至80周岁的被保险人保险金为保单所载金额的一半，81至100周岁为四分之一。此外，按中国银保监会规定，任何不满10周岁的被保险人，其死亡累计保险金额不得超过人民币20万元；已满10周岁但未满18周岁的被保险人，其死亡累计保险金额不得超过人民币50万元。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010。\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n平安产险交通出行意外伤害保险（互联网版）条款\t注册号：C00001732312022080414693\n平安个人随车物品损失保险条款  注册号：H00001732112017050450351\n平安附加承保地域特约条款\t注册号为：C00001732322018031302341\t\n平安附加适用医疗机构范围约定保险条款\t注册号为：C00001731922019040903801\t\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。',
          insuranceStatusName: { title: '已出保', color: 'green' },
          insuranceStatus: 63003,
          insuranceClauses:
            'https://pages.ctrip.com/tour/carpolicy/%E4%BF%9D%E9%99%A9%E6%9D%A1%E6%AC%BE1.pdf',
          insuranceDetail: [
            {
              coverageWithoutPlatformInsuranceV2: '（国内保险公司提供）',
              coverageWithPlatformInsuranceV2: '¥30/天',
              coverageWithoutPlatformInsurance: '国内保险公司提供¥30/天',
            },
          ],
          giveUp: false,
          code: 'MP18021541PK00080231',
          itemUrl:
            'https://baoxian.pingan.com/pa18shopnst%2Fdo%2Fera%2FPasCommon%2FdownloadPolicyStream%3FpolicyNo%3DVULpd7hFX8ImZLu7GjvF1LrhZcum7Fa68T4RtYG4hj88x5ICTLJK3bmTDXNTQl8zvE2eq8%252B8Mhmmc9stzAfwO236EyXs6%252Frgrb0FwDZAYJuUjzB5oJ6bT02ATuglGe3FvHcX6zvUor4X4Mn06ke%252FPFVhi48k2rQSndJCEa6vJnc%253D%2F%26partnerCode%3DP_CTRIP_GA',
          isInclude: true,
          name: '驾乘意外险',
          isFromCtrip: true,
        },
      ],
      extraDesc: [],
      insPackageId: 1,
      insuranceItems: [
        {
          productId: 284257,
          description: '保障因意外事故对租赁车辆的车身造成的损伤',
          converageExplain: {
            title: '承保范围',
            content: [
              '在租车期间内，因碰撞或剐蹭导致的租赁车辆车身损伤。承租人需承担起赔额以内的费用。\n*实际赔付范围与标准以门店合同为准',
            ],
          },
          excessTable: {
            title: '起赔额 0起赔额',
            content: [
              '*套餐中包含国内保险公司提供的安心补充险，更多保障内容可点击安心补充险查看',
            ],
            subObject: [
              { title: '车行承担', content: ['THB 10000以上部分'] },
              {
                title: '国内保险公司承担',
                content: ['THB 10000及以下部分（据实承担）'],
              },
              { title: '客户或承租方承担', content: ['THB 0'] },
            ],
          },
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '发生事故后报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          type: 7,
          title: '车辆碰撞保障',
          code: 'CDW',
          insuranceDetail: [
            {
              coverageWithoutPlatformInsuranceV2: '起赔额THB 10000',
              coverageWithoutPlatformInsurance: '原起赔额THB 10000可赔',
              currencyCode: 'THB',
              coverageWithPlatformInsuranceV2:
                '*起赔额及以下部分由国内保险公司提供',
              packageId: 284257,
              maxExcess: 0,
              minExcess: 0,
            },
          ],
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '1）对第三方的车辆或财产造成的损失；\n2）车辆涉水造成的车辆损坏；\n3）因加错燃油类型导致车辆损坏；\n4）不爱惜车辆，在非铺装路面行驶；\n5）在正常道路行驶期间，对于公共设施的损坏；\n6）对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n7）由未登记“额外驾驶员”的其他人驾驶车辆；\n8）驾驶车辆跨境未告知工作人员，出境后发生车损；\n9）未经租车公司授权的修车，拖车产生的费用或损失；\n10）超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n11）车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n12）货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n13）由于自然灾害，或不可抗力因素导致的损失。\n*实际不承保范围以门店合同为准',
            ],
          },
          isAddFulCoverage: true,
          isInclude: true,
          name: '车辆碰撞保障',
          isFromCtrip: false,
        },
        {
          description: '道路救援费用，车行第三者保障的起赔额（如有）',
          converageExplain: {
            title: '承保范围',
            content: [
              '由国内知名保险公司提供，主要承保车损和盗抢保障的起赔额部分，车行第三者保障的自付部分，以及车行保障不包含的玻璃、轮胎、底盘等损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方：',
            ],
          },
          coverDetailList: [
            { title: '最高保额', subTitle: '保障范围' },
            {
              title: '￥50,000',
              descList: [
                '车行车损和盗抢保障的自付部分',
                '车行第三者保障的自付部分（此项以5000元为限）',
                '玻璃、轮胎（含轮毂）、底盘损失的自付部分',
                '因意外事故或机械故障导致的道路救援费、拖车费、人工费、锁/钥匙损坏后的替换费',
                '涉及的税费及管理费用（此项以1500元为限）',
                '含野生动物撞击导致的车损',
              ],
            },
            {
              title: '￥50,000',
              descList: [
                '旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金',
              ],
            },
            {
              title: '￥3,000',
              descList: [
                '机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失',
                '证件被盗抢或丟失导致无法取车的租车费用损失',
                '车辆停运费或被迫更换费用',
                '航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用',
              ],
            },
            {
              title: '￥1,000',
              descList: [
                '因意外事故、过失用车赔偿金(钥匙丢失、加错油），自费比例为20%',
              ],
            },
            {
              title: '￥4,000',
              descList: [
                '因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用',
              ],
            },
          ],
          insuranceNotice:
            '1、本计划的投保人为18周岁及以上，且常住地在中华人民共和国境内、临时赴境外旅行、并满足当地对驾驶年龄的法定要求\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。',
          insuranceStatusName: { title: '已出保', color: 'green' },
          insuranceStatus: 63003,
          insuranceClauses:
            'https://pages.c-ctrip.com/tour/carpolicy/%E4%BF%9D%E9%99%A9%E6%9D%A1%E6%AC%BE.pdf',
          insuranceDetail: [
            {
              coverageWithoutPlatformInsuranceV2: '（国内保险公司提供）',
              coverageWithPlatformInsuranceV2: '¥58/天',
              coverageWithoutPlatformInsurance: '国内保险公司提供¥58/天',
            },
          ],
          title: '安心补充险',
          code: 'MP18022460PK00131278',
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '1、不在车行提供的碰撞或盗抢保障的责任范围内的损失（玻璃、车轮、底盘除外）;\n2、造成第三方的人身/财产损失时，不在车行提供的第三者保障的责任范围内的损失;\n3、未经当地租车行认可的自主修车费用;\n4、由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、被保险人的故意行为导致的损失;\n6、证件不被租车公司认可导致无法取车的损失;\n7、租车公司未授权的驾驶人驾车导致的损失;\n8、汇率损失、刷卡费、信用卡费、汇款手续费;\n9、其他不属于保险合同条款范围内的损失和费用;',
            ],
          },
          giveUp: false,
          itemUrl:
            'https://baoxian.pingan.com/pa18shopnst%2Fdo%2Fera%2FPasCommon%2FdownloadPolicyStream%3FpolicyNo%3DTb4Gj1iX8rtTGB9xOggzWbuSbz0pvSF1z0a4j4FPAqaZSgnJgZOMvuek6yFyq638fU%252B4o%252BQ3PIo3g3%252F1LM1wvIqSj5k9DIHsJbRFVSLVhQFtEIs7PBTYsLGGLxLBPijN%252FnVu6o9S8elyUXw7b%252BAMaDr5ZiVXICA3ustzhM1skx4%253D%2F%26partnerCode%3DP_CTRIP_GA',
          isInclude: true,
          name: '安心补充险',
          isFromCtrip: true,
        },
        {
          description: '保障全车人员意外伤害及财物损失',
          converageExplain: {
            title: '承保范围',
            content: [
              '由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方：',
            ],
          },
          coverDetailList: [
            { title: '最高保额', subTitle: '保障范围' },
            { title: '50万/人，每车限额150万', descList: ['全车人员意外伤害'] },
            { title: '2万/人', descList: ['意外医疗'] },
            { title: '100/天，最高90天', descList: ['住院津贴'] },
            {
              title: '2万/车，每套物品限额3000元',
              descList: ['随车行李及财物损失'],
            },
            {
              title: '100万/车',
              descList: ['紧急医疗运送（救援电话+86-755-95511）'],
            },
            { title: '80万/车', descList: ['身故遗体送返'] },
            { title: '2万/车', descList: ['丧葬保险金'] },
          ],
          insuranceNotice:
            '1、本计划的投保人为18周岁及以上，并满足当地对驾驶年龄的法定要求；被保险人年龄为0-100周岁。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。其中，71至80周岁的被保险人保险金为保单所载金额的一半，81至100周岁为四分之一。此外，按中国银保监会规定，任何不满10周岁的被保险人，其死亡累计保险金额不得超过人民币20万元；已满10周岁但未满18周岁的被保险人，其死亡累计保险金额不得超过人民币50万元。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010。\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n平安产险交通出行意外伤害保险（互联网版）条款\t注册号：C00001732312022080414693\n平安个人随车物品损失保险条款  注册号：H00001732112017050450351\n平安附加承保地域特约条款\t注册号为：C00001732322018031302341\t\n平安附加适用医疗机构范围约定保险条款\t注册号为：C00001731922019040903801\t\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。',
          insuranceStatusName: { title: '已出保', color: 'green' },
          insuranceStatus: 63003,
          insuranceClauses:
            'https://pages.ctrip.com/tour/carpolicy/%E4%BF%9D%E9%99%A9%E6%9D%A1%E6%AC%BE1.pdf',
          insuranceDetail: [
            {
              coverageWithoutPlatformInsuranceV2: '（国内保险公司提供）',
              coverageWithPlatformInsuranceV2: '¥30/天',
              coverageWithoutPlatformInsurance: '国内保险公司提供¥30/天',
            },
          ],
          title: '驾乘意外险',
          code: 'MP18021541PK00080231',
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;',
            ],
          },
          giveUp: false,
          itemUrl:
            'https://baoxian.pingan.com/pa18shopnst%2Fdo%2Fera%2FPasCommon%2FdownloadPolicyStream%3FpolicyNo%3DVULpd7hFX8ImZLu7GjvF1LrhZcum7Fa68T4RtYG4hj88x5ICTLJK3bmTDXNTQl8zvE2eq8%252B8Mhmmc9stzAfwO236EyXs6%252Frgrb0FwDZAYJuUjzB5oJ6bT02ATuglGe3FvHcX6zvUor4X4Mn06ke%252FPFVhi48k2rQSndJCEa6vJnc%253D%2F%26partnerCode%3DP_CTRIP_GA',
          isInclude: true,
          name: '驾乘意外险',
          isFromCtrip: true,
        },
      ],
    },
  ],
  vendorInfo: {
    confirmDate: 1747898494000,
    platformName: 'HERTZ INTERNATIONAL, LTD',
    bizVendorCode: '14088193',
    platformCode: '',
    vendorName: 'Thrifty',
    broker: false,
    vendorImageUrl:
      'https://dimg04.c-ctrip.com/images/20p5112000ejfqlt702F3.png',
    confirmDateStr: '2025-05-22 15:21',
    commentInfo: {
      commentCount: 14,
      topScore: 5,
      level: '不错',
      link: '/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=228754&calabiVehicleId=53936&vehicleName=null&productCategoryId=35&isHideNavBar=YES',
      vendorGoodType: 1,
      hasComment: 1,
      exposedScore: 4.2,
    },
  },
  platformInsuranceReminder: {
    title: '您已购安心补充险, 驾乘意外险',
    explain: [
      {
        title:
          '由于车行系统无法查看国内保险公司提供的保险，如被店员推销类似保险，请明确无需购买，可出示保单或{tag}',
        link: {
          linkType: 3,
          color: 'blue',
          url: 'We would like to inform you that this customer has already purchased excess wavier and roadside service waiver through Ctrip. To prevent customers from accidentally purchasing duplicate coverage, we kindly request that you refrain from suggesting similar insurance packages at the counter. Failure to do so may result in a complaint filed with the appropriate authority.',
          text: '英文说明',
        },
      },
      {
        title:
          '如签署合同时，发现额外收费或强制购买，请您不要签字，当场联系携程客服协助解决或请店员用英文在纸上写下哪些项目是需要强制购买（These items are mandatory），并加上店员签名和日期，回国后提交给携程为您申诉处理。',
      },
      {
        title: '如发生事故，请先垫付相关费用，还车后向保险公司申请理赔，{tag}',
        link: { color: 'blue', text: '理赔流程', linkType: 4 },
      },
    ],
  },
  addPayments: [],
  useCalabiId: true,
  orderBaseInfo: {
    orderDate: 1747901477000,
    uId: 'M180970538',
    channelType: '17671',
    payMode: 2,
    extOperation: [
      {
        operationId: 17,
        buttonName: 'Resend Confirmation Email',
        enable: true,
      },
    ],
    sourceCountryId: 1,
    orderLocale: 'zh-CN',
    orderStatusName: '已确认',
    orderStatusCtrip: 'CAR_CONFIRMED',
    successSafeRentAuth: false,
    orderId: 1128138820081243,
    orderStatus: 2,
    allStatuses: [],
    allOperations: [
      { operationId: 3, enable: false, code: 4 },
      {
        operationId: 2,
        enable: true,
        buttonName: '取消订单',
        contents: [
          {
            description:
              '海外租车价格库存随时存在波动，重新预订价格可能会上涨，请你再次确认是否取消？',
          },
        ],
      },
      {
        code: -1,
        operationId: 7,
        enable: true,
        disableCode: 0,
        buttonName: '修改订单',
      },
      {
        operationId: 6,
        enable: true,
        buttonName: '报销凭证',
        url: 'https://m.ctrip.com/webapp/carhire/xsd/osdinvoice?id=1128138820081243&hideHeader=true',
        contents: [
          {
            description:
              '在线支付的费用可以在订单完成后，进入订详页面自助下载相关报销凭证；到店支付的费用，请在门店向供应商索要相关Receipt凭证。',
          },
        ],
      },
      {
        code: 0,
        operationId: 12,
        enable: false,
        display: 'none',
        buttonName: '取消修改',
      },
    ],
    orderTip: {
      tipContentArray: ['已为您预留车辆，取车时请记得携带身份证件'],
      tipType: 3,
      warnType: 0,
      tipContent: '',
    },
    orderStatusDesc: '已确认',
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1749003904917+0800)/',
    extension: [
      { id: 'CLOGGING_TRACE_ID', value: '7472285936736502617' },
      { id: 'RootMessageId', value: '921822-0a9184f5-485834-475353' },
    ],
  },
  resBodySize: 49458,
  refundProgressList: [],
  driverInfo: {
    flightNo: '',
    decryptTelphone: '13946646624',
    lastName: 'CE',
    age: '39',
    firstName: 'SHI',
    isChangeContact: false,
    decryptMail: '<EMAIL>',
    contactWayList: [],
    optionalContactWayList: [
      { contactWayName: '微信', isCanModify: true, contactWayType: '1' },
      { contactWayName: 'WhatsApp', isCanModify: true, contactWayType: '4' },
      { contactWayName: 'QQ', isCanModify: true, contactWayType: '5' },
      { contactWayName: '当地电话', isCanModify: true, contactWayType: '0' },
    ],
    telphone: '139****6624',
    email: 'dj***<EMAIL>',
    name: 'CE/SHI',
    areaCode: '86',
  },
  packageInfos: [
    {
      insPackageId: 1,
      naked: false,
      defaultBomCode: '',
      currencyCode: 'THB',
      defaultPackageId: 284257,
      gapPrice: 0,
      isDefault: true,
      stepPrice: 0,
      lowestDailyPrice: 0,
      packageName: '尊享套餐',
      guaranteeDegree: 0,
    },
  ],
  vehicleInfo: {
    doorNum: 4,
    esgInfo: {},
    fuelType: '2416',
    fourDrive: false,
    transmission: 'AT',
    vehicleGroupName: '小型轿车',
    fuelNote:
      '门店会根据库存情况为您提供汽油车或者柴油车，具体燃油类型与加油型号以门店告知为准。',
    displacement: '',
    imageUrl: 'https://dimg04.c-ctrip.com/images/0yc1x12000d4jngcs72AD.png',
    special: true,
    luggageNum: 1,
    hasAC: false,
    vehicleDisplacement: '',
    vehicleGroupId: 1001,
    fuelNoteTitle: '汽油或柴油',
    vehicleName: '丰田Yaris',
    passengerNum: 5,
    vendorVehicleCode: 'TOYOTA YARIS',
  },
  pickupStore: {
    location: {
      city: { id: 359, name: '曼谷' },
      country: { id: 4, name: '泰国' },
      locationType: 1,
      locationName: '素万那普国际机场',
      continent: { id: 4, name: '泰国' },
      poiInfo: { type: 1, longitude: 100.750112, latitude: 13.689999 },
      locationCode: 'BKK',
      province: { id: 0 },
    },
    storeAddress:
      '2nd floor Arrival Hall Terminal , Gate 7 - 8 , 999, Bangna-Trad Rd., KM.15 Moo 10, Rachathewa Distritct, Bangpli, Samutprakan 10540',
    countryName: '泰国',
    storeCode: 'BKK53',
    storeLocation: '门店位于素万那普国际机场到达大厅',
    localDateTime: '2025-07-10 10:00:00',
    storeName: 'Suvarnabhumi International Airport',
    latitude: 13.689989,
    storeOpenTimeDesc: '{"":"24小时营业"}',
    storeGuide:
      'Dollar租赁服务请前往Hertz柜台办理手续，柜台位于二楼的国际到达大厅，7-8号出口之间。\n国际或国内到达的乘客：从行李领取区出来后需步行到国际到达大厅，然后直接前往7号出口，柜台位于7-8号出口之间，写有黄色的Hertz标志。\n您办理完手续后，请下到一楼从7号出口出去，穿过马路到外侧道路，跟随写有【Car Rental】的指示牌前往停车场，在Hertz的指示标志处找工作人员取车。',
    countryId: 4,
    storeTel: '+************/+************',
    localDateTimeDTO: { date: '2025年7月10日', time: '10:00' },
    businessTimeDesc: '{"正常营业时间：":"24小时营业"}',
    longitude: 100.750112,
    storeWay: '抵达后联系店员',
    businessTimePolicy: { content: [''] },
    serviceType: '0',
    waitTimeDesc: '',
    userSearchLocation: '素万那普国际机场',
    storeID: 228754,
    disclaimer: 'The above information is provided by the branch',
    cityName: '曼谷',
  },
  baseResponse: {
    code: '200',
    requestId: '3a95cafb-6dba-452f-a1b3-e82f1ef0f3d5',
    cost: 851,
    isSuccess: true,
    returnMsg: 'success',
  },
  localesInfo: [
    {
      locale: 'zh-CN',
      ctripInsDeclaration: {
        purchased: '该客人已经通过Trip.com购买了保障，涵盖',
        purchasedDesc:
          '客人已知晓需垫付相关费用后再向Trip.com提交理赔。为了防止客人购买重复保障，我们请贵处不要向客人推销相似的保险，否则可能会产生向相关当局的投诉。',
        partner: '亲爱的供应商伙伴：',
        statement: '携程声明',
      },
      localeType: 1,
      localeName: '简体中文',
    },
    {
      locale: 'th-TH',
      ctripInsDeclaration: {
        purchased:
          'ลูกค้าได้ซื้อความคุ้มครองประกันภัยผ่าน Trip.com แล้ว ซึ่งครอบคลุม',
        purchasedDesc:
          'ลูกค้าทราบว่าต้องชำระค่าธรรมเนียมที่เกี่ยวข้องก่อนยื่นเคลมต่อ Trip.com เพื่อป้องกันไม่ให้ลูกค้าซื้อประกันภัยซ้ำ โปรดอย่าเสนอขายกรมธรรม์ประกันภัยที่คล้ายกันให้กับลูกค้า การกระทำดังกล่าวอาจทำให้เกิดการร้องเรียนได้',
        partner: 'เรียน ซัพพลายเออร์พาร์ทเนอร์:',
        statement: 'ใบแจ้งยอดของ Trip.com',
      },
      localeType: 2,
      localeName: '泰语',
    },
    {
      locale: 'en-US',
      ctripInsDeclaration: {
        purchased:
          'The customer has already purchased insurance protection through Trip.com, including',
        purchasedDesc:
          'The customer is aware they need to pay the relevant fees before submitting a claim to Trip.com. To prevent the customer from purchasing duplicate insurance, please do not promote similar insurance policies to the customer. Doing so may result in complaints being raised.',
        partner: 'Dear supplier partner:',
        statement: 'Trip.com Statement',
      },
      localeType: 3,
      localeName: '英语',
    },
  ],
  checkResponseTime: 1749003904755.851,
  checkRequestTime: 1749003903845.597,
  insuranceAndXProductDesc: [],
  continuePayInfo: { needContinuePay: false },
  extraInfos: [],
  extendedInfo: {
    attr: {
      osdConfirmNew: 'true',
      voucherVersionV2: 'B',
      isContractTemplates: 'true',
      fulfillmentVersion: 'D',
      isVehicle2: '1',
      voucherVersion: 'B',
      osdModifyOrderVersion: 'B',
      ctripInsuranceVersion: 'B',
      mergeVoucherFlag: '1',
      osdDetailVersion: 'A',
    },
    osdModifyOrderVersion: 'B',
    osdModifyNewOrder: false,
    carAssistantSummary: [
      { content: '取车材料' },
      { content: '办理驾照翻译件' },
    ],
    crossLocationsPolicy: {
      title: '旅行限制',
      crossLocationsInfos: [
        {
          crossType: 3,
          crossTypeName: '跨境政策',
          summaryPolicies: [
            '由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨境，建议您更换其它租车公司或车型组。',
          ],
        },
      ],
    },
    ctripInsuranceVersion: 'B',
    cesLink: {
      title: '本次预订租车是否方便？',
      url: 'https://trippoll.ctrip.com/trippollweb/newpollanswer?popup=close&surveygUID=6361883c-1a63-4c40-9cfa-61bc8951e60c&locale=zh-cn&needlogin=1',
    },
    pickUpMaterials: [
      {
        summaryTitle: '驾驶员需持有本人名下4项材料取车：',
        title: '取车要求',
        content: ['驾驶员年龄21-70周岁，且持有驾照至少满12个月'],
        summaryContent: [],
        type: 20,
        summaryContentObject: [
          { contentStyle: 'age', stringObjs: [{ content: '21-70周岁' }] },
          { contentStyle: 'licenseAge', stringObjs: [{ content: '≥12个月' }] },
        ],
      },
      {
        subObject: [
          {
            code: 'CN',
            subObject: [
              {
                title: '护照原件',
                content: [
                  '主驾驶员和其他额外驾驶员需提供他们名下的护照原件',
                  '护照签发地需与驾照发证国家/地区一致',
                ],
                type: 21,
              },
            ],
            title: '中国大陆护照',
            subTitle: '中国大陆护照原件',
          },
        ],
        title: '护照原件',
        subTitle: '',
        type: 0,
      },
      {
        subObject: [
          {
            type: 5,
            subObject: [
              {
                code: 'CN',
                subObject: [
                  {
                    sortNum: 3,
                    content: [
                      '中国大陆驾照原件：中国大陆颁发的驾照原件',
                      '车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 ',
                    ],
                    status: '2',
                    code: 'CDL,DLT',
                    title: '中国大陆驾照原件 + 车行翻译件',
                    type: 22,
                    url: 'https://m.ctrip.com/webapp/carhire/xsd/driverlicenseTranslatePage?isHideNavBar=YES',
                  },
                  {
                    sortNum: 4,
                    content: [
                      '中国大陆驾照原件：中国大陆颁发的驾照原件',
                      '英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件',
                    ],
                    status: '2',
                    code: 'CDL,OET',
                    title: '中国大陆驾照原件 + 英文公证件',
                    type: 22,
                    url: 'https://m.ctrip.com/webapp/carhire/xsd/languagelicensePage?isHideNavBar=YES',
                  },
                ],
                title: '门店支持以下驾照组合(任选其一)',
                type: 5,
              },
            ],
          },
        ],
        title: '驾照组合',
        content: [
          '你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合原件，电子版、照片或非正式驾照（临时驾照、学习驾照、区域限制驾照等）将不被认可',
          '持有不同护照需要驾照组合要求可能不同',
        ],
        type: 1,
      },
      {
        subObject: [
          {
            title: '信用卡要求',
            content: [
              '请保证可用金额足以支付押金',
              '卡片所示姓名（需为全拼）与主驾驶员护照姓名一致。',
              '需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）',
            ],
            type: 6,
            summaryContentObject: [
              {
                contentStyle: 'SupportCreditCard',
                stringObjs: [{ content: '1' }],
              },
              { contentStyle: 'UnionPay', stringObjs: [{ content: '1' }] },
              { contentStyle: 'NeedEmbossed', stringObjs: [{ content: '1' }] },
              { contentStyle: 'NeedChip', stringObjs: [{ content: '1' }] },
              {
                contentStyle: 'SupportMagneticStripe',
                stringObjs: [{ content: '0' }],
              },
              {
                contentStyle: 'UnionPayCurrencyType',
                stringObjs: [{ content: '2' }],
              },
            ],
            urlList: [
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png',
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png',
            ],
          },
          {
            title: '押金说明',
            content: [
              '押金约THB 5,000（约¥1,102），取车时刷取信用卡预授权，还车后14-30天内退还',
            ],
            type: 8,
            table: [
              {
                title: '押金',
                showFree: false,
                description:
                  '押金约THB 5,000（约¥1,102），取车时刷取信用卡预授权，还车后14-30天内退还',
              },
            ],
          },
        ],
        title: '国际信用卡',
        content: ['带芯片，卡号为凸字（摸起来有凹凸感）。'],
        summaryContent: [
          '押金约THB 5,000（约¥1,102），取车时刷取信用卡预授权，还车后14-30天内退还',
        ],
        type: 2,
        summaryTitle: '国际信用卡',
      },
      {
        title: '提车凭证',
        content: [
          '取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。',
        ],
        summaryContent: ['订单确认后，平台为您提供'],
        type: 3,
      },
    ],
    klbVersion: 1,
    osdDetailVersion: 'B',
    carAssistantSummaryV2: [
      {
        title: '取车材料&租车指南',
        type: 1,
        url: 'https://m.ctrip.com/carhire/materialsMustRead?orderId=1128138820081243&h5View=1&locale=zh_cn&orderStatus=CAR_CONFIRMED&vendorId=14088193&countryId=4&isHideNavBar=YES&transparentbar=1&newOsdVoucherV2=1&orderLocale=zh-CN,th-TH,en-US',
      },
      {
        title: '提车凭证',
        type: 5,
        url: 'https://m.ctrip.com/carhire/pickupVoucher2?orderId=1128138820081243&locale=zh-CN&orderLocale=zh-CN,th-TH,en-US&newOsdVoucherV2=1&transparentbar=1&isHideNavBar=YES&orderStatus=CAR_CONFIRMED&countryId=4',
      },
      {
        title: '自驾政策',
        type: 2,
        url: 'https://m.ctrip.com/tangram/ODA0ODA=?ctm_ref=vactang_page_80480&isHideNavBar=YES&apppgid=10650039393&statusBarStyle=1&channelid=237777',
      },
      { title: '取车时记得携带取车材料和提车凭证', type: 6 },
    ],
    flightDelayRule: {
      rules: [
        { title: '若航班延误，门店将不保留车辆，请留取充足的时间取车' },
        { title: '建议您填写航班号，如遇延误请携带机票前往取车' },
      ],
      title: '航班延误保留政策',
      description: '若航班延误，门店将不保留车辆，请留取充足的时间取车',
      delayStatus: 0,
    },
    promptInfos: [
      {
        title: '押金汇率说明',
        type: 12,
        contents: [
          {
            stringObjs: [
              {
                content:
                  '押金所需人民币按照授权免押时的汇率计算，押金实际所需人民币以授权免押时的金额为准',
              },
            ],
          },
        ],
      },
    ],
    sign: '2+NEdD828Z0Wik719zVkNTKXZJs=',
  },
  cancelRuleInfo: {
    cancelReasons: [
      '行程变更/取消',
      '修改订单',
      '重复下单',
      '车不能跨境跨岛',
      '信用卡问题',
      '驾照证件问题',
      '其他网站更便宜',
      '其他',
    ],
    osdCancelRuleInfo: {
      code: 'FreeCancel',
      title: '取消政策',
      complexSubTitle: {
        contentStyle: '1',
        stringObjs: [
          { content: '当地时间7月10日10:00前可免费取消', style: '5' },
          { content: '，', style: '6' },
          { content: '当地时间7月10日10:00前可免费取消', style: '6' },
        ],
      },
      showFree: true,
      type: 300,
      description: '注意：均为当地时间',
      subTitle: '当地时间7月10日10:00前可免费取消',
      items: [
        {
          showFree: true,
          lossFee: 0,
          title: '2025-07-10 10:00前',
          subTitle: '取车时间前',
          description: '可免费取消',
          key: '240971207',
        },
        {
          showFree: true,
          lossFee: 0,
          title: '2025-07-10 10:00后',
          subTitle: '取车时间后',
          description: '可免费取消',
          key: '240971214',
        },
      ],
    },
    cancelTip: 'This booking can be canceled',
    cancelTipColor: 1,
    modifyTip: {
      title: 'Booking cannot be modified',
      desc: 'If you need to make changes, cancel it and rebook',
    },
    ruleList: [
      '- You pick up the car late or drop it off early',
      '- You do not pick up the car within the given timeframe',
      '- You do not provide the required documents when picking up the car',
      "- The main driver's credit card does not have sufficient funds",
    ],
    customerCurrency: 'CNY',
    longTitle: 'In the following circumstances, you will not receive a refund:',
  },
  authType: 0,
  timeInterval: 910.254150390625,
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 2323,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 2323,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1749003903843,
    afterFetch: 1749003906166,
  },
};

// 已确认订单 1128138820081243 18862/queryOrderSecondOpen
export const qrddSecondRes = {
  returnStore: {
    cityName: '曼谷',
    countryName: '泰国',
    longitude: 100.750112,
    localDateTimeDTO: {
      date: '2025年7月11日',
      time: '10:00',
    },
    storeAddress:
      '2nd floor Arrival Hall Terminal , Gate 7 - 8 , 999, Bangna-Trad Rd., KM.15 Moo 10, Rachathewa Distritct, Bangpli, Samutprakan 10540',
    localDateTime: '2025-07-11 10:00:00',
    latitude: 13.689989,
    storeID: 228754,
    serviceType: '0',
    storeTel: '+************/+************',
    storeCode: 'BKK53',
    storeName: 'Suvarnabhumi International Airport',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  baseResponse: {
    code: '0',
    requestId: '70404533-6105-4513-8786-ea0d2af9d5a0',
    cost: 17,
    isSuccess: true,
    returnMsg: 'success',
  },
  pickupStore: {
    cityName: '曼谷',
    countryName: '泰国',
    longitude: 100.750112,
    localDateTimeDTO: {
      date: '2025年7月10日',
      time: '10:00',
    },
    storeAddress:
      '2nd floor Arrival Hall Terminal , Gate 7 - 8 , 999, Bangna-Trad Rd., KM.15 Moo 10, Rachathewa Distritct, Bangpli, Samutprakan 10540',
    localDateTime: '2025-07-10 10:00:00',
    latitude: 13.689989,
    storeID: 228754,
    serviceType: '0',
    storeTel: '+************/+************',
    storeCode: 'BKK53',
    storeName: 'Suvarnabhumi International Airport',
    storeOpenTimeDesc: '{"":"24小时营业"}',
  },
  responseStatus: {
    ack: 'Success',
    errors: [],
    timestamp: '/Date(1749005180109+0800)/',
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '8116546465264905668',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a6e4899-485834-1571061',
      },
    ],
  },
  extendedInfo: {
    sign: '2+NEdD828Z0Wik719zVkNTKXZJs=',
  },
  vehicleInfo: {
    displacement: '',
    passengerNum: 5,
    transmission: 'AT',
    imageUrl: 'https://dimg04.c-ctrip.com/images/0yc1x12000d4jngcs72AD.png',
    doorNum: 4,
    vehicleGroupName: '小型轿车',
    vehicleName: '丰田Yaris',
    vendorVehicleCode: 'TOYOTA YARIS',
  },
  timeInterval: 29.25830078125,
  orderDetailVersion: 'A',
  resBodySize: 2098,
  checkRequestTime: 1749005179848.035,
  checkResponseTime: 1749005179877.2932,
  orderBaseInfo: {
    orderId: 1128138820081243,
    orderStatusDesc: '已确认',
    orderTip: {
      tipContentArray: ['已为您预留车辆，取车时请记得携带身份证件'],
    },
    orderStatusCtrip: 'CAR_CONFIRMED',
  },
  vendorInfo: {
    vendorName: 'Thrifty',
    vendorImageUrl:
      'https://dimg04.c-ctrip.com/images/20p5112000ejfqlt702F3.png',
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 348,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 348,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1749005179847,
    afterFetch: 1749005180195,
  },
};

// 已确认订单的履约接口 1128138820081243 31474/queryOrderFulfill
export const qrddlyRes = [
  {
    data: {
      baseResponse: { code: '200', returnMsg: 'success', isSuccess: true },
      openOrderProof: true,
      nodeHash: '$1$fulfill$itvswcc2GbuJW5kO/2tmG0',
      orderProofDesc: '避免争议',
      orderProofTitle: '拍车况合同单据',
      timeInterval: 304.60986328125,
      fulfillmentNodeList: [
        {
          processList: [
            {
              processCode: 'PickPrepare',
              title: '取车前准备',
              processStatus: 1,
              subProcessList: [
                {
                  processCode: 'PickMaterials',
                  processStatus: 1,
                  operationList: [
                    { operationId: 15, buttonName: '取车材料', enable: true },
                    { operationId: 34, buttonName: '提车凭证', enable: true },
                    { operationId: 35, buttonName: '租车指南', enable: true },
                    { operationId: 37, buttonName: '立即入群', enable: true },
                  ],
                },
              ],
            },
            {
              processCode: 'PickUp',
              title: '07月10日10:00取车',
              processStatus: 1,
              subProcessList: [
                {
                  processCode: 'PickGuide',
                  title: '自行前往门店',
                  processStatus: 1,
                  operationList: [{ operationId: 321, enable: true }],
                  desc: '地址：2nd floor Arrival Hall Terminal , Gate 7 - 8 , 999, Bangna-Trad Rd., KM.15 Moo 10, Rachathewa Distritct, Bangpli, Samutprakan 10540',
                },
              ],
            },
            {
              title: '签署合同，完成取车。',
              processStatus: 1,
              processCode: 'SignPickUpContract',
            },
          ],
          nodeType: 1,
          nodeStatus: 1,
          nodeTitle: '取车',
        },
        {
          processList: [
            {
              processCode: 'Using',
              title: '开始自驾之旅',
              processStatus: 1,
              subProcessList: [
                {
                  processCode: 'UsingNotice',
                  processStatus: 1,
                  operationList: [
                    { operationId: 38, buttonName: '自驾政策', enable: true },
                    { operationId: 35, buttonName: '租车指南', enable: true },
                  ],
                },
                {
                  title: '如需变更还车时间，请您提前联系门店。',
                  processStatus: 1,
                  processCode: 'ContactStore',
                },
              ],
            },
          ],
          nodeType: 2,
          nodeStatus: 1,
          nodeTitle: '用车',
        },
        {
          processList: [
            {
              processCode: 'Return',
              title: '07月11日10:00还车',
              processStatus: 1,
              subProcessList: [
                {
                  processCode: 'ReturnGuide',
                  title: '自行前往门店',
                  processStatus: 1,
                  operationList: [{ operationId: 323, enable: true }],
                  desc: '地址：2nd floor Arrival Hall Terminal , Gate 7 - 8 , 999, Bangna-Trad Rd., KM.15 Moo 10, Rachathewa Distritct, Bangpli, Samutprakan 10540',
                },
              ],
            },
            {
              title: '签署验车单，完成还车。',
              processStatus: 1,
              processCode: 'SignReturnContract',
            },
          ],
          nodeType: 3,
          nodeStatus: 1,
          nodeTitle: '还车',
        },
      ],
      result: { success: true, errorMsg: '200', errorCode: '200' },
      ResponseStatus: {
        Timestamp: '/Date(1749005802362+800)/',
        Ack: 'Success',
        Errors: [],
      },
      checkResponseTime: 1749005802016.814,
      checkRequestTime: 1749005801712.204,
      resBodySize: 2322,
    },
    isSuccess: true,
  },
];

// 退款记录 18862/OSDQueryOrder
export const tkjlRes = {
  ResponseStatus: {
    Timestamp: '2024-10-22 15:13:52',
    Ack: 'Success',
    Errors: [],
    Extension: [
      {
        Id: 'CLOGGING_TRACE_ID',
        Value: '847423705214526695',
      },
      {
        Id: 'RootMessageId',
        Value: '921822-0a73dc7d-480439-501657',
      },
    ],
  },
  orderBaseInfo: {
    orderId: 33726913597,
    uId: 'M3318296560',
    channelType: '235443',
    orderDate: 1723223212000,
    orderLocale: 'zh-CN',
    orderStatus: 3,
    orderStatusDesc: '已完成',
    orderStatusName: '已完成',
    orderStatusCtrip: 'CAR_COMPLETED',
    allStatuses: [],
    allOperations: [
      {
        operationId: 4,
        buttonName: '再次预订',
        enable: true,
      },
      {
        operationId: 3,
        enable: false,
        code: 4,
      },
      {
        operationId: 11,
        buttonName: '续租',
        enable: true,
      },
      {
        operationId: 6,
        buttonName: '报销凭证',
        enable: false,
        url: 'https://m.ctrip.com/webapp/carhire/xsd/osdinvoice?id=33726913597&hideHeader=true',
        contents: [
          {
            description:
              '在线支付的费用可以在订单完成后，进入订详页面自助下载相关报销凭证；到店支付的费用，请在门店向供应商索要相关Receipt凭证。',
          },
        ],
      },
    ],
    orderTip: {
      tipType: 11,
      tipContent: '',
      tipContentArray: ['行程已结束，感谢您的使用'],
      warnType: 0,
    },
    payMode: 3,
    successSafeRentAuth: false,
    extOperation: [],
  },
  osdDeductionList: [
    {
      occurrenceTime: '2024-10-24 10:46:38',
      feeInfo: {
        estimateTitle: '预扣费用',
        actualTitle: '核实后实扣',
        feeInfoDetails: [
          {
            expenseName: '车损',
            expenseAmountStr: 'US$ 1.00，约¥7.13',
            actualAmountStr: 'US$ 0.50，约¥3.56',
          },
        ],
        feeContrast: {
          desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
          title: '费用汇率说明',
        },
        estimateTotalAmountStr: 'US$ 1.00，约¥7.13',
        actualTotalAmountStr: 'US$ 0.50，约¥3.56',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2024-10-24 10:46扣费¥ 7.13',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '2024-10-24 14:57确认实际损失¥ 3.56',
          status: 2,
        },
        {
          name: '完成',
          desc: '2024-10-24 14:57退还预扣车损金额¥ 3.57',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc4s12000bikic1kBDF8.png',
            'https://dimg04.c-ctrip.com/images/0yc3s12000bijrsa39AE8.png',
          ],
          commitTime: '2024-10-24 10:46:38',
          firstCommit: true,
        },
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc4s12000bikic1kBDF8.png',
            'https://dimg04.c-ctrip.com/images/0yc3s12000bijrsa39AE8.png',
          ],
          commitTime: '2024-10-24 14:57:00',
          firstCommit: false,
        },
      ],
      id: 38167571,
      expenseTitle: '实扣US$ 0.50，约¥3.56',
      deductionTypeDesc: '车损',
    },
    {
      occurrenceTime: '2024-10-24 14:56:25',
      feeInfo: {
        actualTitle: '核实后实扣',
        feeInfoDetails: [
          {
            expenseName: '违章',
            actualAmountStr: 'US$ 1.00，约¥7.12',
          },
        ],
        feeContrast: {
          desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
          title: '费用汇率说明',
        },
        actualTotalAmountStr: 'US$ 1.00，约¥7.12',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '门店发起预扣款',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '2024-10-24 14:56确认实际损失¥ 7.12',
          status: 2,
        },
        {
          name: '完成',
          desc: '2024-10-24 14:56',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc5u12000gaih1rs0D60.png',
          ],
          commitTime: '2024-10-24 14:56:25',
          firstCommit: true,
        },
      ],
      id: 38188452,
      expenseTitle: '实扣US$ 1.00，约¥7.12',
      deductionTypeDesc: '违章',
    },
    {
      occurrenceTime: '2024-10-24 14:56:29',
      feeInfo: {
        estimateTitle: '预扣费用',
        feeInfoDetails: [
          {
            expenseName: '车损',
            expenseAmountStr: 'US$ 1.00，约¥7.12',
          },
        ],
        feeContrast: {
          desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
          title: '费用汇率说明',
        },
        estimateTotalAmountStr: 'US$ 1.00，约¥7.12',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '门店已撤销扣款',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆定损及维修情况进行跟踪核实',
          status: 0,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 0,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc4s12000bikic1kBDF8.png',
          ],
          commitTime: '2024-10-24 14:56:29',
          firstCommit: true,
        },
      ],
      id: 38188473,
      expenseTitle: '预扣US$ 1.00，约¥7.12',
      deductionTypeDesc: '车损',
    },
  ],
  continuePayInfo: {
    needContinuePay: false,
  },
  orderPriceInfo: {
    packageType: 0,
    currentTotalPrice: 233,
    currentCurrencyCode: 'CNY',
    localTotalPrice: 1150,
    localCurrencyCode: 'THB',
    payMode: 3,
    payModeDesc: '在线支付',
    prepayPrice: {
      title: '',
      totalPrice: 30,
      currencyCode: 'CNY',
    },
    localPrice: {
      title: '',
      totalPrice: 1000,
      currencyCode: 'THB',
    },
    prepayPriceDetails: [],
    localPriceDetails: [
      {
        title: 'shark|common_carHireFee',
        totalPrice: 1000,
        currencyCode: 'THB',
      },
    ],
    priceDetails: [
      {
        title: 'shark|common_carHireFee',
        totalPrice: 234,
        currencyCode: 'CNY',
      },
    ],
    payAmount: 30,
    couponAmount: 0,
    coupons: [],
  },
  vehicleInfo: {
    vehicleName: '本田 Jazz',
    special: true,
    passengerNum: 5,
    luggageNum: 1,
    hasAC: false,
    transmission: 'AT',
    vehicleGroupName: '小型轿车',
    vendorVehicleCode: '',
    imageUrl: 'https://dimg04.c-ctrip.com/images/0yc4w12000dozw6n65549.png',
    vehicleDisplacement: '',
    displacement: '',
    doorNum: 4,
    fuelType: '3',
    vehicleGroupId: 1,
    fourDrive: false,
  },
  vendorInfo: {
    vendorName: 'Meet Car Rental',
    vendorImageUrl:
      'https://dimg04.c-ctrip.com/images/20p4012000ejfr4dkE80B.png',
    confirmDate: 1723220253000,
    confirmDateStr: '2024-08-10 00:17',
    platformCode: '',
    platformName: 'บริษัท มีท คาร์ แอนด์ มอเตอร์ไซเคิล เรนทัล จำกัด',
    bizVendorCode: '14487',
    commentInfo: {
      vendorGoodType: 1,
      exposedScore: 5,
      topScore: 5,
      level: '超棒',
      commentCount: 7,
      hasComment: 1,
      link: '/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=426140&calabiVehicleId=64079&vehicleName=null&productCategoryId=35&isHideNavBar=YES',
    },
    broker: false,
  },
  pickupStore: {
    localDateTime: '2024-08-11 14:00:00',
    storeName: 'CNX, Airport',
    storeCode: 'CNX, Airport',
    storeAddress:
      '39 Soi 4, Tambon Su Thep, Amphoe Mueang Chiang Mai, Chang Wat Chiang Mai 50200, Thailand',
    longitude: 98.960472,
    latitude: 18.777288,
    storeGuide:
      '工作人员会送车至Gate 2的对面的停车场。联系电话：(+66)-653812310',
    storeLocation: '门店位于清迈国际机场到达大厅',
    storeWay: '抵达后联系工作人员前来接送',
    storeTel: '66-653812310',
    storeOpenTimeDesc: '{"":"09:00 - 22:00"}',
    cityName: '清迈',
    provinceName: '清迈府',
    countryName: '泰国',
    userSearchLocation: '清迈国际机场',
    storeID: 426140,
    location: {
      locationType: 1,
      locationName: '清迈国际机场',
      locationCode: 'CNX',
      continent: {
        id: 4,
        name: '泰国',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      province: {
        id: 10218,
        name: '清迈府',
      },
      city: {
        id: 623,
        name: '清迈',
      },
      poiInfo: {
        latitude: 18.767749,
        longitude: 98.964009,
        type: 1,
      },
    },
    disclaimer: 'The above information is provided by the branch',
    localDateTimeDTO: {
      date: '2024年8月11日',
      time: '14:00',
    },
    countryId: 4,
  },
  returnStore: {
    localDateTime: '2024-08-12 14:00:00',
    storeName: 'CNX, Airport',
    storeCode: 'CNX, Airport',
    storeAddress:
      '39 Soi 4, Tambon Su Thep, Amphoe Mueang Chiang Mai, Chang Wat Chiang Mai 50200, Thailand',
    longitude: 98.960472,
    latitude: 18.777288,
    storeGuide: '请在还车前联系工作人员，联系电话：(+66)-653812310',
    storeLocation: '门店位于清迈国际机场到达大厅',
    storeWay: '抵达后联系工作人员前来接送',
    storeTel: '66-653812310',
    storeOpenTimeDesc: '{"":"09:00 - 22:00"}',
    cityName: '清迈',
    provinceName: '清迈府',
    countryName: '泰国',
    userSearchLocation: '清迈国际机场',
    storeID: 426140,
    location: {
      locationType: 1,
      locationName: '清迈国际机场',
      locationCode: 'CNX',
      continent: {
        id: 4,
        name: '泰国',
      },
      country: {
        id: 4,
        name: '泰国',
      },
      province: {
        id: 10218,
        name: '清迈府',
      },
      city: {
        id: 623,
        name: '清迈',
      },
      poiInfo: {
        latitude: 18.767749,
        longitude: 98.964009,
        type: 1,
      },
    },
    disclaimer: 'The above information is provided by the branch',
    localDateTimeDTO: {
      date: '2024年8月12日',
      time: '14:00',
    },
    countryId: 4,
  },
  driverInfo: {
    name: 'aa70e0a164b301dc1e0f9809fb0887f5f1195165bf9e5f9135df91662f3c4b22',
    age: '25',
    email: 'd307419162d04efb58f16d8edfacdb989237032f6440bc0c0b7c5a15abf76ddf',
    telphone:
      '78468f3fe7c399f4edc37a5d00e75988261001b98cb660db3f1ae943d7ee07a6',
    areaCode: '66',
    flightNo: '',
    decryptTelphone: '802395504',
    decryptMail: '<EMAIL>',
    lastName:
      'e752c04ca6953e1e1a15683a708cc71af188747cae49e3f17e5b7973c8f83406',
    firstName:
      '560d7156a8db223c177e0e0f3d70eb18183badd2846d7f5ca77f7681b08a43dc',
  },
  extraInfos: [],
  cancelRuleInfo: {
    cancelTip: 'Car picked up - booking can no longer be canceled',
    longTitle: 'In the following circumstances, you will not receive a refund:',
    ruleList: [
      '- You pick up the car late or drop it off early',
      '- You do not pick up the car within the given timeframe',
      '- You do not provide the required documents when picking up the car',
      "- The main driver's credit card does not have sufficient funds",
    ],
    cancelReasons: [
      '行程变更/取消',
      '修改订单',
      '重复下单',
      '车不能跨境跨岛',
      '信用卡问题',
      '驾照证件问题',
      '其他网站更便宜',
      '其他',
    ],
    cancelTipColor: 0,
    modifyTip: {
      title: 'Booking cannot be modified',
      desc: 'If you need to make changes, cancel it and rebook',
    },
    osdCancelRuleInfo: {
      title: '取消政策',
      subTitle: '当地时间2024-08-10 14:00前可免费取消',
      complexSubTitle: {
        contentStyle: '1',
        stringObjs: [
          {
            content: '当地时间2024-08-10 14:00前可免费取消',
            style: '5',
          },
          {
            content: '，',
            style: '6',
          },
          {
            content: '当地时间2024-08-10 14:00前可免费取消',
            style: '6',
          },
        ],
      },
      code: 'FreeCancel',
      type: 300,
      items: [
        {
          title: '2024-08-10 14:00前',
          subTitle: '取车前24小时前',
          description: '可免费取消',
          showFree: true,
          key: '136949744',
          lossFee: 0,
        },
        {
          title: '2024-08-10 14:00后',
          subTitle: '取车前24小时后',
          description: '取消将收取首日租金作为违约金',
          showFree: false,
          key: '136949751',
          lossFee: 30,
        },
      ],
      description: '注意：均为当地时间',
      showFree: true,
    },
    customerCurrency: 'CNY',
  },
  refundProgressList: [
    {
      currency: 'CNY',
      refundAmount: 20.71,
      billStatus: 'SUCCESS',
      remark: '车损退款',
      dealStatus: 1,
      createTime: '2024-10-22 14:12:33',
      dealTime: '2024-10-23 23:59:59',
      refundPeriodKey: '',
      paymentWayID: 'AlipayAuth',
      paymentWayName: '支付宝',
      clientSubmitCardNoTime: '',
    },
    {
      currency: 'CNY',
      refundAmount: 277.75,
      billStatus: 'SUCCESS',
      remark: '车损退款',
      dealStatus: 1,
      createTime: '2024-10-22 14:12:04',
      dealTime: '2024-10-23 23:59:59',
      refundPeriodKey: '',
      paymentWayID: 'AlipayAuth',
      paymentWayName: '支付宝',
      clientSubmitCardNoTime: '',
    },
    {
      currency: 'CNY',
      refundAmount: 207.06,
      billStatus: 'SUCCESS',
      remark: '车损退款',
      dealStatus: 1,
      createTime: '2024-10-22 14:12:03',
      dealTime: '2024-10-23 23:59:59',
      refundPeriodKey: '',
      paymentWayID: 'AlipayAuth',
      paymentWayName: '支付宝',
      clientSubmitCardNoTime: '',
    },
  ],
  baseResponse: {
    isSuccess: true,
    code: '200',
    returnMsg: 'success',
    requestId: '0bdef164-799e-4e2b-9a41-db7b59db4e9f',
    cost: 449,
  },
  packageInfos: [
    {
      insPackageId: 5,
      isDefault: true,
      packageName: '基础套餐',
      currencyCode: 'THB',
      defaultBomCode: '',
      defaultPackageId: 219136,
      guaranteeDegree: 0,
      naked: false,
      lowestDailyPrice: 0,
      gapPrice: 0,
      stepPrice: 0,
    },
  ],
  productDetails: [
    {
      insPackageId: 5,
      insuranceItems: [
        {
          productId: 219136,
          title: '第三者保障',
          description: '保障第三方车辆或人员伤害损失',
          code: 'TPL',
          type: 7,
          name: '第三者保障',
          isInclude: true,
          isFromCtrip: false,
          insuranceDetail: [
            {
              packageId: 219136,
              currencyCode: 'THB',
              minExcess: 5000,
              maxExcess: 5000,
              excessShortDesc: '起赔额THB 5000',
              minCoverage: 10000000,
              maxCoverage: 10000000,
              coverageWithPlatformInsurance: '起赔额THB 5000',
              coverageWithoutPlatformInsuranceV2: '起赔额THB 5000',
            },
          ],
          converageExplain: {
            title: '承保范围',
            content: [
              '若开车发生事故对第三方车辆或人员造成伤害，起赔额以上保额以内的部分将由车行的保险赔付。一般第三者保障没有起赔额（如有标注起赔额除外）但有最高保额。\n*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况',
                ],
              },
              {
                title: '还车时向车行提交理赔',
                content: [
                  '在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          excessTable: {
            title: '起赔额THB 5000',
            subObject: [
              {
                title: '车行承担',
                content: ['THB 5000以上部分'],
              },
              {
                title: '客户或承租方承担',
                content: ['THB 5000及以下部分（据实承担）'],
              },
            ],
          },
        },
        {
          productId: 219136,
          title: '车辆碰撞保障',
          description: '保障车辆碰撞损失',
          code: 'CDW',
          type: 7,
          name: '车辆碰撞保障',
          isInclude: true,
          isFromCtrip: false,
          insuranceDetail: [
            {
              packageId: 219136,
              currencyCode: 'THB',
              minExcess: 5000,
              maxExcess: 5000,
              excessShortDesc: '起赔额THB 5000',
              minCoverage: 500000,
              maxCoverage: 500000,
              coverageWithPlatformInsurance: '起赔额THB 5000',
              coverageWithoutPlatformInsuranceV2: '起赔额THB 5000',
            },
          ],
          converageExplain: {
            title: '承保范围',
            content: [
              '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准',
            ],
          },
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '发生事故后报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          excessTable: {
            title: '起赔额THB 5000',
            subObject: [
              {
                title: '车行承担',
                content: ['THB 5000以上部分'],
              },
              {
                title: '客户或承租方承担',
                content: ['THB 5000及以下部分（据实承担）'],
              },
            ],
          },
        },
      ],
      productInfoList: [
        {
          minPackageItmes: [
            {
              title: '手机支架',
              type: 0,
              code: '',
              description: '',
            },
            {
              title: '送车上门',
              type: 0,
              code: '',
              description: '送车上门服务',
            },
            {
              title: '不限里程',
              type: 2,
              code: 'ULM',
              description: '租期内没有公里数限制。\n',
            },
            {
              title: '1名额外驾驶员',
              type: 3,
              code: 'ADD1',
              description:
                '1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
            },
            {
              title: '满油取还',
              type: 4,
              code: 'FRFB',
              description:
                '取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。',
            },
          ],
        },
      ],
      claimsProcess: [
        {
          subObject: [
            {
              title: '报警并联系门店',
              content: [
                '请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。',
              ],
              type: 1,
            },
            {
              title: '还车时向车行提交事故材料',
              content: [
                '用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。',
              ],
              type: 2,
            },
          ],
        },
      ],
      extraDesc: [],
      briefInsuranceItems: [
        {
          description: '保障第三方车辆或人员伤害损失',
          code: 'TPL',
          type: 7,
          name: '第三者保障',
          isInclude: true,
          isFromCtrip: false,
          insuranceDetail: [
            {
              packageId: 219136,
              currencyCode: 'THB',
              minExcess: 5000,
              maxExcess: 5000,
              excessShortDesc: '起赔额THB 5000',
              minCoverage: 10000000,
              maxCoverage: 10000000,
              coverageWithPlatformInsurance: '起赔额THB 5000',
              coverageWithoutPlatformInsuranceV2: '起赔额THB 5000',
            },
          ],
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况',
                ],
              },
              {
                title: '还车时向车行提交理赔',
                content: [
                  '在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          excessTable: {
            title: '起赔额THB 5000',
            subObject: [
              {
                title: '车行承担',
                content: ['THB 5000以上部分'],
              },
              {
                title: '客户或承租方承担',
                content: ['THB 5000及以下部分（据实承担）'],
              },
            ],
          },
        },
        {
          description: '保障车辆碰撞损失',
          code: 'CDW',
          type: 7,
          name: '车辆碰撞保障',
          isInclude: true,
          isFromCtrip: false,
          insuranceDetail: [
            {
              packageId: 219136,
              currencyCode: 'THB',
              minExcess: 5000,
              maxExcess: 5000,
              excessShortDesc: '起赔额THB 5000',
              minCoverage: 500000,
              maxCoverage: 500000,
              coverageWithPlatformInsurance: '起赔额THB 5000',
              coverageWithoutPlatformInsuranceV2: '起赔额THB 5000',
            },
          ],
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '发生事故后报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          excessTable: {
            title: '起赔额THB 5000',
            subObject: [
              {
                title: '车行承担',
                content: ['THB 5000以上部分'],
              },
              {
                title: '客户或承租方承担',
                content: ['THB 5000及以下部分（据实承担）'],
              },
            ],
          },
        },
      ],
    },
  ],
  freeDeposit: {
    depositStatus: 2,
    depositExplainV2: [
      {
        explain: '已解除信用授权',
        color: 1,
        badDebt: false,
      },
    ],
    freeDepositType: 100,
    freeDepositWay: 101,
    depositItemName: '押金明细',
    freeDepositProgress: {
      title: '授权及解冻进度',
      subTitle: '已扣费：¥ 330.04',
      progressInfos: [
        {
          mainText: '授权芝麻信用免押',
          subText: [
            {
              subDesc: '2024-08-10 01:06',
            },
          ],
          type: '2',
          name: '授权',
          level: '10',
          color: '1',
        },
        {
          mainText: '车损扣款实扣¥ 136.38',
          subText: [
            {
              subDesc: '2024-08-30 00:44',
            },
          ],
          links: [
            {
              type: '1',
              desc: '查看扣费详情',
            },
          ],
          type: '3',
          level: '20',
          color: '1',
          deductId: 38167571,
        },
        {
          mainText: '额度暂无法解冻',
          subText: [
            {
              subDesc: '您尚未支付车损费用，请保证账户中有足够金额',
            },
          ],
          type: '8',
          name: '解除',
          level: '10',
          color: '2',
        },
      ],
      notice:
        '如产生车损和违章，建议与门店现场结算，并保留相关凭证，后续将不会扣除您的押金',
    },
    freeDepositTitle: '已扣费：¥ 330.04',
    depositItemTitle: {
      title: '此单已享免押金',
      subTitle: 'THB 5000.00，约¥1017.00',
      feeContrast: {
        title: '押金汇率说明',
        desc: '押金所需人民币按照授权免押时的汇率计算，押金实际所需人民币以授权免押时的金额为准',
      },
    },
  },
  extendedInfo: {
    flightDelayRule: {
      title: '航班延误保留政策',
      description: '航班延误保留至24:00（当天）',
      rules: [
        {
          title: '航班延误保留至24:00（当天）',
        },
        {
          title: '建议您填写航班号，如遇延误请携带机票前往取车',
        },
      ],
      delayStatus: 0,
    },
    klbVersion: 1,
    pickUpMaterials: [
      {
        title: '取车要求',
        content: ['驾驶员年龄21-65周岁'],
        summaryContent: [],
        type: 20,
        summaryContentObject: [
          {
            contentStyle: 'age',
            stringObjs: [
              {
                content: '21-65周岁',
              },
            ],
          },
        ],
        summaryTitle: '驾驶员需持有本人名下4项材料取车：',
      },
      {
        title: '护照原件',
        subTitle: '',
        type: 0,
        subObject: [
          {
            title: '中国大陆护照',
            subTitle: '中国大陆护照原件',
            code: 'CN',
            subObject: [
              {
                title: '护照原件',
                content: [
                  '主驾驶员和其他额外驾驶员需提供他们名下的护照原件',
                  '护照签发地需与驾照发证国家/地区一致',
                ],
                type: 21,
              },
            ],
          },
        ],
      },
      {
        title: '驾照组合',
        content: [
          '你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合原件，电子版、照片或非正式驾照（临时驾照、学习驾照、区域限制驾照等）将不被认可',
          '持有不同护照需要驾照组合要求可能不同',
        ],
        type: 1,
        subObject: [
          {
            type: 5,
            subObject: [
              {
                title: '门店支持以下驾照组合',
                type: 5,
                code: 'CN',
                subObject: [
                  {
                    title: '中国大陆驾照原件 + 驾照国际翻译认证件',
                    content: [
                      '中国大陆驾照原件：中国大陆颁发的驾照原件',
                      '驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可至携程APP境外租车首页办理。',
                    ],
                    type: 22,
                    code: 'CDL,IDL',
                    sortNum: 2,
                    optimalType: '1',
                    status: '1',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        title: '国际信用卡',
        content: ['带芯片，卡号为凸字（摸起来有凹凸感）。'],
        summaryContent: [
          '押金约THB 5,000（约¥1,063），到店刷取押金预授权，还车后30-60天内退还',
        ],
        type: 2,
        subObject: [
          {
            title: '信用卡要求',
            content: [
              '请保证可用金额足以支付押金',
              '卡片所示姓名（需为全拼）与主驾驶员护照姓名一致。',
              '需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）',
            ],
            type: 6,
            urlList: [
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
            ],
            summaryContentObject: [
              {
                contentStyle: 'SupportCreditCard',
                stringObjs: [
                  {
                    content: '1',
                  },
                ],
              },
              {
                contentStyle: 'UnionPay',
                stringObjs: [
                  {
                    content: '1',
                  },
                ],
              },
              {
                contentStyle: 'NeedEmbossed',
                stringObjs: [
                  {
                    content: '1',
                  },
                ],
              },
              {
                contentStyle: 'NeedChip',
                stringObjs: [
                  {
                    content: '1',
                  },
                ],
              },
              {
                contentStyle: 'SupportMagneticStripe',
                stringObjs: [
                  {
                    content: '0',
                  },
                ],
              },
              {
                contentStyle: 'UnionPayCurrencyType',
                stringObjs: [
                  {
                    content: '2',
                  },
                ],
              },
            ],
          },
          {
            title: '押金说明',
            content: [
              '押金约THB 5,000（约¥1,063），到店刷取押金预授权，还车后30-60天内退还',
            ],
            type: 8,
            table: [
              {
                title: '押金',
                description: '此单已享免押金',
                showFree: true,
              },
            ],
          },
        ],
        summaryTitle: '国际信用卡',
      },
      {
        title: '提车凭证',
        content: [
          '取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。',
        ],
        summaryContent: ['订单确认后，平台为您提供'],
        type: 3,
      },
    ],
    ctripInsuranceVersion: 'B',
    carAssistantSummary: [
      {
        content: '取车材料',
      },
      {
        content: '办理驾照翻译件',
      },
    ],
    osdDetailVersion: 'B',
    crossLocationsPolicy: {
      crossLocationsInfos: [
        {
          crossType: 3,
          crossTypeName: '跨境政策',
          summaryPolicies: [
            '由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨境，建议您更换其它租车公司或车型组。',
          ],
        },
        {
          crossType: 1,
          crossTypeName: '跨岛政策',
          summaryPolicies: [],
          summaryTitle: '允许跨岛',
        },
        {
          crossType: 2,
          crossTypeName: '跨州政策',
          summaryPolicies: [],
          summaryTitle: '允许跨州',
        },
      ],
      notes: [
        '若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。除特别说明的情况外，“跨境费用”为前往单个国家所需的费用。',
      ],
      title: '旅行限制',
    },
    sign: '+kthlSeusBhBYiQImuRoIwU7L5U=',
    promptInfos: [
      {
        title: '押金汇率说明',
        type: 12,
        contents: [
          {
            stringObjs: [
              {
                content:
                  '押金所需人民币按照授权免押时的汇率计算，押金实际所需人民币以授权免押时的金额为准',
              },
            ],
          },
        ],
      },
    ],
    carAssistantSummaryV2: [
      {
        title: '取车材料&租车指南',
        type: 1,
        url: 'https://m.ctrip.com/carhire/materialsMustRead?orderId=33726913597&h5View=1&locale=zh_cn&orderStatus=CAR_COMPLETED&vendorId=14487&countryId=4&isHideNavBar=YES&transparentbar=1',
      },
      {
        title: '提车凭证',
        type: 5,
        url: 'https://m.ctrip.com/carhire/pickupVoucher?orderId=33726913597&h5View=1&locale=zh_cn&isHideNavBar=YES&transparentbar=1&orderStatus=CAR_COMPLETED&countryId=4',
      },
      {
        title: '自驾政策',
        type: 2,
        url: 'https://m.ctrip.com/tangram/ODA0ODA=?ctm_ref=vactang_page_80480&isHideNavBar=YES&apppgid=10650039393&statusBarStyle=1&channelid=237777',
      },
      {
        title: '取车时记得携带取车材料和提车凭证',
        type: 6,
      },
    ],
    attr: {
      ctripInsuranceVersion: 'B',
      isVehicle2: '1',
      osdConfirmNew: 'true',
      isContractTemplates: 'true',
      osdDetailVersion: 'A',
      voucherVersion: 'B',
    },
  },
  modifyInfoDto: {},
  insuranceAndXProductDesc: [
    {
      type: 1,
      desc: [
        '取车时，店员可能会向您推销额外保险或收费项目，请根据实际需要选购。在签署合同时，请仔细核对是否有额外收费。如被强制要求购买，请您不要签字，当场联系携程客服协助解决。',
      ],
    },
  ],
  authType: 0,
  useCalabiId: true,
};

// graphql接口
export const graphqlRes = {
  getcustomerservice: {
    baseResponse: {
      code: '200',
      message: '查询成功',
      requestId: '',
      type: 0,
      isSuccess: true,
      showMessage: '',
    },
    url: 'ctrip://wireless/chat_customerServiceChat?isPreSale=0&sceneCode=0&bizType=1339&pageId=222053&ext=eyJhaVBhcmFtIjp7Im9yZGVyaWQiOiIxMTI4MTY4OTQ1MDYyMTE5In0sIm9yZGVySW5mbyI6eyJjdHlwZSI6Ik9SRCIsImNpZCI6IjExMjgxNjg5NDUwNjIxMTkiLCJkZXNjIjoi5rW35aSW6Ieq6am+IiwidGl0bGUiOiLlooPlpJbnp5/ovabnrqHlrrYiLCJhbW91bnQiOiIxMTgyLjAwIiwiYnUiOiJyZW50YWwifX0=',
    ResponseStatus: {
      Timestamp: '2025-05-30 14:48:25',
      Ack: 'Success',
      Errors: [],
    },
  },
  queryPriceInfo: {
    baseResponse: {
      code: '200',
      requestId: '57a55c3d-4e9e-4214-b395-435803b85d63',
      cost: 427,
      isSuccess: true,
      returnMsg: 'success',
    },
    feeDetailInfo: {
      notIncludeCharges: {},
      equipmentInfos: [],
      activityInfo: {},
      chargesInfos: [
        { title: '车辆租金+基础套餐' },
        {
          code: 'Car',
          title: '基础租车费用',
          size: '约¥1,182×1天',
          currencyCode: 'CNY',
          currentTotalPrice: 1182,
          description: '',
          type: 8,
          payMode: 2,
          items: [
            {
              code: 'Car',
              title: '基础租车费用',
              descList: [
                '满油取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 基本租车费用, 不限里程',
              ],
              description:
                '满油取还, 税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税, 基本租车费用, 不限里程',
              type: 8,
            },
            { title: '车辆碰撞保障', type: 1, code: 'Insurance' },
            { title: '车辆盗抢保障', type: 1, code: 'Insurance' },
            { title: '第三者责任保障', type: 1, code: 'Insurance' },
          ],
        },
      ],
      couponInfos: [],
      rentalTerm: 1,
      chargesSummary: {
        code: 'Summary',
        title: '全额',
        currencyCode: 'CNY',
        notices: [],
        currentTotalPrice: 1182,
        type: 103,
        items: [
          {
            code: '1',
            currentTotalPrice: 1182,
            title: '在线预付',
            currencyCode: 'CNY',
          },
        ],
      },
    },
    priceDesc: {
      possibleChangeList: [
        { title: '里程政策', description: '该车辆没有里程限制。' },
        { title: '能源政策', description: '满油/电取还' },
        { title: '年龄要求', description: '驾驶员年龄要求：25-65周岁' },
        {
          title: '额外驾驶员',
          description:
            '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
        },
        {
          title: '营业时间外取还车',
          description:
            '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
        },
        {
          title: '当地费用',
          description:
            '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
        },
      ],
    },
    responseStatus: {
      ack: 'Success',
      errors: [],
      timestamp: '/Date(1748587706030+0800)/',
    },
  },
  querySimilarVehicle: {
    introduce: {
      title: '同组车型',
      description:
        '同组车型是指按照车型功能、空间大小、座位数和车门数划分的紧凑型轿车、中型轿车、小型SUV等等这些分类。您在网上预订时选择的车并不能保证到店就一定取到一模一样的，可能会得到同组车型中的其他车。',
    },
    carProtection: {
      title: '取车保障',
      description:
        '在库存充足的情况下，车行会尽量提供您在预订时所选的车型\n如果这款车暂时没有库存，车行便会为您提供同车型组内的其他车型\n若因门店原因需要调整车型，不额外收取车型变更费用',
    },
    cases: [
      {
        vehicleGroupName: '车型组',
        representativeVehicleName: '代表车型',
        vehicleGroupItems: '车组包含',
        vehicleGroupCode: 'default',
      },
      {
        vehicleGroupName: '紧凑型轿车',
        representativeVehicleName: '福特嘉年华',
        vehicleGroupItems: '丰田卡罗拉、现代雅绅特',
        vehicleGroupCode: 'D',
      },
      {
        vehicleGroupName: '中大型轿车',
        representativeVehicleName: '丰田凯美瑞',
        vehicleGroupItems: '雪佛兰迈锐宝、大众帕萨特',
        vehicleGroupCode: 'S',
      },
      {
        vehicleGroupName: '中大型SUV',
        representativeVehicleName: '大众途观',
        vehicleGroupItems: '丰田RAV4、吉普指南者',
        vehicleGroupCode: 'R',
      },
    ],
    baseResponse: {
      code: '200',
      requestId: '57a55c3d-4e9e-4214-b395-435803b85d63',
      cost: 43,
      isSuccess: true,
      returnMsg: 'success',
    },
    responseStatus: {
      ack: 'Success',
      errors: [],
      timestamp: '/Date(1748587705648+0800)/',
    },
    vedio: 'https://ak-v.tripcdn.com/videos/9D0g2r000001j0rrk1E0A.mp4',
    cover: 'https://ak-d.tripcdn.com/images/1tg1u12000dwdf1uw1DCD.png',
  },
  getEasyLifeTagInfo: {
    baseResponse: {
      code: '200',
      requestId: 'c2f51529-7141-4a48-8050-5d43c601e18c',
      cost: 8,
      isSuccess: true,
      returnMsg: 'success',
    },
    titleAbstract: [
      { title: '免费取消', sortNum: 1 },
      { title: '满油取车', sortNum: 2 },
      { title: '免加油服务费', sortNum: 3 },
    ],
    ResponseStatus: {
      Timestamp: '/Date(1748587705604+0800)/',
      Ack: 'Success',
      Errors: [],
    },
    easyLifeTag: [
      {
        sortNum: 1,
        showLayer: 1,
        title: '免费取消',
        titleExtra: '(国定节假日除外)',
        type: 0,
        description: '订单取车时间前可免费取消',
        subTitle: '*国定节假日以页面披露为准',
      },
      {
        sortNum: 2,
        showLayer: 0,
        title: '安心保障',
        titleExtra: '(需加购优享服务)',
        type: 1,
        description:
          '付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧',
        subTitle: '*覆盖损失范围以预订页面内披露为准',
      },
      {
        sortNum: 10,
        title: '满油取车',
        subTitle: '',
        type: 0,
        description: '保障取车时油量满',
        showLayer: 0,
      },
      {
        sortNum: 99,
        title: '免加油服务费/充电服务费',
        subTitle: '',
        type: 0,
        description:
          '还车时，若油量/电量少于取车油量/电量，无需支付加油服务费/充电服务费，所产生的油量/电量差价仍需支付',
      },
    ],
  },
  queryAdditionPayment: {
    baseResponse: {
      code: '200',
      requestId: '57a55c3d-4e9e-4214-b395-435803b85d63',
      cost: 133,
      isSuccess: true,
      returnMsg: 'success',
    },
    responseStatus: {
      ack: 'Success',
      errors: [],
      timestamp: '/Date(1748587705739+0800)/',
    },
    additionalPaymentList: [
      {
        orderId: 1128168945062119,
        amount: 130,
        reason: '订单取消违约金',
        payStatus: 0,
        toPayCount: 130,
        toPayAmount: 130,
        additionalPaymentId: 47629,
        bizScene: 4,
      },
    ],
  },
};

// 押金扣款接口 18862/queryDeduction
export const yjkkRes = {
  responseStatus: {
    timestamp: '/Date(1730339974408+0800)/',
    ack: 'Success',
    errors: [],
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '166693167797091064',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a7098fd-480649-1933294',
      },
    ],
  },
  baseResponse: {
    isSuccess: true,
    code: '0',
    returnMsg: 'success',
    requestId: '8d050d22-2a64-43f2-b8d6-39cb0f58cf59',
    cost: 155,
  },
  oSDDeductionList: [
    {
      occurrenceTime: '2024-10-24 10:46:38',
      feeInfo: {
        estimateTitle: '预扣费用',
        actualTitle: '核实后实扣',
        feeInfoDetails: [
          {
            expenseName: '车损',
            expenseAmountStr: 'US$ 1.00，约¥7.13',
            actualAmountStr: 'US$ 0.50，约¥3.56',
          },
        ],
        feeContrast: {
          desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
          title: '费用汇率说明',
        },
        estimateTotalAmountStr: 'US$ 1.00，约¥7.13',
        actualTotalAmountStr: 'US$ 0.50，约¥3.56',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2024-10-24 10:46扣费¥ 7.13',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '2024-10-24 14:57确认实际损失¥ 3.56',
          status: 2,
        },
        {
          name: '完成',
          desc: '2024-10-24 14:57退还预扣车损金额¥ 3.57',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc4s12000bikic1kBDF8.png',
            'https://dimg04.c-ctrip.com/images/0yc3s12000bijrsa39AE8.png',
          ],
          commitTime: '2024-10-24 10:46:38',
          firstCommit: true,
        },
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc4s12000bikic1kBDF8.png',
            'https://dimg04.c-ctrip.com/images/0yc3s12000bijrsa39AE8.png',
          ],
          commitTime: '2024-10-24 14:57:00',
          firstCommit: false,
        },
      ],
      id: 38167571,
      expenseTitle: '实扣US$ 0.50，约¥3.56',
      deductionTypeDesc: '车损',
    },
    {
      occurrenceTime: '2024-10-24 14:56:25',
      feeInfo: {
        actualTitle: '核实后实扣',
        feeInfoDetails: [
          {
            expenseName: '违章',
            actualAmountStr: 'US$ 1.00，约¥7.12',
          },
        ],
        feeContrast: {
          desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
          title: '费用汇率说明',
        },
        actualTotalAmountStr: 'US$ 1.00，约¥7.12',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '门店发起预扣款',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '2024-10-24 14:56确认实际损失¥ 7.12',
          status: 2,
        },
        {
          name: '完成',
          desc: '2024-10-24 14:56',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc5u12000gaih1rs0D60.png',
          ],
          commitTime: '2024-10-24 14:56:25',
          firstCommit: true,
        },
      ],
      id: 38188452,
      expenseTitle: '实扣US$ 1.00，约¥7.12',
      deductionTypeDesc: '违章',
    },
    {
      occurrenceTime: '2024-10-24 14:56:29',
      feeInfo: {
        estimateTitle: '预扣费用',
        feeInfoDetails: [
          {
            expenseName: '车损',
            expenseAmountStr: 'US$ 1.00，约¥7.12',
          },
        ],
        feeContrast: {
          desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
          title: '费用汇率说明',
        },
        estimateTotalAmountStr: 'US$ 1.00，约¥7.12',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '门店已撤销扣款',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆定损及维修情况进行跟踪核实',
          status: 0,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 0,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc4s12000bikic1kBDF8.png',
          ],
          commitTime: '2024-10-24 14:56:29',
          firstCommit: true,
        },
      ],
      id: 38188473,
      expenseTitle: '预扣US$ 1.00，约¥7.12',
      deductionTypeDesc: '车损',
    },
  ],
};

// 补款接口 18862/queryAdditionPayment
export const bkRes = {
  responseStatus: {
    timestamp: '/Date(1592819710168+0800)/',
    ack: 'Success',
    errors: [
      {
        message: 'String',
        errorCode: 'String',
        stackTrace: 'String',
        severityCode: 'Error',
        errorFields: [
          {
            fieldName: 'String',
            errorCode: 'String',
            message: 'String',
          },
        ],
        errorClassification: 'ServiceError',
      },
    ],
    build: 'String',
    version: 'String',
    extension: [
      {
        id: 'String',
        version: 'String',
        contentType: 'String',
        value: 'String',
      },
    ],
  },
  baseResponse: {
    isSuccess: true,
    code: '1',
    returnMsg: 'success',
    requestId: 'String',
    cost: 100,
  },
  totalSize: 2,
  totalAmount: 500,
  toPayCount: 1,
  toPayAmount: 300,
  reason: 'String',
  payedCount: 1,
  payedAmount: 200,
  currency: 'CNY',
  additionalPaymentList: [
    {
      orderId: 123456789,
      amount: 300,
      reasonCode: 0,
      remark: 'String',
      payStatus: 1,
      additionalPaymentId: 123566,
      payTime: 215646,
      bizScene: 1,
      createTime: 23465464,
      vehicleDamageLst: [
        {
          carModelName: 'String',
          carNo: 'String',
          occurrenceTime: 'String',
          totalAmount: 0,
          expenseDetailLst: [
            {
              expenseType: 0,
              expenseName: 'String',
              expenseAmount: 0,
            },
          ],
          imgLst: ['String'],
        },
      ],
      violationLst: [
        {
          carModelName: '违章',
          carNo: ' 沪145256',
          occurrenceTime: '2020-06-22 18:00:00',
          location: ' 上海市 上海市长宁区',
          behavior: '闯红灯',
          penaltyPoint: 6,
          penaltyAmount: 100,
          payStatus: 0,
          imgLst: ['String'],
        },
      ],
    },
    {
      orderId: 123456789,
      amount: 200,
      reasonCode: 0,
      remark: 'String',
      payStatus: 1,
      additionalPaymentId: 123566,
      payTime: 215646,
      bizScene: 1,
      createTime: 23465464,
      vehicleDamageLst: [
        {
          carModelName: 'String',
          carNo: 'String',
          occurrenceTime: 'String',
          totalAmount: 0,
          expenseDetailLst: [
            {
              expenseType: 1,
              expenseName: '维修费',
              expenseAmount: 100,
            },
            {
              expenseType: 2,
              expenseName: ' 停运 ',
              expenseAmount: 100,
            },
          ],
          imgLst: ['String'],
        },
      ],
      violationLst: [
        {
          carModelName: 'String',
          carNo: 'String',
          occurrenceTime: 'String',
          location: 'String',
          behavior: 'String',
          penaltyPoint: 0,
          penaltyAmount: 0,
          payStatus: 0,
          imgLst: ['String'],
        },
      ],
    },
  ],
};

// 门店消息接口 31474/queryOrderNoticeFromDid
export const mdxxRes = {
  noticeList: [
    { emailType: 1, typeName: '车损', text: '一条车损' },
    { emailType: 5, typeName: '结算单据', text: '一条结算单据' },
  ],
  noticeTitle: '门店信息',
  history: false,
  ResponseStatus: {
    Timestamp: '/Date(1748952449556+800)/',
    Ack: 'Success',
    Errors: [],
  },
  baseResponse: { isSuccess: true, code: '200', returnMsg: 'success' },
};
