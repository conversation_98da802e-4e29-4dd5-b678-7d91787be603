/* eslint-disable no-promise-executor-return */
/* eslint-disable prefer-const */
/* eslint-disable no-param-reassign */
/* eslint-disable import/no-extraneous-dependencies */
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { produce } from 'immer';
import memoize from 'memoize-one';
import {
  get as lodashGet,
  filter as lodashFilter,
  map as lodashMap,
  isNil as lodashIsNil,
  pick as lodashPick,
  isEmpty as lodashIsEmpty,
  find as lodashFind,
  keyBy as lodashKeyBy,
  forEach as lodashForEach,
  set as lodashSet,
  startsWith as lodashStartsWith,
  reduce as lodashReduce,
} from 'lodash-es';
import Loading from '@c2x/apis/Loading';
import { xShowToast, xRouter } from '@ctrip/xtaro';
import { font, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import UIToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { actionErrorCatcher } from '../../__Middleware/ZustandMiddleware';
import {
  CreateInsOrderResCode,
  FreeDepositWayType,
  SelfServicePageName,
  ORDER_BUTTON,
  DepositTipsDepositType,
  CustomerPhoneModalType,
  InsMsg,
  OrderStatusCtrip,
  ListPromptType,
  DepositStatus,
} from '../../../Constants/OrderDetail';
import {
  CarLog,
  Utils,
  CarFetch,
  AppContext,
  CarFetchHelper,
  CarStorage,
  Channel,
  MiddlePay,
  EventHelper,
  GetAB,
  InsuranceConfirmUtil,
} from '../../../Util/Index';
import {
  StorageKey,
  EventName,
  LogKeyDev,
  LogKey,
  Platform,
  Url,
  OrderDetail,
} from '../../../Constants/Index';
import {
  PageIndexId,
  TraceCode,
  BizSceneType,
  PayScene,
  PayType,
  PAY_TITLE_TYPE,
  ICrossType,
  FilterGroupCode,
  PayCurrencyStr,
  PayBusinessType,
  FEE_CODES,
  TransLimitModuleType,
  ModifyOrderAllOperationsCodeType,
  ITEM_TYPE,
  ItemLabelCodeType,
  ILableCode,
  TagCodeType,
  DepositLabelType,
  VendorId,
} from '../enum';
import {
  QueryOrderApiStatusType,
  CheckSubmitReturnCarCode,
  ILocation,
  InsCallStatus,
  AuthType,
  PolicyPressType,
  IPolicyTipType,
  ICardHistory,
  ItemsType2,
  IFeeDetailType,
  ZhimaResultMap,
  ZhimaWarnType,
  PackageInfosType,
} from '../Types';
import Texts from '../Texts';
import { CarPayParams, MiddlePayRes } from '../../../Types/PaymentType';
import {
  OrderBaseInfoType,
  ModifyTipInfoCodeType,
  InsuranceItemsType,
  RentalEssential,
} from '../../../Types/Dto/OrderDetailRespaonseType';
import { CarRentalMustReadCodeType } from '../../../Types/Dto/DetailType';
import { LogErrorInfoType } from '../../../Types/CarLogTypes';
import { graphqlFetch, graphqlPath } from '../../../Util/CarFetch/GraphqlFetch';
import getQuerySchema from '../../../Graphql/Schemas/Index';
import { fetchMetricLog } from '../../../Util/CarFetchHelper';
import getOrderCache, { setOrderCache } from '../../../Global/Cache/OrderCache';
import { IFeeItem } from '../Components/FeeDetail/src/Types';
import commonStore from './common';
import locationAndDateStore, {
  getInitPTime,
  getInitRTime,
} from './locationAndDate';
import driverAgeAndNumberStore from './driverAgeAndNumber';
import { orderLocalContactsMap } from '../method';

const { TRAVEL_INSURANCE_ID, INSURANCE_STATUS } = OrderDetail;

// #region

export const getOrderId = state => lodashGet(state, 'reqOrderParams.orderId');

const depoistFreeAuthResultTraceLog = info => {
  CarLog.LogTrace({
    key: LogKey.vac_car_trace_orderdetail_depoistfree_result,
    info,
  });
};

const getReplace = (contentText, tableMap, pictureMap) => {
  const tablePrefix = '<table>';
  if (lodashStartsWith(contentText, tablePrefix)) {
    return {
      table: tableMap[contentText.replace(tablePrefix, '')],
    };
  }

  // 国内图片处理
  const picPrefix = '<pic>';
  if (lodashStartsWith(contentText, picPrefix)) {
    // 契约没有语义化
    return {
      imgList: [
        {
          imageUrl: lodashGet(
            pictureMap[contentText.replace(picPrefix, '')],
            'desc',
          ),
        },
      ],
    };
  }

  return {
    htmlText: contentText,
  };
};

const getStorePolicyItemContent = (content, tableMap, pictureMap) => {
  if (typeof content === 'string') {
    return getReplace(content, tableMap, pictureMap);
  }

  return lodashMap(content, contentText =>
    getReplace(contentText, tableMap, pictureMap),
  );
};

const getStartInfo = info => {
  const {
    location: {
      locationName = '',
      poiInfo: { latitude = 0, longitude = 0 },
    } = {
      locationName: '',
      poiInfo: {},
    },
  } = info;
  return {
    lat: latitude,
    lng: longitude,
    addr: locationName,
  };
};

export const getOrderDetailDepositInfo = (
  authStatus,
  freeDepositData,
  userName = '',
  orderbaseInfo: any = null,
) => {
  const {
    depositItems = [],
    payMethodExplain,
    deductionTime,
    freeDepositType = 0,
    depositStatus,
    showDepositType,
    tip = {},
    realPayItems = [],
    tipsExplainDesc,
    freeDepositBtn,
  } = freeDepositData || {};

  const { vendorPreAuthInfo = {} } = orderbaseInfo || {};

  const serverRuleURL = Platform.CAR_CROSS_URL.ServerRule.ISD;

  let zhimaResult: ZhimaResultMap = {};

  if (showDepositType === 1) {
    // 程信分
    zhimaResult = {
      warnTip: '',
      texts: [
        {
          get title() {
            return '确认授权视为理解并同意';
          },
          link: {
            get text() {
              return '服务协议';
            },
            url: serverRuleURL,
          },
        },
      ],

      btnType: 1,
    };
  } else if (showDepositType === 2) {
    // 芝麻验证
    zhimaResult = tip;
  }

  const zhimaBtnTexts = ['确认信用免押', '去授权', '实名认证'];

  const zhimaBtnText = zhimaResult.btnType
    ? zhimaBtnTexts[zhimaResult.btnType - 1]
    : '';

  const depositDescTable: any = {
    items: [],
    notices: null,
  };

  if (depositItems) {
    depositDescTable.items = depositItems.map(item => ({
      retractable: item.deposit > 0,
      title: item.depositTitle,
      currencyCode: '¥',
      showFree: item.depositStatus === 1,
      currentTotalPrice: item.deposit,
      description: item.explain,
    }));

    if (payMethodExplain) {
      depositDescTable.notices = [payMethodExplain];
    }
  }

  const creditText = '';

  const depositTableTitleMap = {
    [DepositStatus.Unsupport]: '到店支付押金',
    [DepositStatus.PreAuth]: '您已在线预授权押金',
    // app 无单免
    [DepositStatus.Zhima]: '您已享押金双免',
    // app 无单免
    [DepositStatus.CreditRent]: '您已享押金双免',
    [DepositStatus.PayOnline]: '您已选择在线支付押金',
  };
  let depositTableTitle = depositTableTitleMap[depositStatus] || '';

  if ([DepositStatus.Zhima, DepositStatus.CreditRent].includes(depositStatus)) {
    const freeUsingTxt = '本单已享';
    switch (freeDepositType) {
      case 20:
        depositTableTitle = `${freeUsingTxt}${'免租车押金'}`;

        break;
      case 30:
        depositTableTitle = `${freeUsingTxt}${'免违章押金'}`;

        break;
      default:
        break;
    }
  }

  const creaditRentTitleLabelMap = {
    get 0() {
      return '押金双免';
    },
    get 10() {
      return '押金双免';
    },
    get 20() {
      return '免租车押金';
    },
    get 30() {
      return '免违章押金';
    },
  };

  const creaditRentDescMap = {
    get 0() {
      return '无需支付租车押金和违章押金';
    },
    get 10() {
      return '无需支付租车押金和违章押金';
    },
    get 20() {
      return '无需支付租车押金';
    },
    get 30() {
      return '无需支付违章押金';
    },
  };

  const orderDepositFreeTxt = '免收';

  const creaditRentTagMap = {
    0: `${orderDepositFreeTxt}`,
    10: `${orderDepositFreeTxt}`,
    20: `${orderDepositFreeTxt}${'租车押金'}`,
    30: `${orderDepositFreeTxt}${'违章押金'}`,
    100: `${orderDepositFreeTxt}`,
  };

  const creditEntryTitle = `${creditText}${creaditRentTitleLabelMap[freeDepositType]}`;
  const depositCreditSubTitle = creaditRentTitleLabelMap[freeDepositType];
  const creaditRentOutDesc = creaditRentDescMap[freeDepositType];
  const creaditRentTag = creaditRentTagMap[freeDepositType];

  const logoTypeMapper = {
    [DepositStatus.CreditRent]: DepositLabelType.CtripCreditRent,
    [DepositStatus.Zhima]: DepositLabelType.Zhima,
  };

  const isMax =
    zhimaResult.warnType === ZhimaWarnType.yihai ||
    zhimaResult.warnType === ZhimaWarnType.normal;

  return {
    ...freeDepositData,
    // 如果没有免押信息，则说明是无需免押，需要展示免押标签
    showFreeLabel: freeDepositData?.showFreeLabel || !freeDepositData,
    isHasFreeDepositData: !!freeDepositData,
    creditEntryTitle,
    get depositCreditTitle() {
      return '更多押金方式';
    },
    depositCreditSubTitle,
    creaditRentTag,
    showDepositType,
    authStatus,
    userName,
    depositTableTitle,
    creaditRentOutDesc,
    depositStatus,
    freeDepositType,
    showCreditRent: showDepositType === 1 || showDepositType === 2,
    showPreAuthRent:
      showDepositType !== 4 && vendorPreAuthInfo.preAuthDisplay === 1,
    depositDescTable,
    realPayItems,
    zhimaResult,
    zhimaBtnText,
    preAuthDescTime: `需${deductionTime}后申请`,
    get preAuthDescFeature() {
      return '提前支付租车押金，取车手续更便捷';
    },
    preAuthDescTips: [
      {
        title: `使用信用卡预授权押金，取车前72小时（${deductionTime}）将采用冻结你的信用卡额度的方法开始扣款。若扣款失败则仍需到店支付押金`,
      },
      {
        get title() {
          return '使用微信、支付宝等其他支付方式支付，将以消费的方式进行扣款';
        },
      },
      {
        get title() {
          return '支付预授权视为理解并同意';
        },
        get btnText() {
          return '服务协议';
        },
        url: serverRuleURL,
      },
    ],

    hasCreditFreeLabelType: logoTypeMapper[depositStatus],
    isMax,
    tipsExplainDesc,
    btnColorIsGray: !freeDepositBtn?.statusType,
  };
};

const getStorePolicyItems = (
  subObject,
  expandIds?: string[],
  tableMap?: any,
  pictureMap?: any,
) =>
  lodashMap(subObject, item => {
    const {
      type,
      title,
      content = [],
      subObject: subObject2,
      contentObject = [],
    } = item;
    const res: any = {
      itemTitle: title,
      type,
      content: getStorePolicyItemContent(content, tableMap, pictureMap),
      expand: !!expandIds?.find(id => id === type),
      contentObject:
        (!!contentObject.length &&
          lodashMap(contentObject, cnt => {
            const stobjs = lodashMap(cnt.stringObjs, stringObj =>
              getStorePolicyItemContent(
                stringObj.content,
                tableMap,
                pictureMap,
              ),
            );
            return {
              ...cnt,
              stringObjs: stobjs,
            };
          })) ||
        {},
    };

    if (subObject2) {
      res.contentStyle = [];
      // 三级标题
      // 将subObject2平铺开来, 通过样式进行控制
      res.content = lodashReduce(
        subObject2,
        (result, { type: type2, title: title2, content: content2 }) => {
          result.push({
            htmlText: title2,
            style: {
              ...font.body3MediumStyle,
              color: color.fontPrimary,
            },
          });
          res.expand = res.expand || !!expandIds?.find(id => id === type2);
          return result.concat(
            getStorePolicyItemContent(content2, tableMap, pictureMap),
          );
        },
        [],
      );
    }
    return res;
  });

export const getBbkStorePolicyProps = (
  expandIds: any = [],
  curProductInfo?,
) => {
  const {
    carRentalMustRead = [],
    rentalMustReadTable,
    rentalMustReadPicture,
  } = curProductInfo;
  const tableMap = lodashKeyBy(rentalMustReadTable, 'tableId');
  const pictureMap = lodashKeyBy(rentalMustReadPicture, 'title');
  const policyCodes = [
    CarRentalMustReadCodeType.Policy,
    CarRentalMustReadCodeType.ShortPolicy,
  ];

  const res: any = [];
  let hasExpand = false;

  lodashForEach(carRentalMustRead, item => {
    const { subObject, code, title, type } = item;
    if (code === CarRentalMustReadCodeType.All && title) {
      const items = getStorePolicyItems(
        [item],
        expandIds,
        tableMap,
        pictureMap,
      );
      hasExpand = hasExpand || !!lodashFind(items, { expand: true });
      res.push({
        title,
        items,
      });
    }
    if (policyCodes.indexOf(code) > -1) {
      const items = getStorePolicyItems(
        subObject,
        expandIds,
        tableMap,
        pictureMap,
      );
      // 是否是大类展开
      let expand = false;
      if (expandIds.includes(type)) {
        expand = true;
        lodashSet(items, '[0].expand', true);
      }
      hasExpand = hasExpand || !!lodashFind(items, { expand: true });
      res.push({
        title: item.title,
        items,
        expand,
      });
    }
  });

  // 全部默认打开第一个
  if (expandIds[0] === PolicyPressType.All || !hasExpand) {
    lodashSet(res, '[0].items[0].expand', true);
  }

  return res;
};

// 从订单信息中获取新首页的参数
export const getNewHomeParamFromOrder = (pickupStore, returnStore) => {
  const pickupLocation = lodashGet(pickupStore, 'location');
  const dropoffLocation = lodashGet(returnStore, 'location');
  const pickUpLat =
    pickupStore.userLatitude || lodashGet(pickupLocation, 'poiInfo.latitude');
  const pickUpLng =
    pickupStore.userLongitude || lodashGet(pickupLocation, 'poiInfo.longitude');
  const dropOffLat =
    returnStore.userLatitude || lodashGet(dropoffLocation, 'poiInfo.latitude');
  const dropOffLng =
    returnStore.userLongitude ||
    lodashGet(dropoffLocation, 'poiInfo.longitude');
  return {
    rentalLocation: {
      pickUp: {
        cid: pickupStore.cityId || lodashGet(pickupLocation, 'city.id'),
        cname: pickupStore.cityName || lodashGet(pickupLocation, 'city.name'),
        country: lodashGet(pickupLocation, 'country.name') || '中国',

        isDomestic: false,
        area: {
          id: pickupLocation?.locationCode || '',
          name:
            pickupStore.userAddress ||
            lodashGet(pickupLocation, 'locationName'),
          lat: pickUpLat,
          lng: pickUpLng,
          type: pickupLocation?.locationType || 1,
        },
      },
      dropOff: {
        cid: returnStore.cityId || lodashGet(dropoffLocation, 'city.id'),
        cname: returnStore.cityName || lodashGet(dropoffLocation, 'city.name'),
        country: lodashGet(dropoffLocation, 'country.name') || '中国',

        isDomestic: false,
        area: {
          id: dropoffLocation?.locationCode || '',
          name:
            returnStore.userAddress ||
            lodashGet(dropoffLocation, 'locationName'),
          lat: dropOffLat,
          lng: dropOffLng,
          type: dropoffLocation?.locationType || 1,
        },
      },
      isShowDropOff: !(pickUpLat === dropOffLat && pickUpLng === dropOffLng),
      // 不在 LocationAndDate Reducer setLocationInfo 中进行还车地点的覆盖
      isNotShowDropOff: !(pickUpLat === dropOffLat && pickUpLng === dropOffLng),
    },
    rentalDate: {
      pickup: pickupStore.localDateTime,
      dropoff: returnStore.localDateTime,
    },
  };
};

const getDiffInsurance = (insurance, insuranceAndXProductDesc: any = []) => {
  const purchasedInsurance: any = [];
  const plusInsurance: any = [];
  const upgradeInsurance: any = [];
  if (insurance && insurance.length > 0) {
    insurance.forEach(item => {
      if (item.group === 1) {
        if (
          item.status === INSURANCE_STATUS.Paying ||
          item.status === INSURANCE_STATUS.Payed ||
          item.status === INSURANCE_STATUS.PayFailure
        ) {
          purchasedInsurance.push(item);
        } else if (item.canUpgrade) {
          upgradeInsurance.push(item); // 可升级的保险
        } else if (item.status !== INSURANCE_STATUS.GiveUp) {
          plusInsurance.push(item); // 未支付的保险
        }
      }
    });
  }

  return {
    purchasedInsurance,
    plusInsurance,
    upgradeInsurance,
    insuranceAndXProductDesc,
  };
};

const getPriceItem = ({
  currencyCode,
  currentTotalPrice,
  showPrice,
}: any = {}) => {
  if (!lodashIsNil(currentTotalPrice)) {
    return {
      currency: currencyCode,
      price: currentTotalPrice,
    };
  }
  return { price: showPrice };
};

export const getOTimeOutInterval = ({
  continuePayInfo = {},
  remainSeconds,
}: any) => {
  let minute = 0;
  let second = 0;
  if (remainSeconds) return remainSeconds;
  minute = continuePayInfo.leftMinutes;
  second = continuePayInfo.leftSeconds;

  return second + minute * 60;
};

const getFirstLoadSucTime = (continuePayInfo, firstLoadSucTime) => {
  if (continuePayInfo) {
    const remainTime =
      continuePayInfo.leftMinutes * 60 + continuePayInfo.leftSeconds;
    return firstLoadSucTime + remainTime - dayjs().second();
  }
  return 0;
};

const getOrderTimeOutInterval = (continuePayInfo, remainSeconds) => {
  const timer = getOTimeOutInterval({
    continuePayInfo,
    remainSeconds,
  });
  return timer;
};

export const includesCarRentalFeeCode = (fees: Array<any>) =>
  fees && fees.find(item => item.code === FEE_CODES.CAR_RENTAL_FEE);

const getInfoItem = item => {
  const { title, subTitle = '' } = item || {};
  return {
    ...getPriceItem(item),
    title,
    desc: subTitle,
  };
};

export const getFeeDetailData = (dataInfo: any = {}) => {
  const {
    equipmentInfos = [],
    promotionInfos = [],
    couponInfos = [],
    chargesInfos = [],
    notIncludeCharges = {},
    chargesSummary = {},
    cashBackInfo = {}, // 已下线
    cashBackInfoV2 = {},
    activityInfo = {},
    depositInfo = {},
    discountList = [],
    modifyInfo = {},
    adjustPriceInfo = {},
  } = dataInfo || {};

  const [tableTitle, tableDesc, tablePriceDesc] = lodashGet(
    notIncludeCharges,
    'subTitle',
    '',
  ).split('|');
  const exclude = {
    name: notIncludeCharges.title,
    tableTitle,
    tableDesc,
    tablePriceDesc,
    items: lodashMap(notIncludeCharges.items, item => {
      const { title, size, localDailyPrice, localCurrencyCode } = item;
      return {
        title,
        desc: size,
        currency: localCurrencyCode,
        price: localDailyPrice,
      };
    }),
    tips: notIncludeCharges.notices,
    total: {
      title: notIncludeCharges.description,
      currency: notIncludeCharges.localCurrencyCode,
      price: notIncludeCharges.localTotalPrice,
      localCurreny: notIncludeCharges.currencyCode,
      localDayPrice: notIncludeCharges.currentTotalPrice,
    },
  };

  const totalPriceInfo = {
    ...getPriceItem(chargesSummary),
    ...lodashPick(chargesSummary, ['title', 'notices']),
    items: lodashMap(chargesSummary.items, (item, i) => ({
      totalTitle: i === 0 ? chargesSummary.subTitle : '',
      ...item,
      ...getPriceItem(item),
    })),
  };
  const hasCarRentalFee = includesCarRentalFeeCode(chargesInfos);
  const commonFee = chargesInfos.map((chargesInfo: any = {}) => {
    const {
      title,
      size,
      showFree,
      items,
      description,
      subTitle,
      priceDailys,
      dPriceDesc,
      hourDesc,
      code,
      extraDescription,
    } = chargesInfo;
    const fee: IFeeItem = {
      ...getPriceItem(chargesInfo),
      title,
      subTitle: size,
      desc: description || subTitle,
      extraDescription,
      isFree: showFree,
      priceDailys,
      dPriceDesc,
      hourDesc,
      code,
    };
    if (code === FEE_CODES.CAR_RENTAL_FEE) {
      fee.items = (items || []).map(item => ({
        ...item,
        // 除租车费外，认定其他都属于优惠
        discount: item.code !== FEE_CODES.RENTAL_FEE,
      }));
    } else {
      fee.items = (items || []).map(item => ({
        name: item.title,
        desc: [item.description].filter(Boolean),
        labels: item.labels,
      }));
    }
    return fee;
  });
  let promotion: any = [];
  let promotionList: any = [];
  if (!hasCarRentalFee) {
    if (discountList && discountList.length) {
      promotionList = discountList;
    } else {
      promotion = lodashMap(promotionInfos, promotionInfo =>
        getInfoItem(promotionInfo),
      );
      promotionList = [...(couponInfos || [])];
      if (Object.keys(activityInfo).length !== 0) {
        promotionList.push(activityInfo);
      }
    }
    if (promotionList.length) {
      promotion = promotion.concat(
        promotionList.map(item => {
          const { title, currencyCode, currentTotalPrice, subTitle, items } =
            item;
          return {
            title,
            currency: currencyCode,
            price: currentTotalPrice,
            desc: subTitle,
            items,
          };
        }),
      );
    }
  }

  // 平台补贴展示
  if (Object.keys(adjustPriceInfo).length !== 0) {
    const { currencyCode, currentTotalPrice, title } = adjustPriceInfo;
    promotion.unshift({
      title,
      currency: currencyCode,
      price: currentTotalPrice,
    });
  }

  const extraPurchase =
    equipmentInfos.length > 0
      ? lodashMap(equipmentInfos, equipmentInfo => ({
          ...getInfoItem(equipmentInfo),
          subTitle: equipmentInfo.size,
        }))
      : [];

  let cashBack: any = [];
  if (cashBackInfoV2) {
    const keys = Object.keys(cashBackInfoV2);
    if (keys.length > 0) {
      const { title, subTitle, description, currencyCode, currentTotalPrice } =
        cashBackInfoV2;
      cashBack = [
        {
          name: title,
          desc: description,
          subTitle,
          currency: currencyCode,
          price: currentTotalPrice,
        },
      ];
    }
  } else {
    const { items } = cashBackInfo;
    cashBack = lodashMap(items, item => {
      const { title, description, currentTotalPrice, currencyCode } = item;
      return {
        name: title,
        desc: description,
        currency: currencyCode,
        price: currentTotalPrice,
      };
    });
  }

  const deposit = lodashMap(depositInfo.items, item => {
    const { title, description, currentTotalPrice, currencyCode } = item;
    return {
      name: title,
      desc: description,
      currency: currencyCode,
      price: currentTotalPrice,
    };
  });

  const modify = lodashIsEmpty(modifyInfo)
    ? []
    : [
        {
          ...getPriceItem(modifyInfo),
          title: modifyInfo?.title,
          subTitle: modifyInfo?.size,
          isFree: modifyInfo?.showFree,
          items: modifyInfo?.items,
        },
      ];

  const data = {
    get name() {
      return '费用明细';
    },
    feeDetail: {
      commonFee,
      extraPurchase,
      promotion,
      cashBack,
      totalPriceInfo,
      exclude,
      deposit,
      modify,
    },
  };
  return data;
};

const getInsExtend = (platformInsurance: any = {}) => {
  let amount = 0;
  const { insuranceItems } = platformInsurance;
  if (insuranceItems) {
    amount = insuranceItems.reduce(
      (count, item) =>
        item.isFromCtrip
          ? count +
            (item.insuranceInfos?.insuranceAmount
              ? item.insuranceInfos.insuranceAmount
              : 0)
          : count,
      0,
    );
  } else {
    return null;
  }
  return {
    insuranceinfos: [
      {
        provider: 1, // 用车产品目前都走的是携程代保
        amount, //
        currency: 'CNY',
      },
    ],
  };
};

export const getPaymentParams = (
  orderBaseInfo: any = {},
  vendorInfo: any = {},
  vehicleInfo: any = {},
  pickupStore: any = {},
  returnStore: any = {},
  driver: any = {},
  feeDetailInfo: any = {},
  orderPriceInfo: any = {},
  orderTimeOutInterval = null,
  insExtend = null,
  cancelRuleInfo: any = null,
  vendorId = null,
) => {
  const { payMode, orderId } = orderBaseInfo;
  const {
    localDateTime: ptime,
    userAddress,
    userSearchLocation,
    storeName,
  } = pickupStore;
  const {
    localDateTime: rtime,
    userAddress: ruserAddress,
    userSearchLocation: ruserSearchLocation,
    storeName: rstoreName,
  } = returnStore;
  const { feeDetail } = feeDetailInfo;
  const { customerPayAmount, currentCurrencyCode } = orderPriceInfo;
  const { packageType } = orderPriceInfo;

  const { commonFee = [], extraPurchase = [], promotion = [] } = feeDetail;
  const nDriver = {
    firstName: driver.name,
    secondName: driver.name,
    email: driver.email,
    cellPhone: driver.telphone,
    idnumber: driver.iDCardNo,
    idtype: String(driver.iDCardType),
    flightNo: '',
    areaCode: '',
    name: driver.name,
    age: '',
  };
  let chargesInfos = [...commonFee, ...promotion, ...extraPurchase].map(
    item => ({
      title: item.title || '',
      currencyCode: item.currency || '',
      currentTotalPrice: item.price || 0,
      description: '',
      size: item.subTitle || '',
    }),
  );

  // 海外过滤费用项没有价格的费用项
  chargesInfos = chargesInfos?.filter(item => !!item.currentTotalPrice);
  return {
    orderId: Number(orderId),
    title: vehicleInfo.vehicleName,
    subtitle: vendorInfo.vendorName,
    currency: currentCurrencyCode,
    amount: customerPayAmount,
    isHertzPrepay: packageType === 4,
    freeCancel: cancelRuleInfo.cancelDescription,
    payName: payMode && payMode === 3 ? '预付押金' : '在线预付',

    ptime: dayjs(ptime).format('YYYY-MM-DD HH:mm:ss'),
    rtime: dayjs(rtime).format('YYYY-MM-DD HH:mm:ss'),
    chargesInfos: [...chargesInfos],
    pickupLocation: userAddress || userSearchLocation || storeName,
    returnLocation: ruserAddress || ruserSearchLocation || rstoreName,
    driver: nDriver,
    orderTimeOutInterval,
    insExtend,
    vendorId,
  };
};

// 获取国内加购保险参数
export const getisdInsData = ({ plusInsurance = [] }) => {
  const ins = lodashFilter(
    plusInsurance,
    item => item.code === TRAVEL_INSURANCE_ID,
  );
  return ins;
};

export const updateFreeDepositInfoParams = state => {
  const { freeDeposit: deposit = {}, vendorInfo } = state;
  const { preAmountForCar, freeDepositType } = deposit;
  const vendorId = lodashGet(vendorInfo, 'vendorID');
  const freeDepositWay = FreeDepositWayType.Zhima;

  return {
    preAmountForCar,
    freeDepositType,
    vendorId,
    freeDepositWay,
  };
};

// 获取订单取还及年龄信息
export const getOrderInfo = (pickupStore, returnStore, customerInfo) => {
  const pickupLocation = lodashGet(pickupStore, 'location');
  const dropoffLocation = lodashGet(returnStore, 'location');
  const pickUpLat =
    pickupStore?.userLatitude || lodashGet(pickupLocation, 'poiInfo.latitude');
  const pickUpLng =
    pickupStore?.userLongitude ||
    lodashGet(pickupLocation, 'poiInfo.longitude');
  const dropOffLat =
    returnStore?.userLatitude || lodashGet(dropoffLocation, 'poiInfo.latitude');
  const dropOffLng =
    returnStore?.userLongitude ||
    lodashGet(dropoffLocation, 'poiInfo.longitude');

  return {
    rentalLocation: {
      pickUp: {
        cid: pickupStore?.cityId || lodashGet(pickupLocation, 'city.id'),
        cname: pickupStore?.cityName || lodashGet(pickupLocation, 'city.name'),
        country: lodashGet(pickupLocation, 'country.name') || '',
        isDomestic: false,
        area: {
          id: pickupLocation?.locationCode || '',
          name:
            pickupStore?.userAddress ||
            lodashGet(pickupLocation, 'locationName'),
          lat: pickUpLat,
          lng: pickUpLng,
          type: pickupLocation?.locationType || 1,
        },
      },
      dropOff: {
        cid: returnStore?.cityId || lodashGet(dropoffLocation, 'city.id'),
        cname: returnStore?.cityName || lodashGet(dropoffLocation, 'city.name'),
        country: lodashGet(dropoffLocation, 'country.name') || '',
        isDomestic: false,
        area: {
          id: dropoffLocation?.locationCode || '',
          name:
            returnStore?.userAddress ||
            lodashGet(dropoffLocation, 'locationName'),
          lat: dropOffLat,
          lng: dropOffLng,
          type: dropoffLocation?.locationType || 1,
        },
      },
      isShowDropOff: !(pickUpLat === dropOffLat && pickUpLng === dropOffLng),
      isNotShowDropOff: !(pickUpLat === dropOffLat && pickUpLng === dropOffLng),
    },
    rentalDate: {
      pickUp: {
        dateTime: pickupStore?.localDateTime,
      },
      dropOff: {
        dateTime: returnStore?.localDateTime,
      },
    },
    age: customerInfo?.age,
  };
};

export const getElsePaymentParams = (orderInfo, state): CarPayParams => {
  const {
    orderId,
    amount,
    isInsOrder,
    titletype = PAY_TITLE_TYPE.Car,
    hideOrderPaySummary,
    payremindTime,
    businessType,
    businessId,
  } = orderInfo;
  const {
    vendorInfo,
    pickupStore,
    returnStore,
    driverInfo: driver,
  } = state || {};
  const nDriver = {
    firstName: driver.name,
    secondName: driver.name,
    email: driver.email,
    cellPhone: driver.telphone,
    idnumber: driver.iDCardNo,
    idtype: String(driver.iDCardType),
    flightNo: '',
    areaCode: '',
    name: driver.name,
    age: '',
  };
  const currency = orderInfo.currencyCode ? orderInfo.currencyCode : 'CNY';
  const chargesInfos = {
    title: orderInfo.name,
    currencyCode: currency,
    currentTotalPrice: amount,
  };
  const insExtend = isInsOrder
    ? {
        insExtend: {
          insuranceinfos: [
            {
              provider: 1, // 用车产品目前都走的是携程代保
              amount,
              currency: 'CNY',
            },
          ],
        },
      }
    : {};

  return {
    orderId,
    title: orderInfo.ordertitle || orderInfo.orderTitle,
    subtitle: vendorInfo.vendorName,
    currency: orderInfo.currencyCode ? orderInfo.currencyCode : 'CNY',
    amount,
    isHertzPrepay: false,
    freeCancel: '',
    payName: '',
    ptime: pickupStore.localDateTime,
    rtime: returnStore.localDateTime,
    pickupLocation: pickupStore.userAddress,
    returnLocation: returnStore.userAddress,
    driver: nDriver,
    chargesInfos: [chargesInfos],
    requestid: orderInfo.requestid || orderInfo.requestId,
    requestId: orderInfo.requestId,
    isFillMoney: true,
    ...insExtend,
    titletype,
    hideOrderPaySummary,
    payremindTime,
    businessType,
    businessId,
    vendorId: state?.vendorInfo?.bizVendorCode,
    payType: PayType.RegularPay,
  };
};

// 重复下单拦截类型
export const OrderCheckResultCode = {
  weakInterception: 'PBK_2_6_1', // 弱拦截
  strongInterception: 'PBK_2_6', // 强拦截
};

// 继续支付接口响应code
export const ContinuePayResultCode = {
  VerificationFailed: '40013', // 验证未通过
};

export const getParams = (data: any): any =>
  data && JSON.parse(decodeURIComponent(data));

export const getApiQueryParam = (actionData, reference = null) => {
  const { data } = AppContext.UrlQuery;
  const params = getParams(data);
  // @ts-ignore
  const pushData = actionData;
  const fixParams = params && params.pickupStoreId ? params : pushData;
  const {
    pickupStoreId,
    dropoffStoreId,
    rentCenterId,
    pStoreWay,
    rStoreWay,
    pickupPointInfo,
    returnPointInfo,
    pRc,
    rRc,
    pickWayInfo,
    returnWayInfo,
  } = fixParams;

  return {
    fixParams,
    param: {
      pickupStoreId,
      dropoffStoreId,
      rentCenterId,
      pStoreWay,
      rStoreWay,
      pickupPointInfo,
      returnPointInfo,
      pRc,
      rRc,
      pickWayInfo,
      returnWayInfo,
      reference,
    },
  };
};

// 获取订详可以展示的按钮
export const getOperationButtons = orderDetailResponse => {
  const allOperations = orderDetailResponse?.operation?.orderOperation;
  let nAllOperations = [];
  if (allOperations) {
    nAllOperations = allOperations?.filter(v => v.display !== 'none');
  }
  return nAllOperations;
};

export const getFulfillIsCanRenew = renewButton => {
  return renewButton?.enable ? 1 : 0;
};

export enum IBizScene {
  modifyOrderAdditionalPay = 4, // 修改订单补款
}

export enum IPayStatus {
  waitingPayment = 0,
  // 0-待支付 1-支付中 2-支付成功 3-支付失败 4-部分退款 5-全额退款
}

// 是否修改订单补款
export const getIsModifyOrderAddPayment = state => {
  const additionalPaymentList =
    state?.additionPaymentInfo?.additionalPaymentList;
  if (additionalPaymentList) {
    const modifyOrder = additionalPaymentList.filter(
      f =>
        f.bizScene === IBizScene.modifyOrderAdditionalPay &&
        f.payStatus === IPayStatus.waitingPayment,
    );
    return modifyOrder && modifyOrder.length > 0;
  }
  return false;
};

const getbuildInsuranceParams = memoize(
  (insurance = [], driverInfo = null, isAddIns?: boolean) => {
    let selectedInsuranceList = [];
    const insuredList: any = [];
    let insuranceList = [];

    selectedInsuranceList = insurance.map(item => ({
      insuranceId: item.code,
      insuredId: AppContext.UserInfo.userId,
    }));

    insuranceList = insurance.map(v => ({
      insuranceId: v.code,
      title: v.name,
      // desc: arryToStr(v.description),
      priceNoteList: [
        {
          priceText: `¥${v.price}`,
        },
      ],
    }));
    const insuredObj = {
      name: driverInfo.name,
      idCardType: driverInfo.iDCardType || 99, // 代表加密方式是其他
      idCardNo: driverInfo.encrypIDCardNo,
      insuredId: AppContext.UserInfo.userId,
      age: driverInfo.age,
      extendInfo: {},
    };
    insuredList.push(insuredObj);
    return {
      insuredList,
      insuranceList,
      selectedInsuranceList,
      callbackType: 1,
      invokePage: isAddIns ? 2 : 0,
    };
  },
);

// 继续支付的保代参数
export const getInsConfirmReqParam = (driverInfo, buyedInsurances) => {
  if (buyedInsurances.length > 0) {
    return getbuildInsuranceParams(buyedInsurances, driverInfo);
  }
  return false;
};

export const getRenewalOrder = state => lodashGet(state, 'renewalOrders[0]');

export const getRenewalOrders = state => lodashGet(state, 'renewalOrders');

const getHadBuyInsurances = (ctripInsuranceInfos, insuranceAndXProduct) => {
  // 获取已购买的保险
  let arr = [];
  if (ctripInsuranceInfos?.length > 0) {
    // 海外
    arr = ctripInsuranceInfos
      .filter(item => {
        const { productId, statusDesc, insuranceOrderId } = item;
        return (
          productId &&
          statusDesc !== INSURANCE_STATUS.GiveUp &&
          insuranceOrderId
        );
      })
      .map(v => ({
        ...v,
        code: v.productId,
        name: v.productName,
        price: v.insuranceAmount,
      }));
  }
  if (insuranceAndXProduct?.length > 0) {
    // 国内
    arr = insuranceAndXProduct.filter(item => {
      const { code, status, insuranceOrderId } = item;
      return (
        code === TRAVEL_INSURANCE_ID &&
        status !== INSURANCE_STATUS.GiveUp &&
        insuranceOrderId
      );
    });
  }
  return arr;
};

export const getDepositPaymentModalData = (freeDeposit, orderModalsVisible) => {
  const {
    oldAlipayCredit,
    tips = [],
    preAmountForCar,
    preAmountForPeccancy,
    freeDepositWay,
  } = freeDeposit || {};
  const tip = tips?.find(
    item =>
      [
        DepositTipsDepositType.CreditRent,
        DepositTipsDepositType.Zhima,
      ]?.includes(item.depositType),
  );
  const { btnName, depositType, btnType, popupInfo, verifyTexts, noteInfo } =
    tip || {};
  const visible = orderModalsVisible?.depositPaymentModal?.visible;
  return {
    visible,
    btnName,
    depositType,
    btnType,
    oldAlipayCredit,
    popupInfo,
    depositDerateRuleInfo: noteInfo,
    preAmountForCar,
    preAmountForPeccancy,
    verifyTexts,
    freeDeposit,
    freeDepositWay,
  };
};

// 芝麻点击埋点信息
export const getZhimaTraceInfo = (
  pickupStore,
  returnStore,
  vehicleInfo,
  orderBaseInfo,
  vendorInfo,
  freeDeposit,
) => ({
  orderId: orderBaseInfo?.orderId,
  // 取还车门店id
  pstoreCode: `${pickupStore?.storeID}`,
  rstoreCode: `${returnStore?.storeID}`,
  // 携程车型id
  vehicleCode: vehicleInfo?.ctripVehicleID,
  // 供应商id
  vendorCode: `${vendorInfo?.vendorID}`,
  rentCarDeposit: freeDeposit?.preAmountForCar,
  illegalDeposit: freeDeposit?.preAmountForPeccancy,
  addDeposit: freeDeposit?.complementaryAmount,
});

export const queryContinuePayParams = (state, insData) => {
  const orderId = getOrderId(state);
  const {
    ctripInsuranceInfos,
    insuranceAndXProduct,
    driverInfo,
    continuePayInterceptionData,
    freeDeposit,
    vendorInfo,
  } = state;
  const buyedInsurances: any =
    getHadBuyInsurances(ctripInsuranceInfos, insuranceAndXProduct) || [];
  const insConfirmReqParam = getInsConfirmReqParam(driverInfo, buyedInsurances);
  const strongSubmit = continuePayInterceptionData?.strongSubmit;
  const preAmountForCar = lodashGet(freeDeposit, 'preAmountForCar');
  const vendorId = lodashGet(vendorInfo, 'vendorID');
  const params = {
    orderid: orderId,
    type: 2,
    preAmountForCar,
    vendorId,
    strongSubmit,
  }; // 默认参数
  if (!insData) {
    return params;
  }
  // 保代status 0: 确认，1: 取消（导航栏back回退，侧滑回退，android物理键回退），2: 保代页面异常导致保险必须取消
  const { status: insStatus } = insData;
  const selectedInsuranceList =
    lodashGet(insData, 'data.selectedInsuranceList') || [];
  if (
    insConfirmReqParam &&
    (insStatus === InsCallStatus.submit || insStatus === InsCallStatus.cancel)
  ) {
    // 获取反选的保险id
    const inverseInsds = buyedInsurances.filter(
      v =>
        !selectedInsuranceList.find(
          item => Number(item.insuranceId) === Number(v.code),
        ),
    );

    const ctripOrderIds = buyedInsurances
      .filter(v =>
        selectedInsuranceList.find(
          item => Number(item.insuranceId) === Number(v.code),
        ),
      )
      .map(v => Number(v.insuranceOrderId));

    const token = lodashGet(insData, 'token');
    let additionalServicesByRemove = [];
    if (inverseInsds.length > 0) {
      additionalServicesByRemove = inverseInsds.map(item => ({
        productId: item.productId,
        serviceCode: item.code,
        serviceName: item.name,
        totalAmount: item.price,
        insureds: [driverInfo.name],
        insuranceOrderId: item.insuranceOrderId,
      }));
    }
    return {
      ...params,
      additionalServicesByRemove,
      preToken: token,
      ctripOrderIds,
    };
  }
  return params;
};

const getCreateInsOrder = async (orderId, processData) => {
  // 优享新流程
  const params = {
    orderId,
    name: processData.name,
    ctripCode: processData.code,
    sourceFrom: processData.sourceFrom,
    price: processData.price,
    amount: processData.price,
    quantity: processData.quantity,
    quantityName: processData.quantityName,
    description: processData.description,
    additionalId: processData.additionalId,
    status: processData.status,
    token: processData.insuranceToken,
    packageDetail: processData.packageDetail,
  };
  const res = await CarFetch.ISDBuyInsOrder(params).catch(() => {});
  return {
    isSuccess: res && res.baseResponse && !!res.baseResponse.isSuccess,
    refNo: res && res.referenceNo,
    businessType: res?.businessType,
    businessId: res?.additionalId,
    resultCode: res?.baseResponse?.code,
  };
};

export const getDepositPayOnlineParams = (
  orderId,
  amount,
  vendorId,
): CarPayParams => {
  const title = '租车押金';
  const currency = PayCurrencyStr.CNY;
  return {
    businessType: PayBusinessType.DepositPayOnline,
    orderId: Number(orderId),
    businessId: orderId,
    title,
    titletype: PAY_TITLE_TYPE.Normal,
    currency,
    amount,
    chargesInfos: [
      {
        title,
        currencyCode: currency,
        currentTotalPrice: amount,
      },
    ],

    hideOrderPaySummary: true,
    hidePayRemind: true,
    requestId: BbkUtils.uuid(),
    vendorId,
    payType: PayType.RegularPay,
  };
};

export const isKlbVersion = state =>
  Number(state?.orderBaseInfo?.attr?.klbVersion) === 1;

export const checkParams = (data): boolean =>
  data && data.pickupStoreId && data.dropoffStoreId;

export const getMapGuideCacheKey = orderId => {
  return `getMapGuide_${orderId}`;
};

export const getIsdFreeDeposit = (
  creditInfo,
  isAlipay,
  orderBaseInfo,
  isdFeeInfo,
  pickupStore,
  orderStatus,
  orderId,
) => {
  const { vendorPreAuthInfo } = orderBaseInfo;

  return {
    orderId,
    vendorPreAuthInfo: {
      ...vendorPreAuthInfo,
      preAuthAmount: isdFeeInfo.preAuthAmount || 0,
      pickTime: pickupStore && pickupStore.localDateTime,
    },
    creditInfo,
    // cashPledgeStatus: 免押金状态 0-未准入 1-未申请 2-已申请 3-额度不足 4-扣款失败 5-扣款成功 6-补款成功 7-需追款
    // orderStatus: 订单状态 0-待支付，1-待确认，2-已确认，3-已取消，4-已完成, 5-处理中
    showCreditTab:
      !isAlipay &&
      creditInfo &&
      creditInfo.cashPledgeStatus === 1 &&
      orderStatus === 2,
  };
};

export const getFetchHelperParam = param => {
  const { orderId } = param;
  // 订详情使用默认缓存key会导致预请求与实际请求的缓存key不一致
  const cachePolicy = {
    cacheKey: getMapGuideCacheKey(orderId),
    enableCache: true,
  };
  const parameter = CarFetchHelper.parameterBuilder({
    param,
    cachePolicy,
  });
  return parameter;
};

export const getApiQueryGuideFetchParam = (actionData, reference) => {
  const { orderId } = actionData;
  const { param } = getApiQueryParam(actionData, reference);
  const reqParam = { orderId };
  return checkParams(param) || orderId ? getFetchHelperParam(reqParam) : null;
};

export const getFreeDepositProgress = freeDeposit =>
  freeDeposit?.freeDepositProgress || {};

export const getDepositInfo = freeDeposit => {
  const {
    depositStatus,
    preAmountForCar,
    preAmountForPeccancy,
    depositItemName,
    freeDepositType,
    depositItemTitle,
  } = freeDeposit || {};
  return {
    depositStatus,
    preAmountForCar,
    preAmountForPeccancy,
    depositItemName,
    freeDepositType,
    depositItemTitle,
  };
};

// 获取只有一条车损的id
export const getOnlyVehicleDamageId = vehicleDamageList => {
  let onlyVehicleDamageId;
  if (vehicleDamageList?.length === 1) {
    onlyVehicleDamageId = vehicleDamageList[0].id;
  }
  return onlyVehicleDamageId;
};

export const getOrderStatusCtrip = orderBaseInfo =>
  orderBaseInfo?.orderStatusCtrip;

// 点评卡片
export const getCommentButtonInfo = operation => {
  const allOperations = operation?.orderOperation;
  if (allOperations?.length > 0) {
    return allOperations.find(f => f.operationId === ORDER_BUTTON.Comments);
  }
  return null;
};

export const getPhoneMenus = (pickupStore, returnStore, phoneModalType) => {
  const { storeTel: pickUpStoreTel = '' } = pickupStore;
  const { storeTel: returnStoreTel = '' } = returnStore;

  const pickupStoreTels = Utils.getPhoneList(pickUpStoreTel);
  const returnStoreTels = Utils.getPhoneList(returnStoreTel);

  let pickStoreTitle: string;
  let returnStoreTitle: string;

  if (phoneModalType === CustomerPhoneModalType.Customer) {
    pickStoreTitle = `${'取车'}${'门店电话'}`;
    returnStoreTitle = `${'还车'}${'门店电话'}`;
  } else if (phoneModalType === CustomerPhoneModalType.Phone) {
    pickStoreTitle = '取车门店';
    returnStoreTitle = '还车门店';
  } else {
    pickStoreTitle = '取车门店';
    returnStoreTitle = '还车门店';
  }

  return JSON.stringify(pickUpStoreTel) === JSON.stringify(returnStoreTel)
    ? [
        {
          get name() {
            return '门店电话';
          },
          tels: pickupStoreTels,
        },
      ]
    : [
        {
          name: pickStoreTitle,
          tels: pickupStoreTels,
        },
        {
          name: returnStoreTitle,
          tels: returnStoreTels,
        },
      ];
};

export const getLocalContactsData = (pickupStore, returnStore) => {
  const { contactWayList } = pickupStore || {};
  const { contactWayList: returnContactWayList } = returnStore || {};
  return [
    orderLocalContactsMap(contactWayList),
    orderLocalContactsMap(returnContactWayList),
  ].filter(item => !!item?.length);
};

export const getStoreIdData = (pickupStore, returnStore) => {
  return [pickupStore?.storeID, returnStore?.storeID];
};

export const getContinuePayTick = continuePayInfo => {
  let visible = false;
  let minute = 0;
  let second = 0;
  visible = continuePayInfo && continuePayInfo.needContinuePay;
  if (visible) {
    minute = continuePayInfo.leftMinutes;
    second = continuePayInfo.leftSeconds;
  }

  return {
    visible,
    minute,
    second,
  };
};

// 门店政策是否有车辆意外与故障处理节点
export const getIsHasAccidentOSD = carRentalMustRead => {
  const importantInformation = carRentalMustRead?.find(
    v => v?.type === PolicyPressType.importantInformation,
  );
  return importantInformation?.subObject?.some(
    obj => obj?.type === PolicyPressType.AccidentBreakdownRule,
  );
};

export const getModifyTip = modifyInfoDto => {
  const reorderTip = modifyInfoDto?.tipInfo?.find(
    v => v.code === ModifyTipInfoCodeType.reorderTip,
  );
  return reorderTip?.content;
};

// 是否来自手机号查单渠道
export const getOrderDataByPhone = authType => authType === AuthType.byPhone;

export const getpolicyList = (isHasAccidentOSD, IsHasFlightDelayRule) => {
  const policies: any = [];
  const osdPolicies = [
    {
      text: '航班延误保留政策',
      type: IPolicyTipType.flightDelay,
      show: !!IsHasFlightDelayRule,
    },
    {
      text: '车辆意外与故障处理',
      type: IPolicyTipType.accidentOsd,
      show: !!isHasAccidentOSD,
    },
    {
      text: '门店政策',
      type: IPolicyTipType.store,
      show: true,
    },
  ];

  policies.push(...osdPolicies.filter(p => p.show));
  return policies;
};

export const getReqOrderParams = state => {
  return state?.reqOrderParams || { orderId: AppContext.UrlQuery?.orderId };
};

export const getTipsCardInfo = (tipsCardInfoRes, orderWaringInfo) => {
  const warningTitle = orderWaringInfo?.warningDtos?.[0]?.warningTitle;
  if (
    tipsCardInfoRes?.isAddWarningTitle &&
    tipsCardInfoRes?.summary?.length &&
    warningTitle
  ) {
    const tipsCardInfo = { ...tipsCardInfoRes };
    const summary = [...(tipsCardInfo?.summary || {})];
    summary.push({
      content: warningTitle,
    });
    tipsCardInfo.summary = summary;
    return tipsCardInfo;
  }
  return tipsCardInfoRes;
};

export const getIsShowMessageAssistantBtn = orderStatusCtrip => {
  // 履约可视化仅在已完成状态展示消息助手入口
  return orderStatusCtrip === OrderStatusCtrip.COMPLETED;
};

// 从订单信息中获取首页的参数
export const getHomeParamFromOrder = (
  vendorInfo: any = {},
  pickupStore: any = {},
  returnStore: any = {},
) => {
  const { vendorID } = vendorInfo || {};
  /**
   * 1.有些VBK默认支持上门取还`serviceType`为2，即使`issendcar`和`ispickupcar`传值为2
   * 2.一嗨则需要用户在首页手动选择是否需要上门取还服务
   * 3.以下逻辑为了避免当用户在首页没选上门取还但是下单了默认支持上门取还的vbk门店，
   * 用户点击修改订单时，首页错误缓存了issendcar=1，ispickupcar=1，导致后续只能搜索出一嗨门店
   */
  let issendcar;
  let ispickupcar;
  if (vendorID === 9787) {
    issendcar = pickupStore.serviceType === 2 ? 1 : 2;
    ispickupcar = returnStore.serviceType === 2 ? 1 : 2;
  } else {
    issendcar = 2;
    ispickupcar = 2;
  }
  const ptime = pickupStore?.localDateTime?.replace(/-/g, '/');
  const rtime = returnStore?.localDateTime?.replace(/-/g, '/');
  return {
    issendcar,
    ispickupcar,
    pcid: `${pickupStore.cityId}`,
    rcid: `${returnStore.cityId}`,
    pcname: pickupStore.cityName,
    rcname: returnStore.cityName,
    poiinfo: {
      addr: pickupStore.userAddress,
      lat: `${pickupStore.userLatitude}`,
      lng: `${pickupStore.userLongitude}`,
    },
    rpoiinfo: {
      addr: returnStore.userAddress,
      lat: `${returnStore.userLatitude}`,
      lng: `${returnStore.userLongitude}`,
    },
    ptime,
    rtime,
  };
};

export const getOrderParamFromOrder = (
  pickupStore,
  returnStore,
  vehicleInfo,
  orderBaseInfo,
  vendorInfo,
  isdFeeInfo,
) => {
  // 原订单参数
  const feeItem = isdFeeInfo?.feeList?.find(n => n.priceCode === '2003');
  const seatCount = (feeItem && feeItem.quantity) || 0;
  return {
    ctripOrderId: orderBaseInfo?.orderId,
    ctripOrderVendorId: `${vendorInfo?.vendorID}`,
    vendorOrderCode: orderBaseInfo?.vendorOrderCode || '',
    paymode: orderBaseInfo?.payMode,
    sid: `${pickupStore?.storeID}`,
    rsid: `${returnStore?.storeID}`,
    pid: Number(vehicleInfo?.ctripVehicleID),
    seatCount,
  };
};

export const getVendorParmFromOrder = (
  pickupStore: any = {},
  returnStore: any = {},
  vehicleInfo: any = {},
  orderBaseInfo: any = {},
  vendorInfo: any = {},
  isdFeeInfo: any = {},
) => {
  const otherRisSend = returnStore.serviceType === '2' ? 1 : 0;
  const otherPisSend = pickupStore.serviceType === '2' ? 1 : 0;
  return {
    // 取还车门店id
    psid: `${pickupStore.storeID}`,
    rsid: `${returnStore.storeID}`,
    // 供应商车型id
    vpid: `${vehicleInfo.vendorVehicleID}`,
    // 携程车型id
    pid: Number(vehicleInfo.ctripVehicleID),
    // 车型等级
    vdegree: vehicleInfo.vehicleDegree,
    // 无忧租信息
    isgranted: vehicleInfo.granted ? 1 : 0,
    grantedcode: vehicleInfo.grantCode,
    // 供应商id
    vendorid: `${vendorInfo?.vendorID}`,
    // 供应商类型
    ctripvendortype: `${vendorInfo?.vendorID}`,
    // 安飞士专用
    ratecode: isdFeeInfo.rateCode,
    ratecid: isdFeeInfo.rateCategory,
    // 1普通价格 2提前预约 3打包 4推荐打包
    pricetype: isdFeeInfo.priceType,
    // 送车上门服务
    issend: pickupStore.serviceType === '2' ? '1' : '0',
    risSend: Number(vendorInfo?.vendorID) > 30000 ? otherRisSend : null,
    pisSend: Number(vendorInfo?.vendorID) > 30000 ? otherPisSend : null,
    paymode: orderBaseInfo.payMode,
  };
};

export const getVendorHeaderProps = (vendorInfo, rentCenter, orderBaseInfo) => {
  const { commentInfo = {} } = vendorInfo || {};
  const {
    exposedScore = 0,
    topScore = 0,
    commentLabel = '',
    level,
    isFlagShip,
    flapShipText,
    hasComment,
  } = commentInfo;
  const { vendorLogo, vendorName, vendorImageUrl } = vendorInfo;
  const { ftype } = orderBaseInfo || {};
  return {
    vendorLogo: vendorLogo || vendorImageUrl,
    vendorName: vendorName?.replace('(携程优选)', ''),
    title: '',
    scoreDesc: level || '',
    commentDesc: '',
    hasComment,
    score: Number(exposedScore).toFixed(1) || '',
    totalScore: topScore,
    commentLabel,
    scoreLow: false,
    isEasyLife: false,
    isSelect: false,
    isRentCenter: !!rentCenter,
    isFlagShip: !!ftype || isFlagShip,
    isOptimize: false,
    flapShipText,
  };
};

export const getCarLabelsInfo = (allTags: any = []) => {
  const tags: any = [];

  allTags.forEach(item => {
    // 车辆配置
    if (item?.marketGroupCode && item?.marketGroupCode === 'MarketGroup1201') {
      const isShowQuestion = item?.code === ILableCode.ETC;
      tags.push({
        ...item,
        title: item?.name,
        sortNum: item?.sort,
        category: '1',
        description: item.desc,
        isShowQuestion,
      });
    }
  });

  return tags || [];
};

// 安心行标签
export const getRestAssuredTag = (allTags = []) => {
  const tagObj = lodashFind(
    allTags,
    item => item.code === ItemLabelCodeType.RESTASSURED,
  );
  return tagObj;
};

export const getNextStorageCardsTitle = (
  storageCardsTitle,
  refundPenaltyInfo,
  service,
  tipsCardInfo,
  supportInfo,
) => {
  let nextStorageCardsTitle = [...storageCardsTitle];
  const hasRefundCard = refundPenaltyInfo?.status >= 0;
  const hasServiceCard = !!service?.serviceTitle;
  const hasTipsCard = tipsCardInfo?.summary?.length > 0;
  const hasAuthCard = supportInfo?.isShow;

  const isAllCurrent =
    !storageCardsTitle?.length &&
    hasRefundCard &&
    hasServiceCard &&
    hasTipsCard &&
    hasAuthCard;

  if (
    hasRefundCard &&
    refundPenaltyInfo?.attrDto?.history === ICardHistory.unknown &&
    !storageCardsTitle?.includes(Texts.refundCardTitle)
  ) {
    nextStorageCardsTitle.push(Texts.refundCardTitle);
  }

  if (
    hasServiceCard &&
    service?.serviceCardHistory === ICardHistory.unknown &&
    !storageCardsTitle?.includes(Texts.serviceCardTitle)
  ) {
    nextStorageCardsTitle.push(Texts.serviceCardTitle);
  }

  // 四个卡片不全展示，则AuthCard可能曝光
  if (
    !isAllCurrent &&
    hasAuthCard &&
    supportInfo?.authCardHistory === ICardHistory.unknown &&
    !storageCardsTitle?.includes(Texts.authCardTitle)
  ) {
    nextStorageCardsTitle.push(Texts.authCardTitle);
  }

  // 接口返回history为false时，要从localStorage中删除
  if (
    hasRefundCard &&
    refundPenaltyInfo?.attrDto?.history === ICardHistory.notHistory
  ) {
    nextStorageCardsTitle = nextStorageCardsTitle.filter(
      item => item !== Texts.refundCardTitle,
    );
  }

  if (
    hasServiceCard &&
    service?.serviceCardHistory === ICardHistory.notHistory
  ) {
    nextStorageCardsTitle = nextStorageCardsTitle.filter(
      item => item !== Texts.serviceCardTitle,
    );
  }

  return nextStorageCardsTitle;
};

export const getOrderTraceData = (
  orderId,
  pickUpStoreInfo,
  returnStoreInfo,
  vendorId,
) => {
  return {
    orderId,
    pStoreId: pickUpStoreInfo?.storeID,
    rStoreId: returnStoreInfo?.storeID,
    vendorId,
  };
};

export const getNationalChainTag = (allTags = []) => {
  const tagObj =
    lodashFind(
      allTags,
      item =>
        item.code === ITEM_TYPE.ORDERNATIONALCHNAIN ||
        item.code === ITEM_TYPE.NATIONALCHNAIN,
    ) || {};
  return {
    ...tagObj,
    title: tagObj?.name,
  };
};

export const getFetchOrderSuccess = queryOrderApiStatus =>
  queryOrderApiStatus === QueryOrderApiStatusType.success;

export const getOrderFuelDesc = vehicleInfo => {
  return {
    // 燃油提示
    fuelNote: vehicleInfo?.fuelNote,
    // 燃油提示标题
    fuelNoteTitle: vehicleInfo?.fuelNoteTitle,
  };
};

export const getAdditionalDriverDesc = promptInfos => {
  const data = promptInfos?.find(
    v => v.type === ListPromptType.AdditionalDriver,
  );
  return {
    title: data?.title,
    content: data?.contents?.[0]?.stringObjs?.[0].content,
  };
};

export const didNoticeDataIsCurrentFn = didNoticeData => {
  const { history, noticeList } = didNoticeData || {};
  return !history && noticeList?.length > 0;
};

export const getHasOtherCard = (
  commentButtonInfo,
  serviceTitle,
  refundPenaltyInfo,
  didNoticeDataCurrent,
) => {
  // 是否存在点评卡片
  const hasCommentCard = commentButtonInfo?.disableCode === 1;

  // 境外 是否存在服务咨询进度卡片
  const hasServiceProgress = !!serviceTitle;
  // 境外 是否存在申请退违约金进度模块
  const hasRefundPenalty = refundPenaltyInfo?.status >= 0;
  return (
    hasCommentCard ||
    hasServiceProgress ||
    hasRefundPenalty ||
    didNoticeDataCurrent
  );
};

// 获取车损详情 || 海外押金抵扣车损详情
export const getDamageInfoRenderData = (osdDeductionList, vehicleDamageId) => {
  const list = osdDeductionList;
  const damageData = list?.find(item => item.id === vehicleDamageId) || {};
  const {
    occurrenceTime,
    imgLstV2 = [],
    deductionTypeDesc,
    feeInfo,
  } = damageData;
  const { feeContrast } = feeInfo || {};
  const combineUrls: any = [];
  const pureImageList: any = [];
  const showArr: any = [];
  if (imgLstV2?.length > 0) {
    imgLstV2.forEach((item, index) => {
      let urlItems = [
        ...(item?.vedioUrl || []),
        ...(item?.imgUrl?.map(img => ({ imageUrl: img })) || []),
      ];

      if (index > 0) {
        urlItems = urlItems.map(url => ({ ...url, isNewAdd: true }));
      }
      combineUrls.unshift(...urlItems);
      pureImageList.unshift(
        ...(item?.imgUrl?.map(img => ({ imageUrl: img })) || []),
      );
    });
    combineUrls.forEach((combineUrl, combineIndex) => {
      if (combineIndex < 10) {
        if (combineUrl?.videoUrl) {
          showArr.push(combineUrl.videoUrl);
        } else if (combineUrl?.imageUrl) {
          showArr.push(combineUrl.imageUrl);
        }
      }
    });
  }
  return {
    occurrenceTime,
    combineUrls,
    pureImageList,
    showArr,
    feeContrast,
    deductionTypeDesc,
  };
};

export const getIsContractTrackerHistory = orderStatusCtrip =>
  [OrderStatusCtrip.COMPLETED, OrderStatusCtrip.CANCELLED].includes(
    orderStatusCtrip,
  );

// 映射限行提示返回结果
export const mappingLimitInfo = res => {
  let data: any = null;
  const isSuccess = lodashGet(res, 'baseResponse.isSuccess');
  const { limitTitle, limitInfos } = res || {};
  if (isSuccess && limitInfos) {
    const displayTitle = limitTitle; // 用于弹窗内的标题展示
    const LimitSimmarize = limitInfos?.[0]?.limitContents?.find(
      item => item.module === TransLimitModuleType.LimitSimmarize,
    );
    const title = LimitSimmarize?.title;
    data = {
      title,
      limitInfos,
      displayTitle,
    };
  }
  return data;
};

export const getOrderStatus = state =>
  state.orderBaseInfo && state.orderBaseInfo.orderStatus;

interface PriceDetailModalDataType {
  data: IFeeDetailType;
  title: string;
}

export const packagePriceDetailModalData = (
  orderPriceInfoFee,
): PriceDetailModalDataType => {
  let chargesInfos = [];
  if (orderPriceInfoFee?.chargesInfos?.length > 0) {
    chargesInfos = orderPriceInfoFee.chargesInfos.map(feeItem => {
      const isCarRentalFee = feeItem.code === FEE_CODES.CAR_RENTAL_FEE;
      const isAdditionalFee = feeItem.code === FEE_CODES.CAR_ADDITIONAL_FEE;
      const notPromotionCodes: string[] = [
        FEE_CODES.RENTAL_FEE,
        FEE_CODES.EASYLIFE_2024,
      ];

      const newFeeItem = produce(feeItem, draftFeeItem => {
        if (feeItem?.items?.length > 0) {
          // eslint-disable-next-line no-param-reassign
          draftFeeItem.items = feeItem.items.map(detailItem => ({
            ...detailItem,
            isPromotion:
              isCarRentalFee && !notPromotionCodes.includes(detailItem.code),
            size: isAdditionalFee ? detailItem?.size : '',
          }));
        }
      });

      return newFeeItem;
    });
  }
  let chargesSummary: any = null;
  if (orderPriceInfoFee?.chargesSummary) {
    const baseSummary = orderPriceInfoFee.chargesSummary;
    chargesSummary = produce(baseSummary, draftSummary => {
      // eslint-disable-next-line no-param-reassign
      draftSummary.isPriceDesc = baseSummary.code === TagCodeType.gray;
    });
  }
  return {
    data: {
      chargesInfos,
      chargesSummary,
      cashBackInfo: orderPriceInfoFee?.cashBackInfoV2,
      points: orderPriceInfoFee?.userPoints,
      offlineFee: orderPriceInfoFee?.offlineFee,
    },
    get title() {
      return '费用明细';
    },
  };
};

export const getUseCityID = state =>
  lodashGet(state, 'orderBaseInfo.useCityID');

export const getModifyCancelRule = modifyInfoDto => {
  const modifyCancelRules = modifyInfoDto?.modifyCancelRules;
  if (modifyCancelRules && modifyCancelRules.length > 0) {
    return modifyCancelRules[0];
  }
  return null;
};

export const hadBuyInsurances = (ctripInsuranceInfos, insuranceAndXProduct) => {
  // 获取已购买的保险
  let arr: any = [];
  if (ctripInsuranceInfos?.length > 0) {
    // 海外
    arr = ctripInsuranceInfos
      .filter(item => {
        const { productId, statusDesc, insuranceOrderId } = item;
        return (
          productId &&
          statusDesc !== INSURANCE_STATUS.GiveUp &&
          insuranceOrderId
        );
      })
      .map(v => ({
        ...v,
        code: v.productId,
        name: v.productName,
        price: v.insuranceAmount,
      }));
  }
  if (insuranceAndXProduct?.length > 0) {
    // 国内
    arr = insuranceAndXProduct.filter(item => {
      const { code, status, insuranceOrderId } = item;
      return (
        code === TRAVEL_INSURANCE_ID &&
        status !== INSURANCE_STATUS.GiveUp &&
        insuranceOrderId
      );
    });
  }
  return arr;
};

export const getIsEhi = vendorInfo =>
  String(vendorInfo?.vendorID) === VendorId.ehi;

export const supportModifyOrder = orderBaseInfo => {
  const allOperations = orderBaseInfo?.allOperations;
  return !!allOperations?.find(
    v =>
      v?.operationId === ORDER_BUTTON.ModifyOrder &&
      v?.enable &&
      v?.code === ModifyOrderAllOperationsCodeType.page,
  );
};

// 是否出境修改订单B版
export const getIsNewOsdModifyOrder = (state: State) => {
  return state.orderBaseInfo?.attr?.osdModifyOrderVersion === 'B';
};

export const getPolicies = (carRentalMustRead: any[] = []) => {
  const policies: any[] = [];
  const policyCodes = [
    CarRentalMustReadCodeType.ShortPolicy,
    CarRentalMustReadCodeType.OnlyShortPolicy,
  ];
  const carRentalMustReadData = carRentalMustRead;
  carRentalMustReadData.forEach(item => {
    if (policyCodes.indexOf(item.code) > -1) {
      policies.push({
        labName: item.title,
        id: item.type,
        sortNum: item.sortNum,
      });
    }
  });

  const sortPolicies = policies.sort(
    ({ sortNum: sortNum1 } = {}, { sortNum: sortNum2 } = {}) =>
      sortNum1 - sortNum2,
  );

  sortPolicies.push({
    labName: Texts.all,
    id: PolicyPressType.All,
  });

  return sortPolicies;
};

// #endregion

interface IDefaultTipPopData {
  visible: boolean;
  data: object;
}

interface IdefaultCancelFee {
  amount: number;
  currencyCode: number;
  canRefund: boolean;
  auditTime: string;
}

const defaultTipPopData = {
  visible: false,
  data: {},
};

const defaultCancelFee = {
  amount: 0,
  currencyCode: 0,
  canRefund: false,
  auditTime: '',
};

type State = {
  isLoading: boolean;
  isFail: boolean;
  isPolling: boolean;
  response: any;
  useCalabiId: any;
  reqOrderParams: any; // 请求参数
  osdRefund: any;
  orderRenewalEntry: any;
  rentalDays: number;
  priceModalVisible: boolean;
  cancelModalVisible: boolean;
  orderChangeModalVisible: boolean;
  orderIsdChangeModalVisible: boolean;
  reviewSuccessModalVisible: boolean;
  appResponseMap: any;
  reviewModalVisible: boolean;
  reqModifyOrderParam: object;
  npsResponseParam: object;
  npsResponse: object; // nps评价
  hasNps: number; // 默认不展示nps
  resCancelFee: IdefaultCancelFee;
  orderBaseInfo: OrderBaseInfoType;
  operation: any;
  refundPenaltyInfo: any;
  creditInfo: any;
  isAlipay: boolean;
  cancelReason: string;
  feeDeductionVisible: boolean;
  feeDeductionData: any;
  BbkInsuranceDetailProps: any;
  isdVendorInsurance: object;
  packageInfos: Array<any>;
  productDetails: Array<any>;
  isdFeeInfo: any;
  feeDetail: any;
  rentalMustReadTable: any;
  rentalMustReadPicture: any;
  ctripInsuranceInfos: Array<any>;
  labelsModalVisible: boolean;
  easyLifeTagModalVisible: boolean;
  phoneModalVisible: boolean;
  phoneModalType: string;
  phoneModalFromWhere: any;
  personPhoneModalVisible: boolean;
  pmsInfo: any;
  limitPopVisible: boolean;
  limitCont: any;
  paymentParam: object;
  createPaymentResponse: any;
  replenishHistoryVisible: boolean;
  depositDetailModalVisible: boolean;
  customerServiceUrl: string;
  modifyFlightNoModalVisible: boolean;
  insuranceAndXProduct: any;
  similarVehicleInfo: any;
  cancelRuleInfo: {
    osdCancelRuleInfo?: {
      subTitle?: string;
      items?: ItemsType2[];
      description?: string;
    };
    customerCurrency?: string;
    cancelReasons: Array<any>;
  };
  promptInfos: Array<any>;
  orderDetailPrice: any;
  orderPriceInfo: any;
  orderPrice: any;
  rentCenter: {
    id: number;
  };
  rRentCenter: {
    id: number;
  };
  labelsInfo: any;
  insurance: Array<any>;
  fetchDone: boolean;
  freeDeposit: any;
  pickupStore: any;
  returnStore: any;
  renewalOrders: any;
  violationList?: any;
  osdDeductionList: any;
  removeDetail: boolean;
  violationDesc: object;
  vehicleDamageList?: any;
  vehicleDamageId: any;
  renewalTips: any;
  isShowViolationDamageEntry: boolean;
  isShowSupplementRedIcon: boolean;
  isQueryOrderLoading: boolean;
  modalsVisible: {
    optimizeModalVisible: {
      visible: boolean;
    };
    createInsModalVisible: {
      visible: boolean;
    };
    insFailedModalVisible: {
      visible: boolean;
    };
    confirmModal: {
      visible: boolean;
    };
    pickUpMaterials: {
      visible: boolean;
    };
    renewTipModal: {
      visible: boolean;
    };
    refundDetailModal: {
      visible: boolean;
    };
    // 芝麻重复订单提示弹层
    sesameRepeatOrderModalauthModal: {
      visible: boolean;
    };
    ehiModifyOrderModal: {
      visible: boolean;
      data: any;
    };
    damageFeeDetailModalVisible: {
      visible: boolean;
    };
    // 一嗨双免
    ehiFreeDepositModal: {
      visible: boolean;
    };
    // 返现弹窗
    orderCashBackModal: {
      visible: boolean;
    };
    // 免押弹窗
    depositPaymentModal: {
      visible: boolean;
    };
    // 点评开放提醒弹层
    reviewUnopenedModal: {
      visible: boolean;
    };
    // 优化增强弹层
    optimizationStrengthenModal: {
      visible: boolean;
    };
    // 提前还车记录
    advanceReturnModal: {
      visible: boolean;
    };
    // 提前还车费用明细
    advanceReturnFeeModal: {
      visible: boolean;
    };
    // 供应商资质弹层
    businessLicenseModal: {
      visible: boolean;
    };
    // ETC 介绍弹层
    etcIntroModal: {
      visible: boolean;
    };
    // ETC 使用帮助
    etcUseHelperModal: {
      visible: boolean;
    };
    // 距离校验不通过弹窗
    distanceInvalidateModal: {
      visible: boolean;
    };
    // 取消订单确认弹窗
    cancelOrderConfirmModal: {
      visible: boolean;
    };
    // 售后营业时间收费规则
    businessTimePolicyModal: {
      visible: boolean;
      type: string;
    };
    // 售后营业时间详情弹层
    businessTimeModal: {
      visible: boolean;
      type: string;
    };
    // 联系门店社交方式等弹层
    contactDoorStoreModal: {
      visible: boolean;
      data: any;
    };
    buyInsConfirmModal: {
      visible: boolean;
      data: any;
    };
    sesameRepeatOrderModal: {
      visible: boolean;
      data: any;
    };
    vehicleInfoModal: {
      visible: boolean;
      data: any;
    };
  };
  equipmentInfo: any;
  queryOrderApiStatus: QueryOrderApiStatusType; // 0:表示未开始请求，1：请求成功了，2：请求失败了
  firstLoadSucTime: number;
  continuePayInfo: {
    needContinuePay: boolean;
    leftMinutes: number;
    leftSeconds: number;
  };
  orderRenewStatusVisible: boolean;
  orderStatusHashSign: string;
  payCountDownTimeOut: boolean;
  easyLifeTags: Array<any>;
  tipPopData: IDefaultTipPopData;
  queryCarAssistantV2Response: any;
  storageCardsTitle: Array<any>;
  priceDetailModalVisible: boolean; // 费用明细弹层是否展示
  orderDetailConfirmModalVisible: boolean; // 信息确认弹层是否展示
  orderWelfareRes: any; // 订详加油劵领劵入口接口响应
  newOrderInsAndXRes: object;
  scannedImages: Array<any>; // 新增车损图片已阅状态
  appOrderDetailIsSettlementOfClaimOpen: boolean;
  couponLists: Array<any>;
  cashBackInfo: object;
  // 继续支付失败提示弹窗相关信息
  continuePayFailDialogInfo: {
    visible: boolean;
    type: any; // 弹窗类型，confirm或alert
    content: string; // 提示信息
  };
  queryOrderAllDataSuccess: boolean; // 订详全量数据接口请求是否结束
  packageIncludes: any;
  orderFulfillmentModifyInfo: any;
  fullSearchNum: number; // 订详全量数据接口请求次数
  freezeDepositExplain: Array<any>;
  insuranceAndXProductDesc: Array<any>;
  continuePayInterceptionData: any;
  isFetchCancelInfoLoading: boolean; // 刷新取消页信息
  orderCancelInfo: any;
  isPenaltyChange: boolean; // 违约金发生变更
  penaltyChangeTip: any; // 违约金变更信息
  penaltyChangeCancelTip: string;
  flight: any; // 航班信息
  travelLimitSelectedResult: Array<any>; // 旅行限制选择结果
  flightDelayRulesModalVisible: boolean; // 航班延误政策弹层
  driverLicenseOrdersEnities: Array<any>; // 驾照翻译件订单列表
  driverLicensePageNo: number; // 驾照翻译件订单列表页数
  nextPageOffset: any; // 翻下一页时传上一页接口返回的Offset
  isLastPage: boolean; // 是否是最后一页
  vehicleStatus: any;
  fulfillmentData: any; // 履约可视化
  distance: any; // 距离
  cancelOrderSubmitId: string;
  didNoticeData: { noticeList: Array<any> };
  osdModifyOrderNote: any;
  locationDatePopVisible: boolean;
  modifyDriverInfoType: any;
  pickUpCountryInfo: any;
  vendorInfo: any;
  vehicleInfo: any;
  driverInfo: any;
  modifyInfoDto: any;
  isdContinuePayAmount: any;
  carRentalMustRead: any;
  additionPaymentInfo: any;
  authType: any;
  isdCarMgImUrl: string;
  jumpModifyToPay: boolean;
  osdAvailableInsuranceDescInfo: any;
  appOrderDetailIsAddInsuranceNeeded: any;
  orderDetailPageRef: any;
  carAssistantSummary: Array<any>;
  osdPolicy?: any;
  rentalEssential?: RentalEssential;
  platformInsurance?: {
    packageInfos: PackageInfosType;
    briefInsuranceItems: InsuranceItemsType[];
    insuranceItems: InsuranceItemsType[];
    reminder: any[];
  };
  receipt: any;
  ces: any;
};

type Actions = {
  fetchOrder2: (data?: any, interval?: boolean) => void;
  queryOrderDetail: (data: any) => void;
  fetchOrderSuccess: (data: any) => void;
  queryOrderSuccess: (data: any) => void;
  fetchQueryCancelFee: (data: any) => void;
  fetchQueryCancelFeeCallBack: (data: any) => void;
  setEasyLifeTagModalVisible: (data: any) => void;
  toCancelBook: (data: any) => void;
  setPriceModalVisible: (data: any) => void;
  showReviewSuccessModal: (data: any) => void;
  showReviewModal: (data: any) => void;
  setOrderChangeModalVisible: (data: any) => void;
  queryScoreAndSuggestionsCallback: (data: any) => void;
  fetchCarAssistantCallBack: (data: any) => void;
  setIsdOrderChangeModalVisible: (data: any) => void;
  showFeeDeduction: (data: any) => void;
  showInsDetailModal: (data: any) => void;
  setLabelsModalVisible: (data: any) => void;
  setPhoneModalVisible: (data: any) => void;
  setPersonPhoneModalVisible: (data: any) => void;
  getLimitContentData: () => void;
  getLimitContentSuccess: (data: any) => void;
  setLimitRulePopVisible: (data: any) => void;
  createPayment: (data: any) => void;
  setHistoryModalVisible: (data: any) => void;
  setDepositDetailModalVisible: (data: any) => void;
  queryOrderPriceInfoCallBack: (data: any) => void;
  queryOrderPriceInfoFun: (data: any) => void;
  fetchCustomerServiceUrlSuccess: (data: any) => void;
  setModifyFlightNoModalVisible: (data: any) => void;
  ctripContinuePay: (data, goToInsFun) => void;
  continuePayment: () => void;
  getAdditionPayment: (data: any) => void;
  setFetchDone: (data: any) => void;
  updateFreeDepositInfo: (data: any) => void;
  reset: () => void;
  getSupplementList: (data: any) => void;
  setQueryDeductionData: (data: any) => void;
  getSupplementListCallBack: (data: any) => void;
  setVehicleDamageId: (data: any) => void;
  setSupplementListNew: (data: any) => void;
  goIsdInsurancePayment: (data: any) => void;
  setOrderModalsVisible: (data: any) => void;
  isShowRenewStatusByStorage: () => void;
  setSelectorData: (data: any) => void;
  saveRenewalOrderStatus: () => void;
  queryOrderStatus: (data: any) => void;
  setOrderStatusSign: (data: any) => void;
  payCountDownTimeOutFn: (data: any) => void;
  setTipPopData: (data: any) => void;
  queryCarAssistantV2CallBack: (data: any) => void;
  setStorageCardsTitle: (data: any) => void;
  updateFreeDepositInfoByContinuePay: () => void;
  setPriceDetailModalVisible: (data: any) => void;
  depositPayOnline: () => void;
  setOrderDetailConfirmModalVisible: (data: any) => void;
  queryOrderInsAndXProductCallBack: (data: any) => void;
  queryOrderCancelInfo: (data: any) => void;
  queryOrderCancelInfoCallBack: (data: any) => void;
  setContinuePayFailDiaLogInfo: (data: any) => void;
  handleContinuePayFailDialogCancel: () => void;
  queryFirstScreenData: (data: any) => void;
  fetchOrderDataByGraphql: (data: any) => void;
  setContinuePayInterceptionData: (data: any) => void;
  toCancelCallBack: (data: any) => void;
  setTravelLimitSelectedResult: (data: any) => void;
  fetchModifyCrossLocation: (data: any) => void;
  setFlightDelayRulesModalVisible: (data: any) => void;
  queryExtraInsurance: (data: any) => void;
  setIsQueryOrderLoading: (data: any) => void;
  createPreFetch: (data: any) => void;
  setFulfillmentData: (data: any) => void;
  checkSubmitReturnCar: (data: any) => void;
  setDistance: (data: any) => void;
  setDidNoticeData: (data: any) => void;
  queryDidNoticeData: (data: any) => void;
  setLocationAndDatePopIsShow: (data: any) => void;
  queryOsdModifyOrderNote: (data: any) => void;
  setOsdModifyOrderNote: (data: any) => void;
  osdModifyOrderInit: (data: any) => void;
  queryCountrysInfo: () => void;
  setCountrysInfo: (data: any) => void;
  queryOrderWarningInfo: () => void;
  handleContinuePayFail: (response) => void;
};

export const getInitalState = () => ({
  isLoading: true,
  isFail: false,
  isPolling: false,
  response: {},
  useCalabiId: undefined,
  reqOrderParams: {
    orderId: '',
  }, // 请求参数
  osdRefund: {},
  orderRenewalEntry: {},
  rentalDays: 0,
  priceModalVisible: false,
  cancelModalVisible: false,
  orderChangeModalVisible: false,
  orderIsdChangeModalVisible: false,
  reviewSuccessModalVisible: false,
  appResponseMap: {},
  reviewModalVisible: false,
  reqModifyOrderParam: {},
  npsResponseParam: {},
  npsResponse: {}, // nps评价
  hasNps: 1, // 默认不展示nps
  resCancelFee: defaultCancelFee,
  orderBaseInfo: {
    orderId: '',
    orderStatus: '',
  },
  operation: {},
  refundPenaltyInfo: {},
  creditInfo: {},
  isAlipay: false,
  cancelReason: '',
  feeDeductionVisible: false,
  feeDeductionData: {},
  BbkInsuranceDetailProps: {},
  isdVendorInsurance: {},
  packageInfos: [],
  productDetails: [],
  isdFeeInfo: {},
  feeDetail: {},
  rentalMustReadTable: [],
  rentalMustReadPicture: [],
  ctripInsuranceInfos: [],
  labelsModalVisible: false,
  easyLifeTagModalVisible: false,
  phoneModalVisible: false,
  phoneModalType: '',
  phoneModalFromWhere: '',
  personPhoneModalVisible: false,
  pmsInfo: null,
  limitPopVisible: false,
  limitCont: null,
  paymentParam: {},
  createPaymentResponse: null,
  replenishHistoryVisible: false,
  depositDetailModalVisible: false,
  customerServiceUrl: '',
  modifyFlightNoModalVisible: false,
  insuranceAndXProduct: [],
  similarVehicleInfo: null,
  cancelRuleInfo: {
    cancelReasons: [],
    osdCancelRuleInfo: undefined,
  },
  promptInfos: [],
  orderDetailPrice: null,
  orderPriceInfo: null,
  orderPrice: null,
  rentCenter: {
    id: -1,
  },
  rRentCenter: {
    id: -1,
  },
  labelsInfo: [],
  insurance: [],
  fetchDone: false,
  freeDeposit: null,
  pickupStore: {},
  returnStore: {},
  renewalOrders: [],
  violationList: [],
  osdDeductionList: [],
  removeDetail: false,
  violationDesc: {},
  vehicleDamageList: [],
  vehicleDamageId: null,
  renewalTips: {},
  isShowViolationDamageEntry: false,
  isShowSupplementRedIcon: false,
  isQueryOrderLoading: false,
  modalsVisible: {
    optimizeModalVisible: {
      visible: false,
    },
    createInsModalVisible: {
      visible: false,
    },
    insFailedModalVisible: {
      visible: false,
    },
    confirmModal: {
      visible: false,
    },
    pickUpMaterials: {
      visible: false,
    },
    renewTipModal: {
      visible: false,
    },
    refundDetailModal: {
      visible: false,
    },
    // 芝麻重复订单提示弹层
    sesameRepeatOrderModalauthModal: {
      visible: false,
    },
    ehiModifyOrderModal: {
      visible: false,
      data: {},
    },
    damageFeeDetailModalVisible: {
      visible: false,
    },
    // 一嗨双免
    ehiFreeDepositModal: {
      visible: false,
    },
    // 返现弹窗
    orderCashBackModal: {
      visible: false,
    },
    // 免押弹窗
    depositPaymentModal: {
      visible: false,
    },
    // 点评开放提醒弹层
    reviewUnopenedModal: {
      visible: false,
    },
    // 优化增强弹层
    optimizationStrengthenModal: {
      visible: false,
    },
    // 提前还车记录
    advanceReturnModal: {
      visible: false,
    },
    // 提前还车费用明细
    advanceReturnFeeModal: {
      visible: false,
    },
    // 供应商资质弹层
    businessLicenseModal: {
      visible: false,
    },
    // ETC 介绍弹层
    etcIntroModal: {
      visible: false,
    },
    // ETC 使用帮助
    etcUseHelperModal: {
      visible: false,
    },
    // 距离校验不通过弹窗
    distanceInvalidateModal: {
      visible: false,
    },
    // 取消订单确认弹窗
    cancelOrderConfirmModal: {
      visible: false,
    },
    // 售后营业时间收费规则
    businessTimePolicyModal: {
      visible: false,
      type: '',
    },
    // 售后营业时间详情弹层
    businessTimeModal: {
      visible: false,
      type: '',
    },
    // 联系门店社交方式等弹层
    contactDoorStoreModal: {
      visible: false,
      data: null,
    },
    buyInsConfirmModal: {
      visible: false,
      data: null,
    },
    sesameRepeatOrderModal: {
      visible: false,
      data: null,
    },
    vehicleInfoModal: {
      visible: false,
      data: null,
    },
  },
  equipmentInfo: {},
  queryOrderApiStatus: QueryOrderApiStatusType.unstart, // 0:表示未开始请求，1：请求成功了，2：请求失败了
  firstLoadSucTime: 0,
  continuePayInfo: {
    needContinuePay: false,
    leftMinutes: 0,
    leftSeconds: 0,
  },
  orderRenewStatusVisible: false,
  orderStatusHashSign: '',
  payCountDownTimeOut: false,
  easyLifeTags: [],
  tipPopData: defaultTipPopData,
  queryCarAssistantV2Response: null,
  storageCardsTitle: [],
  priceDetailModalVisible: false, // 费用明细弹层是否展示
  orderDetailConfirmModalVisible: false, // 信息确认弹层是否展示
  orderWelfareRes: null, // 订详加油劵领劵入口接口响应
  newOrderInsAndXRes: {},
  scannedImages: [], // 新增车损图片已阅状态
  appOrderDetailIsSettlementOfClaimOpen: false,
  couponLists: [],
  cashBackInfo: {},
  // 继续支付失败提示弹窗相关信息
  continuePayFailDialogInfo: {
    visible: false,
    type: '', // 弹窗类型，confirm或alert
    content: '', // 提示信息
  },
  queryOrderAllDataSuccess: false, // 订详全量数据接口请求是否结束
  packageIncludes: null,
  orderFulfillmentModifyInfo: null,
  fullSearchNum: 0, // 订详全量数据接口请求次数
  freezeDepositExplain: [],
  insuranceAndXProductDesc: [],
  continuePayInterceptionData: null,
  isFetchCancelInfoLoading: false, // 刷新取消页信息
  orderCancelInfo: {},
  isPenaltyChange: false, // 违约金发生变更
  penaltyChangeTip: null, // 违约金变更信息
  penaltyChangeCancelTip: '',
  flight: {},
  travelLimitSelectedResult: [], // 旅行限制选择结果
  flightDelayRulesModalVisible: false, // 航班延误政策弹层
  driverLicenseOrdersEnities: [], // 驾照翻译件订单列表
  driverLicensePageNo: 1, // 驾照翻译件订单列表页数
  nextPageOffset: null, // 翻下一页时传上一页接口返回的Offset
  isLastPage: false, // 是否是最后一页
  vehicleStatus: null,
  fulfillmentData: null, // 履约可视化
  distance: null, // 距离
  cancelOrderSubmitId: '',
  didNoticeData: { noticeList: [] },
  osdModifyOrderNote: null,
  locationDatePopVisible: false,
  modifyDriverInfoType: undefined,
  pickUpCountryInfo: {},
  vendorInfo: {},
  vehicleInfo: {},
  driverInfo: {},
  modifyInfoDto: {},
  isdContinuePayAmount: {},
  carRentalMustRead: [],
  additionPaymentInfo: {},
  authType: undefined,
  isdCarMgImUrl: '',
  jumpModifyToPay: false,
  osdAvailableInsuranceDescInfo: null,
  appOrderDetailIsAddInsuranceNeeded: null,
  orderDetailPageRef: null,
  carAssistantSummary: [],
  rentalEssential: null,
  platformInsurance: null,
  receipt: {},
  ces: {},
});

// @ts-ignore
const initialState: State = getInitalState();

const orderDetailStore = create<State & Actions>(
  actionErrorCatcher(
    immer<State & Actions, [], []>((set, get) => ({
      ...initialState,
      fetchOrder2: async (data?: any) => {
        const { callback, isFetchStatic } = data || {};
        const { orderId: oid } = AppContext.UrlQuery;
        const state = get();
        // dispatch参数orderId||当前页面链接orderId参数||state中的orderid
        const orderId =
          oid || data.orderId || getReqOrderParams(state)?.orderId;

        // const previousOrderStatus = getOrderStatus(state);

        // 本地开发时可以去掉
        if (!orderId) {
          UIToast.show('The order number or account number is wrong');
          return;
        }
        const pageId = Channel.getPageId().Order.ID;
        const params = {
          orderId,
          channelType: Number(AppContext.MarketInfo.channelId),
          pageId,
          requestId: BbkUtils.uuid(),
        };
        let openResultInfo: LogErrorInfoType = {
          eventResult: true,
          expPoint: 'apiOrderQueryData',
        };
        try {
          get().setIsQueryOrderLoading(true);
          // 第一次请求全量数据接口的同时，增加发起首屏静态数据请求
          if (isFetchStatic) {
            get().queryFirstScreenData({ orderId });
            const sOrder = getOrderCache(orderId);
            CarLog.LogTraceDev({
              key: LogKeyDev.c_car_dev_app_order_cache,
              info: { eventResult: !!sOrder, extraData: sOrder, orderId },
            });
            if (sOrder) {
              get().fetchOrderSuccess({
                response: sOrder,
                queryOrderApiStatus: QueryOrderApiStatusType.before,
              });
            }
            return;
          }

          // 国内海外都需要查询车损违章的扣除记录（海外的展示即押金扣款）
          get().getSupplementList({ orderId, isLoading: false }); // 补款
          let res = await CarFetch.queryOrder(params).catch(() => {});
          // if (__DEV__) {
          //   // mock主接口
          //   const mockInfo = require('./mock');
          //   res = mockInfo.dzfddRes;
          // }
          const isSuccess = res?.baseResponse?.isSuccess;
          if (isSuccess) {
            get().queryOrderSuccess({ res, orderId });
            if (res?.orderBaseInfo?.orderContact) {
              // eslint-disable-next-line global-require
              const credentialsStore = require('./credentials').default;
              credentialsStore.getState().getCredentialsList();
            }
            get().fetchOrderDataByGraphql({
              orderId,
              vendorId: res?.vendorInfo?.vendorID,
            });
          } else if (!state?.queryOrderAllDataSuccess) {
            get().fetchOrderSuccess({
              queryOrderApiStatus: QueryOrderApiStatusType.fail,
            });
            // 只上报服务有返回场景
            if (res) {
              openResultInfo = {
                eventResult: false,
                expCode: res.baseResponse?.code,
                expMsg: res.baseResponse?.returnMsg,
                expPoint: 'orderIdEmpty',
                error: res,
              };
            }
          }
          if (callback && typeof callback === 'function') callback();
          get().queryDidNoticeData({ orderId });

          // 是否全部请求结束
          get().setFetchDone({ fetchDone: true });
        } catch (error: any) {
          if (!state?.queryOrderAllDataSuccess) {
            get().fetchOrderSuccess({
              queryOrderAllDataSuccess: false,
              queryOrderApiStatus: QueryOrderApiStatusType.fail,
            });
          }
          openResultInfo = {
            eventResult: false,
            expCode: Utils.getFrontEndExpCode(null, error),
            expMsg: error?.message,
            expPoint: 'logicCatch',
            error,
          };
        } finally {
          get().setIsQueryOrderLoading(false);
          CarLog.LogTraceDev({
            key: LogKeyDev.c_car_dev_trace_order_detail_open_result,
            info: openResultInfo,
          });
        }
      },
      queryOrderDetail: async (data: any) => {
        const { orderId, callback, notifyModifyOrderCallback } = data || {};
        const pageId = AppContext.PageInstance.getPageId();
        const request = {
          orderId,
          channelType: Number(AppContext.MarketInfo.channelId),
          pageId,
          requestId: BbkUtils.uuid(),
        };
        const response = await CarFetch.queryOrder(request).catch(() => {});
        get().fetchOrderSuccess({ response, request });
        if (callback) {
          callback(!!response?.orderBaseInfo?.orderId);
        }
        if (notifyModifyOrderCallback) {
          // eslint-disable-next-line global-require
          const modifyOrder = require('./modifyOrder').default;
          modifyOrder().setInitialDataCallback({
            isSuccess: !!response?.orderBaseInfo?.orderId,
          });
        }
      },
      fetchOrderSuccess: (data: any) => {
        set({
          response: data.response,
          ...data?.response,
          orderDetailPrice: data?.orderDetailPrice || get().orderDetailPrice,
          carRentalMustRead: data?.response?.osdPolicy?.carRentalMustRead,
          reqOrderParams: data?.request || get().reqOrderParams,
          isLoading: false,
          isPolling: true,
          queryOrderAllDataSuccess: data?.queryOrderAllDataSuccess,
          queryOrderApiStatus:
            data?.queryOrderApiStatus ?? get().queryOrderApiStatus,
        });
      },
      queryOrderSuccess: async (data: any) => {
        // @ts-ignore
        const { res, orderId } = data || {};
        get().fetchOrderSuccess({
          response: { ...res, firstLoadSucTime: dayjs().second() },
          request: { orderId },
          orderDetailPrice: {
            ...res?.orderPrice?.feeDetail,
            queryOrderDetailPriceLoaded: true,
          },
          queryOrderApiStatus: QueryOrderApiStatusType.success,
          queryOrderAllDataSuccess: true,
        });
        setOrderCache({ orderId, data: res });
        // 保存isdVendorInsurance到store
        if (res?.isdVendorInsurance) {
          CarStorage.saveIsd(
            StorageKey.CAR_ISD_VENDORINSURANCE,
            res.isdVendorInsurance,
            undefined,
            'rn_car_isd',
          );
        }
        // eslint-disable-next-line global-require
        const sesameStore = require('./sesame').default;
        sesameStore.getState().initSesameAuthState({
          isOrderDetail: true,
        });
        get().queryCountrysInfo();
        // 旅行限制数据初始化
        const crossCountrySelectedLocations =
          res?.osdPolicy?.crossLocationsPolicy?.crossLocationsInfos
            ?.find(
              crossLocationItem =>
                crossLocationItem?.crossType === ICrossType.CrossCountry,
            )
            ?.locations?.filter(item => !!item.isSelected);
        if (crossCountrySelectedLocations?.length > 0) {
          get().setTravelLimitSelectedResult(
            crossCountrySelectedLocations?.map((item: ILocation) => ({
              code: item?.regionId,
              isSelected: item?.isSelected,
              groupCode: FilterGroupCode.CrossPlace,
              ...item,
            })),
          );
        }
      },
      fetchQueryCancelFee: async (data: any) => {
        const goToCancelPage = () => {
          const {
            reqOrderParams,
            resCancelFee,
            orderBaseInfo,
            isdFeeInfo,
            cancelRuleInfo,
            orderCancelInfo,
            pickupStore,
            returnStore,
            phoneModalType,
            freeDeposit,
            vendorInfo,
            isFetchCancelInfoLoading,
            isPenaltyChange,
            penaltyChangeTip,
            penaltyChangeCancelTip,
            modalsVisible,
            cancelOrderSubmitId,
            osdModifyOrderNote,
          } = get();
          const param = {
            urlParams: {
              visible: true,
            },
            storeParams: [
              {
                type: 'ORDER_CROSS_PARAMS',
                data: {
                  reqOrderParams,
                  resCancelFee,
                  orderBaseInfo,
                  isdFeeInfo,
                  cancelRuleInfo,
                  orderCancelInfo,
                  extendedInfo: orderBaseInfo?.attr,
                  pickupStore,
                  returnStore,
                  phoneModalType,
                  freeDeposit,
                  vendorInfo,
                  isFetchCancelInfoLoading,
                  isPenaltyChange,
                  penaltyChangeTip,
                  penaltyChangeCancelTip,
                  modalsVisible,
                  cancelOrderSubmitId,
                  osdModifyOrderNote,
                },
              },
            ],
          };
          // 保存Store数据，用于传参
          CarStorage.save(
            StorageKey.CAR_CROSS_PARAMS,
            JSON.stringify(param),
            '1m',
          );
          const url = Platform.CAR_CROSS_URL.OrderCancel.OSD;
          xRouter.navigateTo({ url });
        };

        const { withCancelInfo = true, callback = goToCancelPage } = data || {};
        const state = get();
        const isKlb = isKlbVersion(state);
        const params = { ...getReqOrderParams(state), timeout: 120 };
        Loading.showMaskLoading();
        const res = await CarFetch.queryCancelFee(params).catch(() => {});
        if (res?.isSuccessful) {
          get().fetchQueryCancelFeeCallBack({ resCancelFee: res });
          // 国内默认请求取消页信息
          if (GetAB.isOSDNewOrderCancel(isKlb)) {
            if (withCancelInfo) {
              get().queryOrderCancelInfo({ callback });
            }
          } else {
            callback();
          }
        } else {
          UIToast.show('系统繁忙');
        }
        Loading.hideMaskLoading();
      },
      fetchQueryCancelFeeCallBack: (data: any) => {
        set(state => {
          state.resCancelFee = data.resCancelFee;
        });
      },
      setEasyLifeTagModalVisible: (data: any) => {
        set(state => {
          state.easyLifeTagModalVisible = data;
        });
      },
      toCancelBook: async (data: any) => {
        const { refundPenaltyAmount, reasonCode, callback } =
          // @ts-ignore
          data;
        Loading.showMaskLoading();
        const state = get();
        // @ts-ignore
        const reason = getCancelReason(state);
        const penaltyAmount = state?.resCancelFee?.amount;
        const params = {
          ...getReqOrderParams(state),
          reason,
          penaltyAmount,
          refundPenaltyAmount,
          reasonCode,
        };

        const res = await CarFetch.cancelOSDOrder(params).catch(() => {});
        Loading.hideMaskLoading();
        let cancelOrderResultInfo: LogErrorInfoType = {
          eventResult: true,
          expMsg: '',
          expCode: '',
          expPoint: 'apiToCancelBook',
        };
        let isPenaltyChange;
        if (res && res.baseResponse && res.baseResponse.isSuccess) {
          UIToast.show('取消成功', 2);
          // 取消订单成功 发送事件 清空列表页缓存
          EventHelper.sendEvent(EventName.removeListCache, {});
          // dispatch(setCancelModalVisible({ visible: false }));
          // 全程保留订单项目-取消订单成功-清除submitId
        } else if (res?.baseResponse?.code === '403') {
          isPenaltyChange = true;
          get().toCancelCallBack({
            isPenaltyChange,
            penaltyChangeTip: res?.penatlyChangeTip, // 展示在取消政策弹窗中
            penaltyChangeCancelTip: res?.cancelTip, // 展示在确认取消弹窗中
          });
        } else {
          UIToast.show('取消失败，请稍后重试', 2);
          cancelOrderResultInfo = {
            eventResult: false,
            expMsg: res?.baseResponse?.returnMsg,
            expCode: res?.baseResponse?.code,
            expPoint: 'apiToCancelBookFailed',
          };
        }
        callback?.({
          isPenaltyChange,
          isShowCancelTip: false,
        });
        get().fetchOrder2({});
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_dev_trace_order_detail_cancel_order_result,
          info: cancelOrderResultInfo,
        });
      },
      setPriceModalVisible: (data: any) => {
        set(state => {
          state.priceModalVisible = data.visible;
        });
      },
      showReviewSuccessModal: (data: any) => {
        set(state => {
          state.reviewSuccessModalVisible = data.visible;
        });
      },
      showReviewModal: (data: any) => {
        set(state => {
          state.reviewModalVisible = data.visible;
        });
      },
      setOrderChangeModalVisible: (data: any) => {
        set(state => {
          state.orderChangeModalVisible = data.visible;
        });
      },
      queryScoreAndSuggestionsCallback: (data: any) => {
        set(state => {
          state.hasNps = data.res.resultCode;
        });
      },
      fetchCarAssistantCallBack: (data: any) => {
        set(state => {
          state.isdContinuePayAmount =
            data.isdContinuePayAmount || state.isdContinuePayAmount;
          state.carRentalMustRead =
            data.carRentalMustRead || state.carRentalMustRead;
          state.additionPaymentInfo =
            data.additionPaymentInfo || state.additionPaymentInfo;
        });
      },
      setIsdOrderChangeModalVisible: (data: any) => {
        set(state => {
          state.orderIsdChangeModalVisible = data.visible;
        });
      },
      showFeeDeduction: (data: any) => {
        set(state => {
          state.feeDeductionVisible = data.visible;
          state.feeDeductionData = data.feeDeductionData;
        });
      },
      showInsDetailModal: (data: any) => {
        set(state => {
          state.BbkInsuranceDetailProps = data;
        });
      },
      setLabelsModalVisible: (data: any) => {
        set(state => {
          state.labelsModalVisible = data;
        });
      },
      setPhoneModalVisible: (data: any) => {
        set(state => {
          state.phoneModalVisible = data.visible;
          state.phoneModalType = data.phoneModalType || state.phoneModalType;
          state.phoneModalFromWhere = data.phoneModalFromWhere;
        });
      },
      setPersonPhoneModalVisible: (data: any) => {
        set(state => {
          state.personPhoneModalVisible = data.visible;
          state.phoneModalType = data.phoneModalType;
        });
      },
      getLimitContentData: async () => {
        const curState = get();
        const cid: any = curState?.orderBaseInfo?.useCityID;
        const ptime = dayjs(curState?.pickupStore?.localDateTime).format(
          'YYYY-MM-DD HH:mm:ss',
        );
        const rtime = dayjs(curState?.returnStore?.localDateTime).format(
          'YYYY-MM-DD HH:mm:ss',
        );

        const param = {
          transLimitReq: {
            cityId: parseInt(cid, 10),
            vendorId: 0,
            ctripVehicleId: 0,
            pickupDate: ptime,
            returnDate: rtime,
          },
        };
        const parameter = CarFetchHelper.parameterBuilder({
          param,
          cachePolicy: { enableCache: true },
        });
        const res = await CarFetch.getLimitContent(parameter).catch(() => {});
        if (res?.baseResponse?.isSuccess) {
          get().getLimitContentSuccess({
            limitCont: res,
          });
        }
      },
      getLimitContentSuccess: (data: any) => {
        set(state => {
          state.limitCont = data.limitCont;
        });
      },
      setLimitRulePopVisible: (data: any) => {
        set(state => {
          state.limitPopVisible = data.visible;
        });
      },
      createPayment: async (data: any) => {
        const state = get();
        // @ts-ignore
        const payParams = data;
        const {
          orderId,
          amount,
          payDeadline,
          bizScene,
          additionalPaymentId,
          isModifyToPay,
        } = payParams || {};
        Loading.showMaskLoading();
        const res = await CarFetch.createOrderAdditionalPay(payParams).catch(
          () => {},
        );
        Loading.hideMaskLoading();
        let successInfo: LogErrorInfoType;
        if (res?.referenceNo) {
          const hideOrderPaySummary = getIsModifyOrderAddPayment(state);
          const payData = getElsePaymentParams(
            {
              orderId,
              amount,
              get ordertitle() {
                return '补款';
              },
              payremindTime:
                bizScene === BizSceneType.ModifyOrder ? payDeadline : undefined,
              hideOrderPaySummary,
              titletype: hideOrderPaySummary
                ? PAY_TITLE_TYPE.Normal
                : undefined,
              requestId: res.referenceNo,
              businessType: 1, // 补款
              businessId: additionalPaymentId,
            },
            state,
          );
          const payRes = (await MiddlePay({
            params: payData,
            scene: PayScene.OrderAdditionPay,
          })) as any;
          if (payRes?.success) {
            if (isModifyToPay) {
              AppContext.PageInstance.pop();
            }
            get().fetchOrder2({});
            successInfo = {
              eventResult: true,
              expCode: res?.baseResponse?.code,
              expMsg: res?.baseResponse?.returnMsg,
              expPoint: 'pay',
            };
          } else {
            successInfo = {
              eventResult: false,
              expCode: TraceCode.E1001,
              expMsg: 'payError',
              expPoint: 'payError',
            };
            UIToast.show('支付失败');
          }
        } else {
          successInfo = {
            eventResult: false,
            expCode: res?.baseResponse?.code,
            expMsg: res?.baseResponse?.returnMsg,
            expPoint: 'serverError',
          };
        }
        get().getAdditionPayment({ orderId });

        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_dev_trace_order_detail_addtion_pay,
          info: successInfo,
        });
        // 支付后延时去加载新状态
        await new Promise(resolve => setTimeout(resolve, 3000));
        get().fetchOrder2({});
      },
      setHistoryModalVisible: (data: any) => {
        set(state => {
          state.replenishHistoryVisible = data.visible;
        });
      },
      setDepositDetailModalVisible: (data: any) => {
        set(state => {
          state.depositDetailModalVisible = data.visible;
        });
      },
      queryOrderPriceInfoCallBack: (data: any) => {
        set(state => {
          state.orderDetailPrice = data.orderDetailPrice;
        });
      },
      queryOrderPriceInfoFun: async (data: any) => {
        const state = get();
        const orderId = data?.orderId || getReqOrderParams(state).orderId;
        const res = await CarFetch.queryOrderPriceInfo({
          orderId: orderId || getReqOrderParams(state).orderId,
        }).catch(() => {});
        get().queryOrderPriceInfoCallBack({
          orderDetailPrice: {
            ...res?.feeDetailInfo,
            queryOrderDetailPriceLoaded: true,
          },
        });
        const { callBack } = data;
        if (callBack && typeof callBack === 'function') callBack();
      },
      fetchCustomerServiceUrlSuccess: (data: any) => {
        set(state => {
          state.customerServiceUrl = data.customerServiceUrl;
        });
      },
      setModifyFlightNoModalVisible: (data: any) => {
        set(state => {
          state.modifyFlightNoModalVisible = data.visible;
          state.modifyDriverInfoType = data.type;
        });
      },
      ctripContinuePay: async (data, goToInsFun) => {
        const state = get();
        const orderId = lodashGet(state, 'orderBaseInfo.orderId');
        // eslint-disable-next-line global-require
        const sesameStore = require('./sesame').default;
        const { authOrderCount } = sesameStore.getState();
        Loading.showMaskLoading();
        const { confirmInsuranceData } = data ?? {};

        const { ctripInsuranceInfos, insuranceAndXProduct, driverInfo } = state;
        const buyedInsurances: any =
          getHadBuyInsurances(ctripInsuranceInfos, insuranceAndXProduct) || [];
        const buildInsuranceParams = getInsConfirmReqParam(
          driverInfo,
          buyedInsurances,
        );

        const { qConfigResponse } = commonStore.getState();
        const insuranceFlag = qConfigResponse?.insuranceFlag;
        // 跳转保险代理页
        if (buildInsuranceParams && insuranceFlag && !confirmInsuranceData) {
          Loading.hideMaskLoading();
          goToInsFun(buildInsuranceParams);
          CarLog.LogCode({
            name: '点击_订详_跳保代页面',

            info: { buildInsuranceParams },
          });
          return;
        }
        const continuePayParams: any = queryContinuePayParams(
          state,
          confirmInsuranceData,
        );
        // 2021-12-14 是否反选了保险
        const unSelectIns =
          confirmInsuranceData &&
          confirmInsuranceData?.data?.selectedInsuranceList?.length === 0;
        // 2021-12-14 到店付的订单加购自营险后变成预付定金, 反选自营险后无需跳支付

        try {
          const taskQueryPriceInfo = unSelectIns
            ? CarFetch.queryOrderPriceInfo({ orderId })
            : null;
          // 2021-12-14 反选自营险后，需要同时调继续支付接口和价格接口，以保证跳支付页时，支付页披露的费用明细不再含自营险费用
          const continuePayRequest = CarFetchHelper.parameterBuilder({
            param: continuePayParams,
            verifyResponseIsSuccess: res =>
              !(res.resultCode === '0' || !!res.rst),
          });
          const result = await Promise.all([
            CarFetch.ctripContinuePay(continuePayRequest),
            taskQueryPriceInfo,
          ]);

          const [response, priceInfoRes] = result;
          if (unSelectIns) {
            get().queryOrderPriceInfoCallBack({
              orderDetailPrice: {
                ...priceInfoRes?.feeDetailInfo,
                queryOrderDetailPriceLoaded: true,
              },
            });
          }
          Loading.hideMaskLoading();
          // 重复下单拦截
          get().setContinuePayInterceptionData(null);
          const { resultCode, resultMessage, strongSubmit } = response;
          if (
            [
              OrderCheckResultCode.weakInterception,
              OrderCheckResultCode.strongInterception,
            ].includes(resultCode)
          ) {
            get().setContinuePayInterceptionData({
              visible: true,
              resultCode,
              resultMsg: resultMessage,
              requestParams: data,
              strongSubmit,
            });
            return;
          }

          if (response.resultCode === '-1') {
            // 如果是芝麻重复订单执行回调
            const alipayTip =
              authOrderCount === 1
                ? '您已经有一笔信用免押订单在使用中，如果要继续支付这笔信用免押订单，需要去授权第二笔信用免押'
                : '如果要继续支付这笔信用免押订单，您需要去授权芝麻分';

            get().setOrderModalsVisible({
              sesameRepeatOrderModal: {
                visible: true,
                data: {
                  title: alipayTip,
                  get submitTxt() {
                    return '去授权';
                  },
                  get cancelTxt() {
                    return '再看看';
                  },
                },
              },
            });
          } else if (response.resultCode === '0' || !!response.rst) {
            get().fetchCarAssistantCallBack({
              isdContinuePayAmount: response.amt,
            });
            get().continuePayment();
          } else {
            // 2022-8-17 继续支付失败文案优化处理
            get().handleContinuePayFail(response);
          }
        } catch (err) {
          UIToast.show('支付失败，请稍后重试');
        }
      },
      continuePayment: async () => {
        const state = get();
        const {
          orderBaseInfo,
          vendorInfo,
          vehicleInfo,
          pickupStore,
          returnStore,
          driverInfo,
          orderDetailPrice,
          orderPrice,
          continuePayInfo,
          firstLoadSucTime,
          platformInsurance,
          cancelRuleInfo,
        } = state;
        const feeDetailInfo = getFeeDetailData(orderDetailPrice);
        const remainSeconds = getFirstLoadSucTime(
          continuePayInfo,
          firstLoadSucTime,
        );
        const orderTimeOutInterval = getOrderTimeOutInterval(
          continuePayInfo,
          remainSeconds,
        );
        const insExtend: any = getInsExtend(platformInsurance);
        const payData: any = getPaymentParams(
          orderBaseInfo,
          vendorInfo,
          vehicleInfo,
          pickupStore,
          returnStore,
          driverInfo,
          feeDetailInfo,
          orderPrice,
          orderTimeOutInterval,
          insExtend,
          cancelRuleInfo,
          vendorInfo?.bizVendorCode,
        );
        let payRes: MiddlePayRes;
        const OSDPayParams = {
          ...payData,
          requestId: BbkUtils.uuid(),
          busType: Platform.BUS_TYPE.NEWOSD,
          payType: PayType.RegularPay,
        };
        payRes = (await MiddlePay({
          params: OSDPayParams,
          scene: PayScene.OrderOSDContinuePay,
        })) as any;
        if (payRes?.success) {
          get().fetchOrder2({});
        } else {
          UIToast.show('支付失败');
        }
      },
      getAdditionPayment: async (data: any) => {
        // dispatch参数orderId||当前页面链接orderId参数||state中的orderid
        const { orderId } = data || {};
        const pms = {
          orderId,
        };

        const res = await CarFetch.queryAdditionPayment({ ...pms }).catch(
          () => {},
        );

        if (res?.baseResponse?.isSuccess) {
          get().fetchCarAssistantCallBack({
            additionPaymentInfo: res,
          });
        }
      },
      setFetchDone: (data: any) => {
        set(state => {
          state.fetchDone = data.fetchDone;
        });
      },
      updateFreeDepositInfo: async (data: any) => {
        const state = get();
        Loading.showMaskLoading({
          cancelable: false,
        });
        const {
          freeDepositWay,
          freeDepositType,
          preAmountForCar,
          vendorId,
          quickPayNo,
        } = data;
        const updateRes = await CarFetch.iSDUpdateFreeDepositInfo({
          orderId: getOrderId(state),
          quickPayNo: quickPayNo || BbkUtils.uuid(),
          freeDepositWay,
          freeDepositType,
          preAmountForCar,
          vendorId,
        }).catch(() => {});
        const { orderBaseInfo } = state;
        const { orderId, orderStatusDesc } = orderBaseInfo || {};
        const freeAuthLogData = {
          orderId: String(orderId),
          orderStatus: orderStatusDesc,
        };
        if (updateRes?.baseResponse?.isSuccess) {
          get().setOrderModalsVisible({
            depositPaymentModal: {
              visible: false,
            },
          });
          if (updateRes?.resultMsg) {
            UIToast.show(updateRes?.resultMsg);
          } else {
            UIToast.show('信用免押成功');
          }
          depoistFreeAuthResultTraceLog({
            ...freeAuthLogData,
            get depositFreeResult() {
              return '信用免押成功';
            },
          });
          get().fetchOrder2({
            callback: () => {
              Loading.hideMaskLoading();
            },
          });
        } else {
          depoistFreeAuthResultTraceLog({
            ...freeAuthLogData,
            get depositFreeResult() {
              return '信用免押失败';
            },
          });
          Loading.hideMaskLoading();
          UIToast.show('出错了，请稍后重试');
        }
      },
      reset: () => {
        set({ ...initialState });
      },
      getSupplementList: async (data: any) => {
        const state = get();
        const { orderId: urlQueryOrderId } = AppContext.UrlQuery;
        // @ts-ignore
        const { orderId, isLoading } = data;
        const parameter = CarFetchHelper.parameterBuilder({
          param: {
            orderId: orderId || getOrderId(state) || urlQueryOrderId,
          },
        });
        if (isLoading) Loading.show();
        const res = await CarFetch.getSuplementListApi(parameter).catch(
          () => {},
        );
        // if (__DEV__) {
        //   // mock 押金退款
        //   const mockInfo = require('./mock');
        //   res = mockInfo.yjkkRes;
        // }
        get().setQueryDeductionData({ res });
        if (isLoading) Loading?.hide();
      },
      setQueryDeductionData: async (data: any) => {
        const state = get();
        const { res } = data || {};
        const { orderId } = AppContext.UrlQuery;
        if (res?.baseResponse?.isSuccess) {
          state.getSupplementListCallBack({
            violationList: res.violationLst,
            vehicleDamageList: res.vehicleDamageLst,
            osdDeductionList: res.oSDDeductionList,
            removeDetail: res.removeDetail,
            violationDesc: res.violationDesc,
            isShowViolationDamageEntry: Number(res.baseResponse.code) === 0,
          });

          const storageTemp = await CarStorage.load(
            StorageKey.SUPPLEMENT_LIST_LENGTH,
          );
          const supplementList = JSON.parse(storageTemp!) || {};
          if (
            res?.violationLst?.length > 0 ||
            res?.vehicleDamageLst?.length > 0
          ) {
            if (supplementList[orderId!]) {
              const isViolationChange =
                supplementList[orderId!].violationListLength <
                res.violationLst.length;
              const isDamageChange =
                supplementList[orderId!].vehicleDamageList <
                res.vehicleDamageLst.length;
              if (isViolationChange || isDamageChange) {
                state.setSupplementListNew({
                  visible: true,
                });
              }
            } else {
              state.setSupplementListNew({
                visible: true,
              });
            }
          }

          supplementList[orderId!] = {
            violationListLength:
              (res.violationLst && res.violationLst.length) || 0,
            vehicleDamageList:
              (res.vehicleDamageLst && res.vehicleDamageLst.length) || 0,
          };

          CarStorage.save(
            StorageKey.SUPPLEMENT_LIST_LENGTH,
            JSON.stringify(supplementList),
          );
        }
      },
      getSupplementListCallBack: (data: any) => {
        set({ ...data });
      },
      setVehicleDamageId: (data: any) => {
        set(state => {
          state.vehicleDamageId = data;
        });
      },
      setSupplementListNew: (data: any) => {
        set({ isShowSupplementRedIcon: data.visible });
      },
      goIsdInsurancePayment: async (data: any) => {
        const state = get();
        // eslint-disable-next-line prefer-destructuring
        const orderParams = { ...getReqOrderParams(state) };
        const { orderId } = orderParams;
        let processData: any = null;
        let insData = data;
        if (data?.token) {
          const { insuranceAndXProduct, insuranceAndXProductDesc } = state;
          const diffInsurance = getDiffInsurance(
            insuranceAndXProduct,
            insuranceAndXProductDesc,
          );
          // 保代回调的情况
          // eslint-disable-next-line prefer-destructuring
          insData = getisdInsData(diffInsurance)[0];
        }
        processData = {
          ...insData,
          orderId,
          amount: insData?.price,
          isInsOrder: insData?.code === TRAVEL_INSURANCE_ID,
          insuranceToken: processData?.data?.token ?? '',
          titletype: PAY_TITLE_TYPE.Normal,
          ordertitle: insData?.title,
          hideOrderPaySummary: true,
        };
        const { status } = processData;
        // status == 0 保险未下单 status == 1 保险待支付 status == 2 保险支付中 status == 3 保险支付成功
        const payFn = async function (res?) {
          const { refNo, businessType, businessId } = res;
          if (refNo) {
            processData.requestId = refNo;
            processData.businessType = businessType;
            processData.businessId = businessId;
          }
          const data2 = getElsePaymentParams(processData, state);
          const payRes = (await MiddlePay({
            params: data2,
            scene: PayScene.OrderInsurancePay,
          })) as any;
          if (payRes?.success) {
            get().fetchOrder2({});
            CarLog.LogTraceDev({
              key: LogKeyDev.c_car_dev_trace_order_detail_plus_service,
              info: {
                eventResult: true,
                expCode: res?.baseResponse?.code,
                expMsg: res?.baseResponse?.returnMsg,
                expPoint: 'isdInsurancePayment success',
              },
            });
          } else {
            UIToast.show('支付失败');
            CarLog.LogTraceDev({
              key: LogKeyDev.c_car_dev_trace_order_detail_plus_service,
              info: {
                eventResult: false,
                expCode: TraceCode.E1001,
                expMsg: 'payError',
                expPoint: 'payError',
              },
            });
          }
        };

        if (status === 1) {
          await payFn();
        } else {
          const res: any = await getCreateInsOrder(orderId, processData);
          if (res && res.isSuccess) {
            await payFn(res);
          } else if (
            [
              CreateInsOrderResCode.getPriceFail1,
              CreateInsOrderResCode.getPriceFail2,
              CreateInsOrderResCode.priceUnMatch,
            ].includes(res.resultCode)
          ) {
            get().setOrderModalsVisible({
              buyInsConfirmModal: {
                visible: true,
                data: {
                  get title() {
                    return '门店调整了服务费价格，请重新核对价格后购买';
                  },
                  get submitTxt() {
                    return '知道了';
                  },
                },
              },
            });
            CarLog.LogTraceDev({
              key: LogKeyDev.c_car_dev_trace_order_detail_plus_service,
              info: {
                eventResult: false,
                expCode: res?.baseResponse?.code,
                expMsg: res?.baseResponse?.returnMsg,
                expPoint: 'serverError',
              },
            });
          } else {
            UIToast.show('系统异常，请稍后重试');
            CarLog.LogTraceDev({
              key: LogKeyDev.c_car_dev_trace_order_detail_plus_service,
              info: {
                eventResult: false,
                expCode: res?.baseResponse?.code,
                expMsg: res?.baseResponse?.returnMsg,
                expPoint: 'serverError',
              },
            });
          }
        }
      },
      setOrderModalsVisible: (data: any) => {
        set(state => {
          state.modalsVisible = {
            ...state.modalsVisible,
            ...data,
          };
        });
      },
      isShowRenewStatusByStorage: async () => {
        const state = get();
        // StorageKey.CAR_RENWWAL_STATUS_VISIBLE+renewalOrderId
        const data = getRenewalOrder(state);
        if (data) {
          const { renewalStatusName, renewalOrderId } = data || {};
          const storageKey = `${StorageKey.CAR_RENWWAL_STATUS_VISIBLE}_${renewalOrderId}`;
          const storageOrders =
            (await CarStorage.load(storageKey, true)) || '{}';
          const isShow =
            renewalStatusName !== JSON.parse(storageOrders)?.renewalStatusName;
          // 设置是否展示订单状态右边续租状态
          get().setSelectorData({
            orderRenewStatusVisible: isShow,
          });
        }
      },
      setSelectorData: (data: any) => {
        set({ ...data });
      },
      saveRenewalOrderStatus: async () => {
        const state = get();
        const data = getRenewalOrder(state);
        if (data) {
          const { renewalStatusName, renewalOrderId } = data || {};
          const storageKey = `${StorageKey.CAR_RENWWAL_STATUS_VISIBLE}_${renewalOrderId}`;
          CarStorage.save(
            storageKey,
            JSON.stringify({ renewalStatusName }),
            undefined,
            true,
          );
        }
      },
      queryOrderStatus: async (data: any) => {
        const state: any = get();
        const { orderId: oid } = AppContext.UrlQuery;
        const orderId = oid || data?.orderId;
        const { isFetchStatic } = data || {};
        if (isFetchStatic) {
          // 第一次请求
          get().fetchOrder2({ orderId, isFetchStatic });
        }
        const res = await CarFetch.queryOrderStatus({ orderId }).catch(
          () => {},
        );
        const orderStatusHashSign = state?.orderStatusHashSign;
        get().setOrderStatusSign({ orderStatusHashSign: res?.sign });
        if (!!orderStatusHashSign && orderStatusHashSign !== res?.sign) {
          get().fetchOrder2({ orderId });
        }
      },
      setOrderStatusSign: (data: any) => {
        set(state => {
          state.orderStatusHashSign = data?.orderStatusHashSign;
        });
      },
      payCountDownTimeOutFn: (data: any) => {
        set(state => {
          state.payCountDownTimeOut = data.timeOut;
        });
      },
      setTipPopData: (param: any) => {
        const curState = get();
        const { data: oldData = {}, visible: oldVisible } =
          curState.tipPopData || {};
        const { visible, data } = param;
        const fixVisible = visible !== undefined ? visible : oldVisible;
        const { style = {} }: any = oldData;
        const { style: newStyle = {} } = data;
        set(state => {
          state.tipPopData = {
            visible: fixVisible,
            data: {
              ...oldData,
              ...data,
              style: { ...style, ...newStyle },
            },
          };
        });
      },
      queryCarAssistantV2CallBack: (data: any) => {
        set({ queryCarAssistantV2Response: data });
      },
      setStorageCardsTitle: (data: any) => {
        set(state => {
          state.storageCardsTitle = data;
        });
      },
      updateFreeDepositInfoByContinuePay: async () => {
        const state = get();
        const updateFreeDepositParams: any = updateFreeDepositInfoParams(state);
        get().updateFreeDepositInfo({ ...updateFreeDepositParams });
      },
      setPriceDetailModalVisible: (data: any) => {
        set(state => {
          state.priceDetailModalVisible = data;
        });
      },
      depositPayOnline: async () => {
        const state = get();
        const orderId = getOrderId(state);
        const { freeDeposit, vendorInfo } = state;
        const payParams = getDepositPayOnlineParams(
          orderId,
          freeDeposit?.preAmountForPayOnline,
          vendorInfo?.bizVendorCode,
        );

        Loading.showMaskLoading({
          cancelable: false,
        });

        try {
          const payRes = await MiddlePay({
            params: payParams,
            scene: PayScene.OrderOnlinePayDeposit,
          });
          const { success } = payRes;
          if (success) {
            get().fetchOrder2();
          } else {
            xShowToast({ title: '支付失败', duration: 3000 });
          }
        } catch (e) {
          xShowToast({ title: '支付失败', duration: 3000 });
        }

        Loading.hideMaskLoading();
      },
      setOrderDetailConfirmModalVisible: (data: any) => {
        set(state => {
          state.orderDetailConfirmModalVisible = data;
        });
      },
      queryOrderInsAndXProductCallBack: (data: any) => {
        set(state => {
          state.newOrderInsAndXRes = data;
        });
      },
      queryOrderCancelInfo: async (data: any) => {
        const { callback } = data || {};
        const state = get();
        const request = {
          orderId: getOrderId(state),
          extraMaps: {
            osdModifyOrderVersion:
              state?.orderBaseInfo?.attr?.osdModifyOrderVersion || '',
          },
        };
        const cancelInfo = await CarFetch.cancelInfo(request).catch(() => {});
        await get().queryOrderCancelInfoCallBack(cancelInfo);
        callback?.();
      },
      queryOrderCancelInfoCallBack: (data: any) => {
        set(state => {
          state.isFetchCancelInfoLoading = false;
          state.orderCancelInfo = data;
        });
      },
      setContinuePayFailDiaLogInfo: (data: any) => {
        set(state => {
          state.continuePayFailDialogInfo = data;
        });
      },
      handleContinuePayFailDialogCancel: async () => {
        // 1、主动取消订单
        get().toCancelBook({
          get reason() {
            return '继续支付失败取消';
          },
        });
        // 2、关闭弹窗
        get().setContinuePayFailDiaLogInfo({ visible: false });
        // 3、回到首页
        const curState = get();
        const { pickupStore, returnStore } = curState;
        const rentalLocationAndDate = getNewHomeParamFromOrder(
          pickupStore,
          returnStore,
        );
        // 若订单的租车时间已过期，则重新取初始化时间
        if (dayjs(rentalLocationAndDate.rentalDate.pickup).isBefore(dayjs())) {
          rentalLocationAndDate.rentalDate = {
            pickup: getInitPTime(),
            dropoff: getInitRTime(),
          };
        }
        const rentalDate = {
          pickUp: {
            dateTime: dayjs(rentalLocationAndDate.rentalDate.pickup).format(
              'YYYYMMDDHHmmss',
            ),
          },
          dropOff: {
            dateTime: dayjs(rentalLocationAndDate.rentalDate.dropoff).format(
              'YYYYMMDDHHmmss',
            ),
          },
        };
        const data = {
          rentalLocation: rentalLocationAndDate.rentalLocation,
          rentalDate,
        };
        const baseUrl = Platform.CAR_CROSS_URL.CTQHOME.OSD;
        const url = `${baseUrl}&data=${encodeURIComponent(
          JSON.stringify(data),
        )}`;
        xRouter.navigateTo({ url });
      },
      queryFirstScreenData: async (data: any) => {
        const { orderId, isAppLoad } = data ?? {};
        const res = await CarFetch.getOrderDetailFirstScreenData({
          orderId,
        }).catch(() => {});
        // if (__DEV__) {
        //   // mock首屏接口
        //   const mockInfo = require('./mock');
        //   res = mockInfo.dzfddsecondRes;
        // }
        const state = get();
        const isSuccess = res?.baseResponse?.isSuccess;
        if (state.queryOrderApiStatus !== QueryOrderApiStatusType.success) {
          if (isSuccess) {
            state.fetchOrderSuccess({
              response: res,
              queryOrderApiStatus: QueryOrderApiStatusType.success,
            });
          }
        }
        if (!isAppLoad) {
          state.fetchOrder2({ orderId });
        }
      },
      fetchOrderDataByGraphql: async (data: any) => {
        const { orderId } = data || {};
        const state = get();
        const paramsWithOrderId = { orderId };
        get().queryOrderWarningInfo();
        const getEasyLifeTagInfoParams = {
          ...getReqOrderParams(state),
          timeout: 120,
        };
        // 获取供应商资质数据
        const pageId = AppContext.PageInstance.getPageId();
        // 获取对应的售后客服地址
        const getcustomerserviceParams = !state?.customerServiceUrl
          ? {
              orderId,
              pageId,
              preSale: 0,
              basicRequest: {},
              abTest: 'A', // 服务端接收参数,A始终代表新版,B始终代表旧版
            }
          : null;
        const requestId = BbkUtils.uuid();
        try {
          const variables = {
            queryAdditionPayment: paramsWithOrderId,
            queryPriceInfo: paramsWithOrderId,
            getEasyLifeTagInfo: getEasyLifeTagInfoParams,
            getcustomerservice: getcustomerserviceParams,
            querySimilarVehicle: paramsWithOrderId,
          };
          const timeStart = new Date().getTime();

          const graphqlQueries = getQuerySchema(variables);

          const actionType = 'Order_SecondFetch_UseGraphql';

          // log
          CarLog.LogTraceDev({
            key: LogKeyDev.c_car_trace_graphql,
            info: {
              graphqlQueries,
              requestId,
              actionType,
              isActionTypeNull: !actionType,
              expPoint: 'OrderDetail-FETCH_ORDER_DATA_BY_GRAPHQL',
              eventResult: true,
            },
          });

          if (!actionType) return;
          // @ts-ignore
          const graphqlResult: any = await graphqlFetch(graphqlQueries, {
            variables,
            requestId,
          });

          // if (__DEV__) {
          //   // mock 补款入口
          //   const mockInfo = require('./mock');
          //   graphqlResult = mockInfo.graphqlRes;
          // }

          const timeEnd = new Date().getTime();
          fetchMetricLog({
            url: graphqlPath,
            isSuccess: true,
            networkCost: timeEnd - timeStart,
            requestId,
            actionType,
            pageId,
            expPoint: 'OrderDetail-FETCH_ORDER_DATA_BY_GRAPHQL',
            eventResult: true,
          });
          const {
            // eslint-disable-next-line @typescript-eslint/no-shadow
            queryAdditionPayment,
            getEasyLifeTagInfo,
            getcustomerservice,
            querySimilarVehicle,
          } = graphqlResult;
          get().setSelectorData({
            additionPaymentInfo: queryAdditionPayment,
            easyLifeTags: getEasyLifeTagInfo?.easyLifeTag || [],
            similarVehicleInfo: querySimilarVehicle,
          });

          const serviceUrl = getcustomerservice?.url
            ? getcustomerservice?.url
            : Utils.getRequestUrl(
                `/webapp/livechath5/chat?GroupCode=aftercar4&origin=250002&version=3.1&orderid=${orderId}&case=-1&platform=1&exinfo=&productid=`,
              );
          get().fetchCustomerServiceUrlSuccess({
            customerServiceUrl: serviceUrl,
          });
        } catch (error: any) {
          fetchMetricLog({
            url: graphqlPath,
            isSuccess: false,
            requestId,
            error: Utils.composeError2String(error),
            actionType: 'Order_SecondFetch_UseGraphql',
            pageId,
            eventResult: false,
            expCode: Utils.getFrontEndExpCode(null, error),
            expMsg: Utils.getErrorMessage(error),
          });
        }
      },
      setContinuePayInterceptionData: (data: any) => {
        set(state => {
          state.continuePayInterceptionData = data;
        });
      },
      toCancelCallBack: (data: any) => {
        set(state => {
          state.isPenaltyChange = data?.isPenaltyChange;
          state.penaltyChangeTip = data?.penaltyChangeTip;
          state.penaltyChangeCancelTip = data?.penaltyChangeCancelTip;
        });
      },
      setTravelLimitSelectedResult: (data: any) => {
        set(state => {
          state.travelLimitSelectedResult = data;
        });
      },
      fetchModifyCrossLocation: async (data: any) => {
        Loading.showMaskLoading({
          cancelable: false,
        });
        // @ts-ignore
        const { selectedResult, callback } = data || {};
        const state = get();
        const { orderId } = getReqOrderParams(state);
        const crossLocations =
          selectedResult?.map(item => ({
            crossType: ICrossType.CrossCountry,
            crossId: item.regionId,
          })) || [];
        const params = {
          orderId,
          crossLocations,
        };
        const res = await CarFetch.modifyCrossLocation(params).catch(() => {});
        Loading.hideMaskLoading();
        if (res?.baseResponse?.isSuccess) {
          get().setTravelLimitSelectedResult(selectedResult);
          if (typeof callback === 'function') {
            callback();
          }
          return;
        }
        UIToast.show('当前网络不佳，请稍后重试');
      },
      setFlightDelayRulesModalVisible: (data: any) => {
        set(state => {
          state.flightDelayRulesModalVisible = data;
        });
      },
      queryExtraInsurance: async (data: any) => {
        Loading.showMaskLoading({
          cancelable: false,
        });
        // @ts-ignore
        const { callback } = data || {};
        const state = get();
        const { orderId } = getReqOrderParams(state);
        const params = {
          orderId,
        };
        const res = await CarFetch.queryExtraInsurance(params).catch(() => {});
        Loading.hideMaskLoading();
        if (res?.baseResponse?.isSuccess) {
          if (typeof callback === 'function') {
            callback(res?.platformInsuranceOrderExtraList);
          }
          return;
        }
        UIToast.show('当前网络不佳，请稍后重试');
      },
      setIsQueryOrderLoading: (data: boolean) =>
        set({ isQueryOrderLoading: data }),
      createPreFetch: async (data: any) => {
        const parameter = getApiQueryGuideFetchParam(data, undefined);
        if (parameter) {
          const fetchFun = CarFetch.queryOrderDetailStoreGuide;
          fetchFun(parameter);
        }
      },
      setFulfillmentData: (data: any) => {
        set(state => {
          state.fulfillmentData = data;
        });
      },
      checkSubmitReturnCar: async (data: any) => {
        const { orderId } = data || {};
        const params = {
          orderId,
        };

        const response = await CarFetch.checkSubmitReturnCar(params);

        if (
          response?.checkItemList?.includes(
            CheckSubmitReturnCarCode.returnCarDistanceInvalidateFail,
          )
        ) {
          get().setDistance(response?.distance || 500);
          get().setOrderModalsVisible({
            distanceInvalidateModal: {
              visible: true,
            },
          });
        } else {
          xRouter.navigateTo({
            url: `${Url.rNXTaroCarOrderBaseUrl}&initialPage=${SelfServicePageName.DROP_OFF_UPLOAD_STATUS}&orderId=${orderId}`,
          });
        }
      },
      setDistance: (data: any) => {
        set(state => {
          state.distance = data;
        });
      },
      setDidNoticeData: (data: any) => {
        set(state => {
          state.didNoticeData = data;
        });
      },
      queryDidNoticeData: async (data: any) => {
        const { orderId } = data || {};
        const params = {
          orderId,
        };

        const response = await CarFetch.queryOrderNoticeFromDid(params);
        // if (__DEV__) {
        //   // mock 补款入口
        //   const mockInfo = require('./mock');
        //   response = mockInfo.mdxxRes;
        // }
        if (response?.baseResponse?.isSuccess) {
          const { noticeList, noticeTitle, history } = response;
          get().setDidNoticeData({ noticeList, noticeTitle, history });
        }
      },
      setLocationAndDatePopIsShow: (data: any) => {
        set(state => {
          state.locationDatePopVisible = data.visible;
        });
      },
      queryOsdModifyOrderNote: async () => {
        const state = get();
        const { response } = await CarFetch.queryOsdModifyOrderNote({
          orderId: getOrderId(state),
        });
        if (response?.baseResponse?.isSuccess) {
          get().setOsdModifyOrderNote({ osdModifyOrderNote: response });
        }
      },
      setOsdModifyOrderNote: (data: any) => {
        set(state => {
          state.osdModifyOrderNote = data.osdModifyOrderNote;
        });
      },
      osdModifyOrderInit: async () => {
        const state = get();
        const { pickupStore, returnStore, driverInfo } = state;
        const orderInfo = getOrderInfo(pickupStore, returnStore, driverInfo);
        // 设置取还时间地点
        locationAndDateStore.getState().setLocationDateInfo(orderInfo);
        const customerAge = orderInfo?.age;
        const { AgeConfig } = OrderDetail;
        if (customerAge) {
          let curAge = customerAge;
          const age = Number(customerAge);
          if (age < AgeConfig.MIN_AGE) {
            curAge = AgeConfig.MIN_AGE.toString();
          } else if (age > AgeConfig.MAX_AGE) {
            curAge = AgeConfig.MAX_AGE.toString();
          } else if (
            age >= AgeConfig.DEFAULT_AGE.min &&
            age <= AgeConfig.DEFAULT_AGE.max
          ) {
            curAge = AgeConfig.DEFAULT_AGE.getVal();
          }
          // 设置年龄
          driverAgeAndNumberStore.getState().setAge(curAge);
        }
      },
      queryCountrysInfo: async () => {
        const state = get();
        const { pickUpCountryInfo, pickupStore } = state;
        if (pickUpCountryInfo?.areaCode) return;
        const { countryId } = pickupStore || {};
        const param = {
          showGAT: true, // 返回港澳台
          dataSource: 'OCH', // 固定用车数据源
          extraTags: { withCountrySuffix: '1' },
        };

        const res = await CarFetch.queryAppCountryIdList(param).catch(() => {});

        const countryInfo = res?.countries?.find(
          item => item?.countryId === countryId,
        );
        get().setCountrysInfo(countryInfo);
      },
      setCountrysInfo: (data: any) => {
        set(state => {
          state.pickUpCountryInfo = data;
        });
      },
      queryOrderWarningInfo: async () => {
        const { fetchListWarningInfo } = commonStore.getState();
        const { pickupStore, returnStore } = get();
        fetchListWarningInfo({
          pageIndexId: PageIndexId.Order,
          locationAndDate: {
            pickUpTime: pickupStore?.localDateTime,
            dropOffTime: returnStore?.localDateTime,
            pickUpCityId: pickupStore?.cityId,
            dropOffCityId: returnStore?.cityId,
          },
        });
      },
      handleContinuePayFail: response => {
        const { resultMessage, tipType, resultCode } = response || {};
        if (tipType === 4) {
          get().setContinuePayFailDiaLogInfo({
            visible: true,
            type:
              resultCode === ContinuePayResultCode.VerificationFailed
                ? 'alert'
                : 'confirm',
            content: resultMessage,
          });
        } else {
          UIToast.show(resultMessage, 2);
        }
      },
    })),
  ),
);

export const mapIsuranceBoxOsd = (
  platformInsurance,
  vendorInfo,
  vehicleInfo,
) => {
  const vendorCode = vendorInfo?.bizVendorCode;
  const groupName = vehicleInfo?.vehicleGroupName;
  const newPackageInfos: any = [];
  const { packageInfos } = platformInsurance ?? {};
  const packageItem = packageInfos?.[0];

  if (packageItem) {
    const curProduct = platformInsurance;
    const { insuranceItems: curInsuranceItems, briefInsuranceItems } =
      curProduct || {};
    const insNameAndExcess: any = [];
    const includeInsCode: any = [];
    // 不包含的保险
    const unIncludeInsNameAndExcess: any = [];
    // 所有的保险
    const allInsNameAndExcess: any = [];
    // 出境自营险一级页面的保险
    const summaryInsNameAndExcess: any = [];

    // 一级页面节点
    briefInsuranceItems?.forEach(briefItem => {
      const curInsItem = briefItem.insuranceDetail?.[0];
      const {
        minExcess,
        maxExcess,
        coverageWithPlatformInsuranceV2,
        coverageWithoutPlatformInsuranceV2,
      } = curInsItem || {};
      // 是否是0起赔额
      const isZeroExcess = minExcess === 0 && maxExcess === 0;
      // 起赔额拼接文案
      const excessLabel = coverageWithoutPlatformInsuranceV2
        ? ` ${coverageWithoutPlatformInsuranceV2}`
        : '';
      // 保险名称拼接起赔额
      const insuranceName = `${briefItem.name}${
        isZeroExcess ? '' : excessLabel
      }`;

      const curNameAndExcess = {
        code: briefItem.code,
        // 非自营险时，拼接起赔额文案
        name: briefItem.isFromCtrip ? briefItem.name : insuranceName,
        isInclude: briefItem.isInclude,
        // 0起赔额绿色文案
        excessShortDescNew: isZeroExcess ? Texts.zeroExcessDesc : '',
        // 是否是0起赔额
        isZeroExcess,
        // 自营险展示灰色标签
        label: briefItem.isFromCtrip ? coverageWithPlatformInsuranceV2 : '',
        // 自营险展示灰色标签
        labelDescription: briefItem.isFromCtrip
          ? coverageWithoutPlatformInsuranceV2
          : '',
        isFromCtrip: briefItem.isFromCtrip,
        itemUrl: briefItem.itemUrl,
        insuranceStatus: briefItem.insuranceStatus,
        insuranceStatusName: briefItem.insuranceStatusName,
        insuranceStatusDesc: briefItem.insuranceStatusDesc,
        // 自营险展示的详细描述
        description: briefItem.description,
      };
      summaryInsNameAndExcess.push(curNameAndExcess);
    });

    // 是否有起赔额
    let isHasExcess = 0;
    curInsuranceItems?.forEach(insItem => {
      let curInsItem: any = null;
      // 如果是海外自营险，则取第一条保险，因为保险可以属于多个套餐，服务端不好增加packageId
      if (insItem.isFromCtrip) {
        curInsItem = insItem?.insuranceDetail?.[0];
      } else {
        curInsItem = insItem?.insuranceDetail?.find(
          f => f.packageId === packageItem.defaultPackageId,
        );
      }
      const {
        minExcess,
        maxExcess,
        excessShortDesc,
        coverageWithoutPlatformInsuranceV2 = '',
        coverageWithPlatformInsuranceV2 = '',
      } = curInsItem || {};
      const isZeroExcess = minExcess === 0 && maxExcess === 0;
      const hasExcess = minExcess >= 0 || maxExcess >= 0;
      // 有起赔额
      if (hasExcess) {
        isHasExcess += 1;
      }

      // 详情描述
      const modalLabel: any = [];
      // 拼接保险弹窗描述
      let labelContent = insItem.isFromCtrip
        ? Texts.insuranceCompanyByCar
        : Texts.includedByCar;
      // 拼接短描述
      if (insItem.description) {
        labelContent += `：${insItem.description}`;
      }
      // 自营险拼接起赔额(起赔额为0不拼接起赔额)
      const isAppendModalLabel =
        insItem.isAddFulCoverage && !insItem.isFromCtrip;
      if (isAppendModalLabel && coverageWithoutPlatformInsuranceV2) {
        labelContent += `${
          insItem.description ? '，' : '：'
        }${coverageWithoutPlatformInsuranceV2}`;
      }
      modalLabel.push(labelContent);
      if (isAppendModalLabel && coverageWithPlatformInsuranceV2) {
        modalLabel.push(coverageWithPlatformInsuranceV2);
      }
      // 起赔额拼接文案
      const excessLabel = coverageWithoutPlatformInsuranceV2
        ? ` ${coverageWithoutPlatformInsuranceV2}`
        : '';
      // 保险名称拼接起赔额
      const insuranceName = `${insItem.name}${
        insItem.isFromCtrip || isZeroExcess ? '' : excessLabel
      }`;

      const curNameAndExcess = {
        code: insItem.code,
        name: insuranceName,
        groupCode: insItem.groupCode,
        excessShortDesc,
        isInclude: insItem.isInclude, // 订单详情页的保险都属于包含项
        // 0起赔额绿色文案
        excessShortDescNew: minExcess === 0 ? Texts.zeroExcessDesc : '',
        // 是否是0起赔额
        isZeroExcess,
        // 自营险展示灰色标签
        label: insItem.isFromCtrip ? coverageWithPlatformInsuranceV2 : '',
        // 自营险展示灰色标签
        labelDescription: insItem.isFromCtrip
          ? coverageWithoutPlatformInsuranceV2
          : '',
        // 详情弹窗灰色标签
        modalLabel,
        isFromCtrip: insItem.isFromCtrip,
        itemUrl: insItem.itemUrl,
        insuranceStatus: insItem.insuranceStatus,
        insuranceStatusName: insItem.insuranceStatusName,
        insuranceStatusDesc: insItem.insuranceStatusDesc,
      };
      if (insItem.isInclude) {
        insNameAndExcess.push(curNameAndExcess);
        includeInsCode.push(insItem.code);
      } else {
        unIncludeInsNameAndExcess.push(curNameAndExcess);
      }
      allInsNameAndExcess.push(curNameAndExcess);
    });

    const logInfo = {
      guaranteePkgName: packageItem.packageName,
      insuranceId: includeInsCode,
      vendorCode,
      groupName,
    };

    // 将所有保险按groupCode进行分组
    const groupInsNameAndExcess = {};
    allInsNameAndExcess.forEach(ins => {
      if (!groupInsNameAndExcess[ins.groupCode]) {
        groupInsNameAndExcess[ins.groupCode] = [];
      }
      groupInsNameAndExcess[ins.groupCode].push(ins);
    });
    newPackageInfos.push({
      ...packageItem,
      allInsNameAndExcess,
      summaryInsNameAndExcess,
      groupInsNameAndExcess,
      insNameAndExcess,
      unIncludeInsNameAndExcess,
      isHasExcess,
      logInfo,
    });
  }
  return {
    packageInfos: newPackageInfos,
  };
};

export const getStorePolicyProps = (expandIds = []) => {
  const { carRentalMustRead, rentalMustReadTable, rentalMustReadPicture } =
    orderDetailStore.getState();
  return getBbkStorePolicyProps(expandIds, {
    carRentalMustRead,
    rentalMustReadTable,
    rentalMustReadPicture,
  });
};

// 订详跳保代处理
export const handleOrderDetailGoToIns = ({ eventName, addInsParams }) => {
  const { setOrderModalsVisible } = orderDetailStore.getState();
  InsuranceConfirmUtil.goToInsConfirmPage({
    eventName,
    reqParams: addInsParams,
    setCreateInsLoadingIsShow: visible => {
      setOrderModalsVisible({
        createInsModalVisible: {
          visible,
          data: {
            title: InsMsg.createInsuranceTitle,
            content: InsMsg.createInsuranceCont,
          },
        },
      });
    },
    setCreateInsFailPopIsShow: visible => {
      setOrderModalsVisible({
        insFailedModalVisible: {
          visible,
          data: {
            content: InsMsg.addInsFailModalContent,
            showPayBtn: false,
          },
        },
        createInsModalVisible: { visible: false },
      });
    },
    getCreateInsLoadingIsShow: () =>
      orderDetailStore(
        state => state.modalsVisible?.createInsModalVisible?.visible,
      ),
    callbackFun: () => {},
  });
};

// 继续支付
export const ctripContinuePay = async (data, goToInsFun) => {
  const curState = orderDetailStore.getState();
  const {
    queryOrderPriceInfoCallBack,
    setContinuePayInterceptionData,
    setOrderModalsVisible,
    fetchCarAssistantCallBack,
    continuePayment,
    handleContinuePayFail,
  } = curState;
  const orderId = getOrderId(curState);
  // eslint-disable-next-line global-require
  const sesameStore = require('./sesame').default;
  const authOrderCount = sesameStore(state => state.authOrderCount);
  Loading.showMaskLoading();
  const { confirmInsuranceData } = data;
  const { ctripInsuranceInfos, insuranceAndXProduct, driverInfo } = curState;
  const buyedInsurances: any =
    getHadBuyInsurances(ctripInsuranceInfos, insuranceAndXProduct) || [];
  const buildInsuranceParams = getInsConfirmReqParam(
    driverInfo,
    buyedInsurances,
  );
  const insuranceFlag = commonStore(
    state => state.qConfigResponse?.insuranceFlag,
  );
  // 跳转保险代理页
  if (buildInsuranceParams && insuranceFlag && !confirmInsuranceData) {
    Loading.hideMaskLoading();
    goToInsFun(buildInsuranceParams);
    CarLog.LogCode({
      name: '点击_订详_跳保代页面',

      info: { buildInsuranceParams },
    });
    return;
  }
  const continuePayParams: any = queryContinuePayParams(
    curState,
    confirmInsuranceData,
  );
  // 2021-12-14 是否反选了保险
  const unSelectIns =
    confirmInsuranceData &&
    confirmInsuranceData?.data?.selectedInsuranceList?.length === 0;
  // 2021-12-14 到店付的订单加购自营险后变成预付定金, 反选自营险后无需跳支付

  try {
    const taskQueryPriceInfo = unSelectIns
      ? CarFetch.queryOrderPriceInfo({ orderId })
      : null;
    // 2021-12-14 反选自营险后，需要同时调继续支付接口和价格接口，以保证跳支付页时，支付页披露的费用明细不再含自营险费用
    const continuePayRequest = CarFetchHelper.parameterBuilder({
      param: continuePayParams,
      verifyResponseIsSuccess: res => !(res.resultCode === '0' || !!res.rst),
    });
    const result = await Promise.all([
      CarFetch.ctripContinuePay(continuePayRequest),
      taskQueryPriceInfo,
    ]);

    const [response, priceInfoRes] = result;
    if (unSelectIns) {
      queryOrderPriceInfoCallBack({
        orderDetailPrice: {
          ...priceInfoRes?.feeDetailInfo,
          queryOrderDetailPriceLoaded: true,
        },
      });
    }
    Loading.hideMaskLoading();
    // 重复下单拦截
    setContinuePayInterceptionData(null);
    const { resultCode, resultMessage, strongSubmit } = response;
    if (
      [
        OrderCheckResultCode.weakInterception,
        OrderCheckResultCode.strongInterception,
      ].includes(resultCode)
    ) {
      setContinuePayInterceptionData({
        visible: true,
        resultCode,
        resultMsg: resultMessage,
        requestParams: data,
        strongSubmit,
      });
      return;
    }

    if (response.resultCode === '-1') {
      // 如果是芝麻重复订单执行回调
      const alipayTip =
        authOrderCount === 1
          ? '您已经有一笔信用免押订单在使用中，如果要继续支付这笔信用免押订单，需要去授权第二笔信用免押'
          : '如果要继续支付这笔信用免押订单，您需要去授权芝麻分';

      setOrderModalsVisible({
        sesameRepeatOrderModal: {
          visible: true,
          data: {
            title: alipayTip,
            get submitTxt() {
              return '去授权';
            },
            get cancelTxt() {
              return '再看看';
            },
          },
        },
      });
    } else if (response.resultCode === '0' || !!response.rst) {
      fetchCarAssistantCallBack({
        isdContinuePayAmount: response.amt,
      });
      continuePayment();
    } else {
      // 2022-8-17 继续支付失败文案优化处理
      handleContinuePayFail(response);
    }
  } catch (err) {
    UIToast.show('支付失败，请稍后重试');
  }
};

export const getGuidePageParam = (guideTabId, isHidePhone = false) => {
  const { pickupStore, returnStore, reqOrderParams, orderBaseInfo } =
    orderDetailStore.getState();

  return {
    pickupStart: getStartInfo(pickupStore),
    dropoffStart: getStartInfo(returnStore),
    pickupStoreId: pickupStore?.storeCode,
    dropoffStoreId: returnStore?.storeCode,
    selectedId: guideTabId,
    rentCenterId: 0,
    pickupServiceType: 0, // 是否是送车上门
    dropoffServiceType: 0, // 是否是上门取车
    pStoreWay: '',
    rStoreWay: '',
    fixMap: true,
    isHidePhone,
    orderId: reqOrderParams?.orderId,
    orderStatus: orderBaseInfo?.orderStatus?.orderStatus,
    pickupStoreGuide: pickupStore?.storeGuide, // 2023-3-21 境外售后地图指引页中的取还车指引取OSDQueryOrder接口返回的
    returnStoreGuide: returnStore?.storeGuide,
  };
};

export default orderDetailStore;
