/* eslint-disable no-param-reassign */
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { get as lodashGet } from 'lodash-es';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import Loading from '@c2x/apis/Loading';
import { xShowToast, xRouter } from '@ctrip/xtaro';
import { actionErrorCatcher } from '../../__Middleware/ZustandMiddleware';
import {
  CarFetch,
  AppContext,
  Utils,
  CarLog,
  CarStorage,
} from '../../../Util/Index';
import { Platform, LogKeyDev, StorageKey } from '../../../Constants/Index';
import {
  SesameGuiderType,
  OrderStatusCtrip,
} from '../../../Constants/OrderDetail';
import {
  ModifyStatusType,
  ModifyTipInfoCodeType,
  ModifyType,
} from '../../../Types/Dto/OrderDetailRespaonseType';
import orderDetailStore from './orderdetail';
import locationAndDateStore from './locationAndDate';
import driverlistStore from './driverList';
import { TitleHightlightType } from '../enum';
import Texts from '../Texts';

// #region

export const getRebookUrl = ({
  rentalLocation,
  rentalDate,
  orderId,
  vendorInfo,
  vehicleInfo,
  pickupStore,
  landingToPage = 'rebookhome',
  passenger,
  priceInfo,
}) => {
  const dateFormat = 'YYYYMMDDHHmmss';
  const locationAndDateParams = {
    rentalDate: {
      pickUp: {
        dateTime: dayjs(rentalDate?.pickUp?.dateTime).format(dateFormat),
      },
      dropOff: {
        dateTime: dayjs(rentalDate?.dropOff?.dateTime).format(dateFormat),
      },
    },
    rentalLocation: {
      pickUp: rentalLocation?.pickUp,
      dropOff: rentalLocation?.dropOff,
      isShowDropOff: rentalLocation?.isShowDropOff,
      isNotShowDropOff: rentalLocation?.isShowDropOff,
    },
    isShowDropOff: rentalLocation?.isShowDropOff,
  };

  const rebookParams = {
    ctripOrderId: orderId,
    vendorId: vendorInfo.vendorID,
    vehicleId: vehicleInfo.vendorVehicleID,
    ctripVehicleCode: vehicleInfo.ctripVehicleID,
    storeCode: pickupStore.storeID,
  };

  const opParams = {
    encryptUid: AppContext?.encryptUid,
    fromType: AppContext?.fromType,
    originOrderId: orderId,
    channelId: AppContext?.channelId,
    eid: AppContext?.eid,
    // TODO: test param
    // @ts-ignore
    env: AppContext?.Env,
    modifyVendorOrderCode: vendorInfo.vendorConfirmCode,
    originVendorId: vendorInfo.vendorID,
    originalCouponCode: lodashGet(priceInfo, 'couponInfos[0].code', ''),
    originalActivityId: priceInfo?.activityInfo?.activityId || '',
    originalActivityName: priceInfo?.activityInfo?.subTitle || '',
  };

  const paramsQuery = Utils.changeObject2QueryString({
    ...rebookParams,
    ...opParams,
  });

  const basePlatform = Platform.CAR_CROSS_URL.REBOOK.ISD;
  const baseUrl = `${basePlatform}st=client&fromurl=common&apptype=ISD_C_APP`;
  return (
    `${baseUrl}&data=${encodeURIComponent(
      JSON.stringify(locationAndDateParams),
    )}` +
    `&landingto=${landingToPage}&${paramsQuery}` +
    `&passenger=${encodeURIComponent(JSON.stringify(passenger))}`
  );
};

class ModifiedStorage {
  key: any = null;

  constructor({ key }) {
    this.key = key;
  }

  load = async () => {
    const storagedStr = await CarStorage.load(this.key);
    return storagedStr ? JSON.parse(storagedStr) : [];
  };

  loadOne = async (orderId?: number) => {
    const storaged = await this.load();
    return storaged.find(v => v.orderId === orderId)?.isShowed || false;
  };

  save = async (orderId: number) => {
    const storaged = await this.load();
    let index = storaged?.findIndex?.(v => v.orderId === orderId);
    if (index < 0) index = storaged.length;
    storaged[index] = { isShowed: true, orderId };
    CarStorage.save(this.key, JSON.stringify(storaged));
  };

  remove = async (orderId: number) => {
    const storaged = await this.load();
    const index = storaged?.findIndex?.(v => v.orderId === orderId);
    storaged.splice?.(index, 1);
    CarStorage.save(this.key, JSON.stringify(storaged));
  };
}

export const ModifiedPopStorage = new ModifiedStorage({
  key: StorageKey.SHOWED_MODIFY_ORDER_POP,
});

// 用于处理各个State下的Mapper里面的reSelect函数
// 从订单信息中获取新首页的参数
export const getNewHomeParamFromOrder = (pickupStore, returnStore) => {
  const pickupLocation = lodashGet(pickupStore, 'location');
  const dropoffLocation = lodashGet(returnStore, 'location');
  const pickUpLat =
    pickupStore.userLatitude || lodashGet(pickupLocation, 'poiInfo.latitude');
  const pickUpLng =
    pickupStore.userLongitude || lodashGet(pickupLocation, 'poiInfo.longitude');
  const dropOffLat =
    returnStore.userLatitude || lodashGet(dropoffLocation, 'poiInfo.latitude');
  const dropOffLng =
    returnStore.userLongitude ||
    lodashGet(dropoffLocation, 'poiInfo.longitude');
  return {
    rentalLocation: {
      pickUp: {
        cid: pickupStore.cityId || lodashGet(pickupLocation, 'city.id'),
        cname: pickupStore.cityName || lodashGet(pickupLocation, 'city.name'),
        country: lodashGet(pickupLocation, 'country.name') || '中国',

        isDomestic: false,
        area: {
          id: pickupLocation?.locationCode || '',
          name:
            pickupStore.userAddress ||
            lodashGet(pickupLocation, 'locationName'),
          lat: pickUpLat,
          lng: pickUpLng,
          type: pickupLocation?.locationType || 1,
        },
      },
      dropOff: {
        cid: returnStore.cityId || lodashGet(dropoffLocation, 'city.id'),
        cname: returnStore.cityName || lodashGet(dropoffLocation, 'city.name'),
        country: lodashGet(dropoffLocation, 'country.name') || '中国',

        isDomestic: false,
        area: {
          id: dropoffLocation?.locationCode || '',
          name:
            returnStore.userAddress ||
            lodashGet(dropoffLocation, 'locationName'),
          lat: dropOffLat,
          lng: dropOffLng,
          type: dropoffLocation?.locationType || 1,
        },
      },
      isShowDropOff: !(pickUpLat === dropOffLat && pickUpLng === dropOffLng),
      // 不在 LocationAndDate Reducer setLocationInfo 中进行还车地点的覆盖
      isNotShowDropOff: !(pickUpLat === dropOffLat && pickUpLng === dropOffLng),
    },
    rentalDate: {
      pickup: pickupStore.localDateTime,
      dropoff: returnStore.localDateTime,
    },
  };
};

export const getRebookPassenger = (passenger, mobile, certificate) => ({
  fullName: passenger?.fullName,
  mobile,
  certificateList: [
    {
      certificateType: String(certificate?.certificateType),
      certificateNo: certificate?.certificateNo,
    },
  ],
});

// #endregion

type State = {
  isLoading: boolean;
  isMaskLoading: boolean;
  isFail: boolean;
  modifyOrderWarnModalVisible: boolean;
  modifyOrderWarnModalProps: any;
  modifyOrderReqeust: any;
  modifyOrderResponse: any;
  modifyOrderSuccess: boolean;
  rebookParams: any;
  rebookParamsOsd: any; // 境外修改订单参数缓存
  isPassengerLoaded: boolean;
};

type Actions = {
  setLoading: (data: any) => void;
  setIsFail: (data: any) => void;
  reBooking: (data: any) => void;
  clear: () => void;
  setModifyOrderWarnModalVisible: (data: any) => void;
  modifyOrderSuccessFn: (data: any) => void;
  cancelModifyOrder: (data: any) => void;
  setModifyOrderResponse: (data: any) => void;
  setInitialDataCallback: (data: any) => void;
};

const initialState: State = {
  isLoading: false,
  isMaskLoading: false,
  isFail: false,
  modifyOrderWarnModalVisible: false,
  modifyOrderWarnModalProps: null,
  modifyOrderReqeust: null,
  modifyOrderResponse: null,
  modifyOrderSuccess: false,
  rebookParams: null,
  rebookParamsOsd: null, // 境外修改订单参数缓存
  isPassengerLoaded: false,
};

const modifyOrder = create<State & Actions>()(
  actionErrorCatcher(
    immer<State & Actions, [], []>((set, get) => ({
      ...initialState,
      setLoading: (data: any) => {
        set(state => {
          state.isLoading = data;
        });
      },
      setIsFail: (data: any) => {
        set(state => {
          state.isFail = data;
        });
      },
      reBooking: (data: any) => {
        const {
          reqOrderParams,
          vendorInfo,
          vehicleInfo,
          pickupStore,
          returnStore,
          orderDetailPrice,
        } = orderDetailStore.getState();
        const {
          rentalLocation: rentalLocationState,
          rentalDate: rentalDateState,
        } = locationAndDateStore.getState();
        const isInfoFromOrder = data;
        const { orderId } = reqOrderParams || {};
        // 从redux拿数据, 跳转列表页
        const isShowDropOff =
          rentalLocationState?.pickUp?.cid ===
            rentalLocationState?.dropOff?.cid &&
          rentalLocationState?.pickUp?.area?.name ===
            rentalLocationState?.dropOff?.area?.name;
        let landingToPage = 'list';
        let rentalLocation: any = { ...rentalLocationState, isShowDropOff };
        let rentalDate: any = rentalDateState;
        if (isInfoFromOrder) {
          const newHomeParam = getNewHomeParamFromOrder(
            pickupStore,
            returnStore,
          );
          // 从订单拿取还车数据, 跳转搜索页
          const {
            rentalLocation: rentalLocationOrder,
            rentalDate: rentalDateOrder,
          } = newHomeParam;
          rentalLocation = rentalLocationOrder;
          rentalDate = {
            pickUp: {
              dateTime: rentalDateOrder?.pickup,
            },
            dropOff: {
              dateTime: rentalDateOrder?.dropoff,
            },
          };
          landingToPage = 'rebookhome';
        }
        const { passenger: passengerState } = driverlistStore.getState();
        const passenger = getRebookPassenger(passengerState, '', undefined);
        const priceInfo = orderDetailPrice;
        const url = getRebookUrl({
          rentalLocation,
          rentalDate,
          orderId,
          vendorInfo,
          vehicleInfo,
          pickupStore,
          landingToPage,
          passenger,
          priceInfo,
        });
        xRouter.navigateTo({ url });
      },
      clear: () => {
        set(state => {
          state.isLoading = initialState.isLoading;
          state.isMaskLoading = initialState.isMaskLoading;
          state.isFail = initialState.isFail;
          state.modifyOrderWarnModalVisible =
            initialState.modifyOrderWarnModalVisible;
          state.modifyOrderWarnModalProps =
            initialState.modifyOrderWarnModalProps;
          state.modifyOrderReqeust = initialState.modifyOrderReqeust;
          state.modifyOrderResponse = initialState.modifyOrderResponse;
          state.modifyOrderSuccess = initialState.modifyOrderSuccess;
          state.rebookParams = initialState.rebookParams;
          state.rebookParamsOsd = initialState.rebookParamsOsd;
          state.isPassengerLoaded = initialState.isPassengerLoaded;
          state.isFail = initialState.isFail;
          state.isFail = initialState.isFail;
        });
      },
      setModifyOrderWarnModalVisible: (data: any) => {
        set(state => {
          state.modifyOrderWarnModalVisible = data.visible;
          state.modifyOrderWarnModalProps = data.content;
        });
      },
      modifyOrderSuccessFn: async () => {
        const {
          modifyInfoDto,
          reqOrderParams,
          orderBaseInfo,
          setOrderModalsVisible,
        } = orderDetailStore.getState();
        const tipInfo = modifyInfoDto?.tipInfo;
        const modifyStatus = modifyInfoDto?.modifyStatus;
        const modifyType = modifyInfoDto?.modifyType;
        const { orderId } = reqOrderParams || {};
        const isShowed = await ModifiedPopStorage.loadOne(orderId);

        // 只有原单修改的订单需要弹窗
        if (
          isShowed ||
          !tipInfo ||
          modifyType === ModifyType.reBook ||
          [ModifyStatusType.processing, ModifyStatusType.canceled].includes(
            modifyStatus,
          )
        ) {
          return;
        }
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_trace_modify_order_pop,
          info: {
            modifyInfoDto,
            orderId,
          },
        });
        if (modifyInfoDto?.goDeposit) {
          const tip = tipInfo?.find(
            v => v.code === ModifyTipInfoCodeType.ehiDeposit,
          );

          setOrderModalsVisible({
            ehiModifyOrderModal: {
              visible: true,
              data: {
                get btnText() {
                  return '去信用免押';
                },
                tips: [],
                type: SesameGuiderType.VERIFY_SUCCESS_ISD,
                verifyRes: {
                  boldTitle: tip?.title,
                  subTitle: tip?.content,
                },
              },
            },
          });

          ModifiedPopStorage.save(orderId);
          return;
        }

        const toast = tipInfo?.find(
          v => v.code === ModifyTipInfoCodeType.success,
        );
        if (toast?.content) {
          xShowToast({ title: toast.content, duration: 3000 });
          ModifiedPopStorage.save(orderId);
          return;
        }

        if ([ModifyStatusType.success].includes(modifyStatus)) {
          return;
        }

        if (modifyStatus === ModifyStatusType.payFail) {
          const contentText = tipInfo?.find(
            v => v.code === ModifyTipInfoCodeType.payFail,
          );

          setOrderModalsVisible({
            confirmModal: {
              visible: true,
              isModifyOrder: true,
              data: {
                title: Texts.modifyFailSimple,
                contentText: contentText?.content,
                titleHeightlightStyle: TitleHightlightType.Warning,
              },
            },
          });

          ModifiedPopStorage.save(orderId);
          return;
        }
        const failCancelRule = tipInfo?.find(
          v => v.code === ModifyTipInfoCodeType.failCancelRule,
        );
        const failText = tipInfo?.find(
          v => v.code === ModifyTipInfoCodeType.failText,
        );
        const isOrderCanceled =
          orderBaseInfo?.orderStatusCtrip === OrderStatusCtrip.CANCELLED;
        const content = {
          header: Texts.modifyFail,
          title: failText?.content,
          subTitle: isOrderCanceled ? '' : Texts.modifyFailTip,
          isWarnHeader: true,
          isWarnTip: failCancelRule?.cancelTipColor !== 1,
          tip: failCancelRule?.content,
        };
        get().setModifyOrderWarnModalVisible({
          visible: true,
          content,
        });
        ModifiedPopStorage.save(orderId);
      },
      cancelModifyOrder: async () => {
        const { reqOrderParams, queryOrderDetail } =
          orderDetailStore.getState();
        const { orderId } = reqOrderParams || {};
        Loading.showMaskLoading();
        try {
          const res = await CarFetch.cancelModify(orderId);
          if (res?.errorMsg) {
            xShowToast({ title: res?.errorMsg, duration: 3000 });
          }
          queryOrderDetail({ orderId });
        } catch {
          xShowToast({ title: '网络错误', duration: 3000 });
        }
        Loading.hideMaskLoading();
      },
      setModifyOrderResponse: (data: any) => {
        set(state => {
          state.modifyOrderResponse = data;
          state.modifyOrderSuccess = true;
        });
      },
      setInitialDataCallback: (data: any) => {
        const { isSuccess } = data;
        get().setLoading(false);
        get().setIsFail(!isSuccess);
      },
    })),
  ),
);

export default modifyOrder;
