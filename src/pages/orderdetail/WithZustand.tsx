/* eslint-disable react/destructuring-assignment */
import React from 'react';
import PreSetSomething from '../PreSetSomething';
import { getPushData } from '../../Util/xTaroTools';
import '../SwitchEnv/switchStyle';
import { getUrlPageName } from '../SwitchEnv/urlQuery';
import ErrorBoundary from './Components/Error/ErrorBoundary';
import { AppContext } from '../../Util/Index';
import { EventName } from '../../Constants/Index';
import orderDetailStore, {
  handleOrderDetailGoToIns,
  getRestAssuredTag,
  getCarLabelsInfo,
  getNextStorageCardsTitle,
  getTipsCardInfo,
  getOperationButtons,
  getFeeDetailData,
  mapIsuranceBoxOsd,
  getAdditionalDriverDesc,
  getOrderTraceData,
} from './state/orderdetail';
import {
  EasyLife2024Code,
  OrderStatusCtrip,
} from '../../Constants/OrderDetail';
import vocStore from './state/voc';
import serviceStore from './state/service';
import commonStore from './state/common';
import advanceReturnStore from './state/advanceReturn';
import debugStore from './state/debug';
import modifyOrder from './state/modifyOrder';
import onlineAuthStore from './state/onlineAuth';

const wrapper = (InnerComponent, renderCheckInfo) => {
  const out = React.forwardRef((props, ref) => {
    // @ts-ignore
    if (!global.isPreSetSomething) {
      PreSetSomething(); // props
    }
    orderDetailStore.setState({
      reqOrderParams: {
        orderId: AppContext.UrlQuery.orderId,
      },
    });
    // TODO: @zyr getUrlPageName不接收参数
    // @ts-ignore
    const urlPageName = getUrlPageName(props);
    const pushData = getPushData(urlPageName);
    return (
      <ErrorBoundary>
        <InnerComponent forwardedRef={ref} {...pushData} {...props} />
      </ErrorBoundary>
    );
  });
  // @ts-ignore
  out.renderCheckInfo = renderCheckInfo;
  return out;
};

const zustand = () => {
  return ComponentPage => {
    const ComponentWithRef = (props: any) => {
      const config = commonStore(state => state.qConfigResponse);
      const driverInfo = orderDetailStore(state => state.driverInfo);
      const cancelRuleInfo = orderDetailStore(state => state.cancelRuleInfo);
      const orderBaseInfo = orderDetailStore(state => state.orderBaseInfo);
      const extendedInfo = orderBaseInfo?.attr;
      const orderPriceInfo = orderDetailStore(state => state.orderPriceInfo);
      const vendorInfo = orderDetailStore(state => state.vendorInfo);
      const appResponseMap = orderDetailStore(state => state.appResponseMap);
      const reqOrderParams = orderDetailStore(state => state.reqOrderParams);
      const orderId =
        props.orderID ||
        props.orderId ||
        AppContext.UrlQuery?.orderId ||
        reqOrderParams?.orderId;
      const { orderStatus: orderStatusInfo, safeRent } = orderBaseInfo;
      const { orderStatus, orderStatusCtrip, orderStatusDesc } =
        orderStatusInfo || {};
      const isDebugMode = debugStore(state => state.isDebugMode);
      const fetchDone = orderDetailStore(state => state.fetchDone);
      const orderIsdModalVisible = orderDetailStore(
        state => state.orderIsdChangeModalVisible,
      );
      const feeDeductionVisible = orderDetailStore(
        state => state.feeDeductionVisible,
      );
      const modifyFlightNoModalVisible = orderDetailStore(
        state => state.modifyFlightNoModalVisible,
      );
      const BbkInsuranceDetailProps = orderDetailStore(
        state => state.BbkInsuranceDetailProps,
      );
      const phoneModalVisible = orderDetailStore(
        state => state.phoneModalVisible,
      );
      const phoneModalType = orderDetailStore(state => state.phoneModalType);
      const carRentalMustReadData = orderDetailStore(
        state => state.carRentalMustRead,
      );
      const modalsVisible = orderDetailStore(state => state.modalsVisible);
      const orderWaringInfo = commonStore(state => state.orderWaringInfo);
      const labelsInfo = orderDetailStore(state => state.labelsInfo);
      const restAssuredTag = getRestAssuredTag(labelsInfo);
      const labelsModalVisible = orderDetailStore(
        state => state.labelsModalVisible,
      );
      const easyLifeTagModalVisible = orderDetailStore(
        state => state.easyLifeTagModalVisible,
      );
      const carTags = getCarLabelsInfo(labelsInfo);
      const easyLifeTags = orderDetailStore(state => state.easyLifeTags);
      const easyLifeTagsInfo =
        extendedInfo?.easyLifeInfo?.tagList || easyLifeTags;
      const queryOrderApiStatus = orderDetailStore(
        state => state.queryOrderApiStatus,
      );
      const modifyOrderWarnModalVisible = modifyOrder(
        state => state.modifyOrderWarnModalVisible,
      );
      const questionnaires = vocStore(state => state.questionnaires);
      const vocModalVisible = vocStore(state => state.vocModalVisible);
      const freezeDepositExplain = orderDetailStore(
        state => state.freezeDepositExplain,
      );
      const vehicleInfo = orderDetailStore(state => state.vehicleInfo);
      const directOpen = AppContext?.UrlQuery?.directOpen;
      const directOpenSub = AppContext?.UrlQuery?.directOpenSub;
      const link = AppContext?.UrlQuery?.link;
      const priceDetailModalVisible = orderDetailStore(
        state => state.priceDetailModalVisible,
      );
      const urgeServiceIds = serviceStore(state => state.urgeServiceIds);
      const serviceProgressList = serviceStore(
        state => state.serviceProgressList,
      );
      const serviceIds = serviceStore(state => state.serviceIds);
      const storageCardsTitle = orderDetailStore(
        state => state.storageCardsTitle,
      );
      const refundPenaltyInfo = orderDetailStore(
        state => state.refundPenaltyInfo,
      );
      const service = serviceStore(state => state);
      const queryCarAssistantV2Response = orderDetailStore(
        state => state.queryCarAssistantV2Response,
      );
      const tipsCardInfo = getTipsCardInfo(
        queryCarAssistantV2Response,
        orderWaringInfo,
      );
      const orderAuthInfo = onlineAuthStore(state => state.orderAuthInfo);
      const nextStorageCardsTitle = getNextStorageCardsTitle(
        storageCardsTitle,
        refundPenaltyInfo,
        service,
        tipsCardInfo,
        orderAuthInfo?.supportInfo,
      );
      const fetchWarningInfoLoading = commonStore(
        state => state.fetchWarningInfoLoading,
      );
      const storeAttendant = extendedInfo?.storeAttendant;
      const orderDetailConfirmModalVisible = orderDetailStore(
        state => state.orderDetailConfirmModalVisible,
      );
      const orderEhiFreeDepositVisible =
        modalsVisible?.ehiFreeDepositModal?.visible;
      const finalQueryIsFinish = orderDetailStore(
        state => state.queryOrderAllDataSuccess,
      );
      const orderDetailPrice = orderDetailStore(
        state => state.orderDetailPrice,
      );
      const orderPriceInfoFee = getFeeDetailData(orderDetailPrice);
      const orderDetailResponse = orderDetailStore(state => state.response);
      const response = orderDetailStore(state => state.response);
      const operationButtons = getOperationButtons(orderDetailResponse);
      const platformInsurance = orderDetailStore(
        state => state.platformInsurance,
      );
      const isuranceBox = mapIsuranceBoxOsd(
        platformInsurance,
        vendorInfo,
        vehicleInfo,
      );
      const isShowTravelLimit = !!response?.osdPolicy?.crossLocationsPolicy;
      const isNewCancelRule = !!cancelRuleInfo?.osdCancelRuleInfo;
      const isSelfService = false;
      const depositDetailModalVisible = orderDetailStore(
        state => state.depositDetailModalVisible,
      );
      const limitPopVisible = orderDetailStore(state => state.limitPopVisible);
      const promptInfos = orderDetailStore(state => state.promptInfos);
      const addInstructData = getAdditionalDriverDesc(promptInfos);
      const isEasyLife2024 = extendedInfo?.packageLevel === EasyLife2024Code;
      const orderStatusList: any = [
        OrderStatusCtrip.CONFIRMED,
        OrderStatusCtrip.IN_SERVICE,
      ];
      const isShowFulfillmentCard = orderStatusList.includes(
        orderBaseInfo?.orderStatusCtrip,
      );
      const fulfillmentData = orderDetailStore(state => state.fulfillmentData);
      const locationDatePopVisible = orderDetailStore(
        state => state.locationDatePopVisible,
      );
      const isNewOsdModifyOrder = extendedInfo?.osdModifyOrderVersion === 'B';
      const osdModifyOrderNote = orderDetailStore(
        state => state.osdModifyOrderNote,
      );
      const isOsdModifyNewOrder = !!extendedInfo?.osdModifyNewOrder;
      const pickupStore = orderDetailStore(state => state.pickupStore);
      const returnStore = orderDetailStore(state => state.returnStore);
      const logBaseInfo = getOrderTraceData(
        reqOrderParams.orderId,
        pickupStore,
        returnStore,
        vendorInfo?.bizVendorCode,
      );
      const phoneSurveyShowCount = commonStore(
        state => state.phoneSurveyShowCount,
      );
      const receipt = orderDetailStore(state => state.receipt);
      const ces = orderDetailStore(state => state.ces);

      const {
        fetchOrder2,
        setModifyFlightNoModalVisible,
        showInsDetailModal,
        setIsdOrderChangeModalVisible,
        showFeeDeduction,
        setLabelsModalVisible,
        setEasyLifeTagModalVisible,
        setFetchDone,
        reset,
        setPhoneModalVisible,
        setPersonPhoneModalVisible,
        goIsdInsurancePayment,
        setOrderModalsVisible,
        isShowRenewStatusByStorage,
        saveRenewalOrderStatus,
        queryOrderStatus,
        payCountDownTimeOutFn,
        setPriceDetailModalVisible,
        setStorageCardsTitle,
        setOrderDetailConfirmModalVisible,
        ctripContinuePay,
        setDepositDetailModalVisible,
        setLimitRulePopVisible,
        createPreFetch,
        setFlightDelayRulesModalVisible,
        queryOsdModifyOrderNote,
        setLocationAndDatePopIsShow,
      } = orderDetailStore.getState();
      const { queryQuestionnaire, saveQuestionnaire, setVocModalVisible } =
        vocStore.getState();
      const { queryServiceProgress, urgeServiceProgress } =
        serviceStore.getState();
      const {
        fetchListWarningInfo,
        setPhoneSurveyShowCount,
        setStoreSurveyCommit,
      } = commonStore.getState();
      const ctripContinuePayFn = data => {
        const goToInsFun = addInsParams => {
          handleOrderDetailGoToIns({
            eventName: EventName.insConfirmBackToOrderDetail,
            addInsParams,
          });
        };
        ctripContinuePay(data, goToInsFun);
      };
      const { setAdvanceApplySign, clearCache } = advanceReturnStore.getState();
      return (
        <ComponentPage
          {...props}
          ref={props.forwardedRef}
          config={config}
          driverInfo={driverInfo}
          cancelRuleInfo={cancelRuleInfo}
          orderBaseInfo={orderBaseInfo}
          extendedInfo={extendedInfo}
          orderPriceInfo={orderPriceInfo}
          vendorInfo={vendorInfo}
          appResponseMap={appResponseMap}
          orderId={orderId}
          orderStatus={orderStatus?.orderStatus}
          orderStatusCtrip={orderStatusCtrip}
          orderStatusDesc={orderStatusDesc}
          isDebugMode={isDebugMode}
          fetchDone={fetchDone}
          orderIsdModalVisible={orderIsdModalVisible}
          feeDeductionVisible={feeDeductionVisible}
          modifyFlightNoModalVisible={modifyFlightNoModalVisible}
          BbkInsuranceDetailProps={BbkInsuranceDetailProps}
          phoneModalVisible={phoneModalVisible}
          phoneModalType={phoneModalType}
          carRentalMustReadData={carRentalMustReadData}
          modalsVisible={modalsVisible}
          orderWaringInfo={orderWaringInfo}
          restAssuredTag={restAssuredTag}
          labelsModalVisible={labelsModalVisible}
          easyLifeTagModalVisible={easyLifeTagModalVisible}
          carTags={carTags}
          safeRent={safeRent}
          easyLifeTags={easyLifeTagsInfo}
          queryOrderApiStatus={queryOrderApiStatus}
          modifyOrderWarnModalVisible={modifyOrderWarnModalVisible}
          questionnaires={questionnaires}
          vocModalVisible={vocModalVisible}
          freezeDepositExplain={freezeDepositExplain}
          vehicleInfo={vehicleInfo}
          directOpen={directOpen}
          directOpenSub={directOpenSub}
          link={link}
          priceDetailModalVisible={priceDetailModalVisible}
          urgeServiceIds={urgeServiceIds}
          serviceProgressList={serviceProgressList}
          serviceIds={serviceIds}
          storageCardsTitle={storageCardsTitle}
          nextStorageCardsTitle={nextStorageCardsTitle}
          fetchWarningInfoLoading={fetchWarningInfoLoading}
          storeAttendant={storeAttendant}
          orderDetailConfirmModalVisible={orderDetailConfirmModalVisible}
          operationButtons={operationButtons}
          orderEhiFreeDepositVisible={orderEhiFreeDepositVisible}
          finalQueryIsFinish={finalQueryIsFinish}
          orderPriceInfoFee={orderPriceInfoFee}
          orderDetailResponse={orderDetailResponse}
          isuranceBox={isuranceBox}
          isShowTravelLimit={isShowTravelLimit}
          isNewCancelRule={isNewCancelRule}
          isSelfService={isSelfService}
          depositDetailModalVisible={depositDetailModalVisible}
          limitPopVisible={limitPopVisible}
          addInstructData={addInstructData}
          isEasyLife2024={isEasyLife2024}
          isShowFulfillmentCard={isShowFulfillmentCard}
          fulfillmentData={fulfillmentData}
          locationDatePopVisible={locationDatePopVisible}
          isNewOsdModifyOrder={isNewOsdModifyOrder}
          osdModifyOrderNote={osdModifyOrderNote}
          isOsdModifyNewOrder={isOsdModifyNewOrder}
          logBaseInfo={logBaseInfo}
          phoneSurveyShowCount={phoneSurveyShowCount}
          ces={ces}
          receipt={receipt}
          fetchOrder2={fetchOrder2}
          setModifyFlightNoModalVisible={setModifyFlightNoModalVisible}
          showInsDetailModal={showInsDetailModal}
          setIsdOrderChangeModalVisible={setIsdOrderChangeModalVisible}
          showFeeDeduction={showFeeDeduction}
          setLabelsModalVisible={setLabelsModalVisible}
          setEasyLifeTagModalVisible={setEasyLifeTagModalVisible}
          setFetchDone={setFetchDone}
          reset={reset}
          setPhoneModalVisible={setPhoneModalVisible}
          setPersonPhoneModalVisible={setPersonPhoneModalVisible}
          goIsdInsurancePayment={goIsdInsurancePayment}
          setOrderModalsVisible={setOrderModalsVisible}
          isShowRenewStatusByStorage={isShowRenewStatusByStorage}
          saveRenewalOrderStatus={saveRenewalOrderStatus}
          queryOrderStatus={queryOrderStatus}
          payCountDownTimeOutFn={payCountDownTimeOutFn}
          queryQuestionnaire={queryQuestionnaire}
          saveQuestionnaire={saveQuestionnaire}
          setVocModalVisible={setVocModalVisible}
          setPriceDetailModalVisible={setPriceDetailModalVisible}
          queryServiceProgress={queryServiceProgress}
          urgeServiceProgress={urgeServiceProgress}
          setStorageCardsTitle={setStorageCardsTitle}
          getListWarningInfo={fetchListWarningInfo}
          closeOrderDetailConfirmModal={setOrderDetailConfirmModalVisible}
          ctripContinuePay={ctripContinuePayFn}
          setAdvanceApplySign={setAdvanceApplySign}
          clearAdvanceCache={clearCache}
          setDepositDetailModalVisible={setDepositDetailModalVisible}
          setLimitRulePopVisible={setLimitRulePopVisible}
          createPreFetch={createPreFetch}
          setFlightDelayRulesModalVisible={setFlightDelayRulesModalVisible}
          queryOsdModifyOrderNote={queryOsdModifyOrderNote}
          setLocationAndDatePopIsShow={setLocationAndDatePopIsShow}
          setPhoneSurveyShowCount={setPhoneSurveyShowCount}
          setStoreSurveyCommit={setStoreSurveyCommit}
        />
      );
    };
    return ComponentWithRef;
  };
};

const connectPage = (renderCheckInfo?) => {
  return ComponentPage => {
    const reduxInner = zustand()(ComponentPage);
    return wrapper(reduxInner, renderCheckInfo);
  };
};

export default connectPage;
