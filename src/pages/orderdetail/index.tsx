/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable no-promise-executor-return */
/* eslint-disable import/no-extraneous-dependencies */
import { debounce as lodashDebounce, get as lodashGet } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import Keyboard from '@c2x/apis/Keyboard';
import PixelRatio from '@c2x/apis/PixelRatio';
import ViewPort from '@c2x/components/ViewPort';
import CRNPage, { IBasePageProps } from '@c2x/components/Page';
import LoadingView from '@c2x/components/LoadingView';
import Event from '@c2x/apis/Event';
import Device from '@c2x/apis/Device';
import React from 'react';
import {
  XView as View,
  xRouter,
  xApplication as Application,
  XViewExposure,
  xDOMUtils,
} from '@ctrip/xtaro';

import { AdSlider } from '@c2x/extraPackages/@ctrip/crn-ext-adsdk';
import BbkToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import {
  color,
  layout,
  setOpacity,
  space,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils/index';
import { ServiceProgressDTO } from '@ctrip/rn_com_car/dist/src/Logic/src/Order/Types/ServiceProgress';
import { PackageDetailListType } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailResDtoType';
import { PhoneNumber } from '@ctrip/rn_com_car/dist/src/Logic/src/Order/Types/QueryOrderNumberType';
import INps from '@c2x/extraPackages/@ctrip/crn-inps';
import { noop } from '@pages/utils/util';
import Taro from '../../xTaroApi';
import c2xStyles from './orderDetail.module.scss';
import {
  BbkCustomScrollView,
  BbkOptimizeModal,
  CreateInsModal,
  InsFailedModal,
  RestAssuredBanner,
  OrderConfirmModal as ConfirmModal,
  NewWarningTipsModal,
  VocModal,
  DepositIntroduceModal,
  EasyLifeTagListModal,
  BbkSkeletonLoading,
  FuelTypeModal,
  BbkInsuranceSuitsModalOsd,
  ExcessIntroduceModalOsd,
  InsuranceNoticeMustReadModal,
  InsuranceReminderEnglishModal,
  DepositRateDescriptionModal,
  BbkMaterialModalNew,
  CustomerInfo,
  ContinuePayTips,
  PickupMaterialsModal,
  ReviewUnopenedModal,
  UpdateSnapShotData,
  UpdateContractTrackerSnapShotData,
  CarLabelsModal,
  PrivilegeTab,
  ServiceProgressModal,
  OsdFeeTitleExplainModal,
  ButtonsBottomBar,
  CESEntry,
  OsdModifyOrderModal,
  AddInstructModal,
  BbkComponentWarningTips,
  PriceDetailModalOsd,
  ReceiptModule,
  AssistiveTouch,
  ErrorNetWork,
  LessDetailModal,
  CPage,
  SupplementaryInfo,
  OrderAmount,
  Vehicle,
  PickReturnTab,
  BuyInsurance,
  AdditionalProduct,
  SesameGuider,
  ModifyOrderWarnModal,
  DepositDetailModal,
  CustomerPhoneModal,
  SesameRepeatOrderModal,
  BuyInsConfirmModal,
  EhiFreeDepositRuleModal,
  EHiModifyOrderSesameGuider,
  DepositPaymentModal,
  ContinuePayFailDialog,
  OrderLessEnter,
  FlightDelayRulesModal,
  FuelDescriptionModal,
  OrderFeeDeduction,
  OrderChangeContainerIsd,
  ModifyFlightNoModal,
  OrderContactDoorStoreModal as ContactDoorStoreModal,
  TravelLimit,
  BusinessTimePolicyModal,
  BusinessTimeModal,
  MicroEnter,
  CommentCard,
  OrderDetaiPriceDetailModal,
  VehModal,
} from './Components/index';
import Header from './Components/Header';
import OrderStatus from './Components/OStatus';
import {
  User,
  CarLog,
  AppContext,
  Utils,
  CarStorage,
  EventHelper,
  Channel,
  GetABCache,
} from '../../Util/Index';
import {
  LogKey,
  EventName,
  StorageKey,
  Url,
  Platform,
  OrderDetail as OrderDetailCons,
  ImageUrl,
  UITestID,
} from '../../Constants/Index';
import {
  setOrderDetailStatusData,
  removeOrderDetailStatusData,
  OrderDetailStatusKeyList,
} from '../../Global/Cache/OrderStatusData';
import {
  InsuranceAnchorCategory,
  DirectOpenSub,
  OrderStatusCtrip,
} from '../../Constants/OrderDetail';
import { initializeABOrderDetailPage } from '../../Util/CarABTesting/InitializeAB';
import { QConfigType } from '../../Types/Dto/QConfigResponseType';
import { AllOperationsType } from '../../Types/Dto/OrderDetailRespaonseType';
import { QueryOsdModifyOrderNoteResponseType } from '../../Types/Dto/QueryOsdModifyOrderNoteRequestAndResponseType';
import { initialPhoneSurvey, setPhoneSurvey } from './method';
import { getStorePolicyProps } from './state/orderdetail';
import { getMaterialsNew } from './state/product';
import {
  OrderModalsVisible,
  InsCallStatus,
  QueryOrderApiStatusType,
  QueryOrderFulfillmentInfoResponseType,
  CardResultType,
  VendorTagType,
  WarningListResponseType,
  WarningDto,
  ITravelLimit,
  ISelectedItem,
  IStateType,
} from './Types';
import {
  PageType,
  DargViewStatus,
  LayoutPartEnum,
  PageRole,
  DEFAULT_HEADER_HEIGHT,
  PageIndexId,
} from './enum';
import texts from './Texts';
import PickupMaterials from './Components/PickupMaterials';
import MustRead from './Components/MustRead';
import Insurance from './Components/Insurance';

const LgColor = [
  color.linearGradientBlueDark,
  color.linearGradientBlueDark,
  setOpacity(color.grayBg, 0),
];

const { selector, getPixel, lazySelector, isAndroid, vw, vh, isHarmony } =
  BbkUtils;
const AdWidth = vw(100) * PixelRatio.get();
const AdHeight = Math.round((AdWidth * 110) / 750);
// TODO 接入新的宽高比
const FIX_OFFESTTOP = 80; // 固定头部的滚动距离

// 自助取还预加载图片
const selfServicePrefetchImages = [
  `${ImageUrl.DIMG04_PATH}1tg0i12000cw6mi0fC09A.gif`,
  `${ImageUrl.DIMG04_PATH}1tg5812000cwsbaht1CB4.png`,
  `${ImageUrl.DIMG04_PATH}1tg5z12000cwsbc4j55CB.png`,
  `${ImageUrl.DIMG04_PATH}1tg3x12000cwsb50q806A.png`,
  `${ImageUrl.DIMG04_PATH}1tg5612000cmm01fp54A6.gif`,
  `${ImageUrl.DIMG04_PATH}1tg4i12000ckehptsA008.png`,
  `${ImageUrl.DIMG04_PATH}1tg5t12000ci0xpha7EDD.png?proc=corp/b55p`,
  `${ImageUrl.DIMG04_PATH}1tg5t12000ci0xpha7EDD.png?proc=corp/b30p`,
  `${ImageUrl.DIMG04_PATH}1tg5t12000ci0xpha7EDD.png`,
];

const styles = StyleSheet.create({
  pageContainer: { ...layout.flex1, backgroundColor: color.grayBg },
  contentBg: { backgroundColor: color.selfHelpBg },
  contentBgIsd: { backgroundColor: color.C_EDF2F8 },
  insWrap: {
    backgroundColor: color.white,
    marginTop: getPixel(20),
  },
  npsWrap: {
    marginTop: getPixel(16),
    marginLeft: 0,
    marginRight: 0,
    paddingLeft: getPixel(16),
    paddingRight: getPixel(16),
    backgroundColor: color.white,
  },
  middleNpsWrap: {
    marginTop: getPixel(0),
    paddingTop: getPixel(16),
    marginBottom: getPixel(16),
    marginLeft: 0,
    marginRight: 0,
    paddingLeft: getPixel(16),
    paddingRight: getPixel(16),
    backgroundColor: color.white,
  },
  shadow: {
    // @ts-expect-error
    shadowOffset: { width: 0, height: getPixel(-5) },
    shadowRadius: getPixel(5),
    shadowColor: color.black,
    shadowOpacity: 0.05,
    elevation: 20,
  },
  supplierWarp: {
    marginTop: getPixel(20),
    paddingLeft: space.spaceXXL,
    paddingRight: space.spaceXXL,
    marginBottom: getPixel(56),
  },
  supplierWarpWithPerson: { marginBottom: getPixel(60) },
  carService: {
    backgroundColor: color.white,
    marginTop: getPixel(20),
    borderWidth: 0,
  },
  carServiceUpgrade: {
    backgroundColor: color.white,
    paddingBottom: getPixel(32),
    marginTop: -getPixel(10),
  },
  guarantee: {
    backgroundColor: color.white,
    width: '100%',
    marginTop: getPixel(20),
    borderWidth: 0,
  },
  travelLimitWrap: { marginTop: getPixel(16) },
  fulfillLoading: {
    backgroundColor: color.white,
    width: getPixel(702),
    left: getPixel(24),
    top: getPixel(-13),
    borderRadius: getPixel(16),
    marginTop: getPixel(16),
  },
  innerViewStyle: {
    height:
      vh(100) * 0.7 + DEFAULT_HEADER_HEIGHT + getPixel(isAndroid ? 9 : 30),
  },
});

let scoreSelectTimer: any = null;
const toUpgradeInsTimer: any = null;
interface PhoneNumberDataType {
  phoneNumberList: PhoneNumber[];
  fetchCompleted: boolean;
}
interface OrderPropsType extends IBasePageProps {
  config: QConfigType;
  fetchOrder2: (data?: any, interval?: boolean) => void;
  isLoading?: boolean;
  orderID?: number;
  orderId?: number;
  orderStatus?: any;
  hasUserAuthInfo: boolean;
  hasUserInfoItem: boolean;
  clearResult: () => void;
  idCardResult: CardResultType;
  driverResult: CardResultType;
  isDebugMode?: boolean;
  orderIsdModalVisible?: boolean;
  labelsModalVisible?: boolean;
  feeDeductionVisible?: boolean;
  modifyFlightNoModalVisible?: boolean;
  BbkInsuranceDetailProps?: any;
  fetchDone: boolean;
  finalQueryIsFinish: boolean;
  fullSearchNum: number;
  showInsDetailModal: ({ visible }) => void;
  setModifyFlightNoModalVisible: ({
    visible,
    type,
  }: {
    visible: boolean;
    type?: string;
  }) => void;
  setIsdOrderChangeModalVisible: ({ visible }) => void;
  showFeeDeduction: ({ visible }) => void;
  setLabelsModalVisible: (data?: any) => void;
  setEasyLifeTagModalVisible: (data?: any) => void;
  payCountDownTimeOutFn: ({ timeOut }) => void;
  setFetchDone: ({ fetchDone }) => void;
  authSafeRentRecord: (data?: any) => void;
  fromPage?: string;
  reset?: (data?: any) => void;
  queryOrderApiStatus: QueryOrderApiStatusType;
  setPhoneModalVisible: (data?: any) => void;
  phoneModalVisible: boolean; // @ts-ignore
  phoneModalType?: OrderDetailCons.CustomerPhoneModalType;
  phoneList: [];
  personPhoneModalVisible: boolean;
  setPersonPhoneModalVisible: (data?: any) => void;
  carRentalMustReadData?: any;
  goIsdInsurancePayment: (data?: any) => void;
  modalsVisible: OrderModalsVisible;
  setOrderModalsVisible: (OrderModalsVisible?: any) => void;
  isShowRenewStatusByStorage: () => void;
  saveRenewalOrderStatus: () => void;
  queryOrderStatus: (data?: any) => void;
  queryQuestionnaire: (data: any) => void;
  saveQuestionnaire: (data: any) => void;
  setVocModalVisible: (data: any) => void;
  setAdvanceApplySign: (data: { sign: any }) => void;
  setPriceDetailModalVisible: (visible: boolean) => void;
  queryServiceProgress: (data: any) => void;
  urgeServiceProgress: (data: any) => void;
  setStorageCardsTitle: (data: any) => void;
  clearAdvanceCache: () => void;
  orderWaringInfo?: WarningListResponseType;
  restAssuredTag?: VendorTagType;
  orderStatusDesc?: string;
  carTags?: any;
  safeRent?: boolean;
  easyLifeTagModalVisible?: boolean;
  easyLifeTags?: any;
  modifyOrderWarnModalVisible?: boolean;
  invoiceDesc?: any;
  orderStatusCtrip?: string;
  renewTipLogData?: any;
  questionnaires?: any;
  vocModalVisible: boolean;
  driverInfo?: any;
  cancelRuleInfo?: any;
  orderBaseInfo?: any;
  extendedInfo?: any;
  orderPriceInfo?: any;
  vendorInfo?: any;
  appResponseMap?: any;
  freezeDepositExplain?: any;
  vehicleInfo?: any; // @ts-ignore
  directOpen?: OrderDetailCons.DirectOpen;
  directOpenSub?: string;
  link?: string;
  callBarDesc: string;
  priceDetailModalVisible: boolean;
  ehiModifyOrderModalVisible?: boolean;
  serviceProgressList?: Array<ServiceProgressDTO>;
  urgeServiceIds?: any;
  serviceIds?: string;
  refundPenaltyInfo: any;
  supportInfo?: any;
  storageCardsTitle?: Array<string>;
  nextStorageCardsTitle?: Array<string>;
  getListWarningInfo: (data) => void;
  fetchWarningInfoLoading: any;
  storeAttendant?: any;
  orderDetailConfirmModalVisible: boolean;
  openOrderDetailConfirmModal: () => void;
  closeOrderDetailConfirmModal: () => void;
  isHideCancelButton: boolean;
  operationButtons: AllOperationsType[];
  orderEhiFreeDepositVisible?: boolean;
  depositPaymentModalAutoShow: boolean;
  ctripContinuePay: (data: any) => void;
  advanceReturnFeeInfo: any;
  advanceReturnFeeInfoByVendor: any;
  advanceRecord: any;
  orderPriceInfoFee?: any;
  orderDetailResponse?: any;
  isuranceBox?: any;
  isShowTravelLimit?: boolean;
  crossPolicy?: ITravelLimit;
  setTravelLimitSelectedResult?: (data: ISelectedItem[]) => void;
  isNewCancelRule: boolean;
  setBusinessLicenseVisible?: (data) => void;
  isSelfService?: boolean;
  depositDetailModalVisible?: boolean;
  setDepositDetailModalVisible?: (data) => void;
  limitPopVisible?: boolean;
  setLimitRulePopVisible?: (data) => void;
  isOrderDataByPhone: boolean;
  createPreFetch?: (data) => void;
  isEasyLife2024: boolean;
  addInstructData?: any;
  claimProcessModalData?: any;
  vehicleUseNotesInfo?: any;
  setFlightDelayRulesModalVisible?: (data) => void;
  policyList?: any;
  isShowFulfillmentCard?: boolean; // 是否展示履约卡，包含订单状态和履约版本判断
  phoneNumberData?: PhoneNumberDataType;
  fulfillmentData?: QueryOrderFulfillmentInfoResponseType;
  locationDatePopVisible?: boolean;
  isNewOsdModifyOrder?: boolean;
  osdModifyOrderNote?: QueryOsdModifyOrderNoteResponseType;
  queryOsdModifyOrderNote: () => void;
  setLocationAndDatePopIsShow: (data) => void;
  isOsdModifyNewOrder?: boolean;
  isPickUpInDoor?: boolean; // 是否送车上门
  logBaseInfo?: any;
  isShelves2?: boolean;
  phoneSurveyShowCount?: number; // 展示电话问卷弹窗的次数
  setPhoneSurveyShowCount?: (data) => void;
  setStoreSurveyCommit?: (data) => void;
  receipt?: any;
  ces?: any;
}

interface CarServiceDetailModalProps {
  data?: Array<PackageDetailListType>;
}
interface OrderState extends IStateType {
  lang?: string;
  messages?: any;
  headerScroll?: boolean;
  refreshResult?: any;
  isShowCancelPolicyModal: boolean;
  vendorCallModal: boolean;
  warningTipsModalVisible?: boolean;
  warningTipsModalCotent?: Array<WarningDto>;
  addInstructModalVisible: boolean;
  isShowCancelPenaltyInfoModal: boolean;
  isShowPageOtherModule: boolean;
  isShowDepositIntro: boolean;
  isConsultProgressModal: boolean;
  confirmModalAnchor?: LayoutPartEnum;
  carServiceModalVisible: boolean;
  carServiceDetailModalProps: CarServiceDetailModalProps;
  carServiceDetailModalVisible: boolean;
  serviceClaimMoreVisible: boolean;
  couponModalVisible: boolean;
  isFuelTypeModalVisible: boolean;
  lessModalVisible: boolean;
  fuelType: string;
  osdPriceDetailModalVisible: boolean;
  osdFeeTitleExplainModalVisible: boolean;
  osdInsuranceVisible: boolean;
  anchorCategory: string;
  osdExcessIntroduceVisible: boolean;
  insuranceNoticeMustReadVisible: boolean;
  insuranceReminderEnglishVisible: boolean;
  englishContent: string;
  insuranceNotice: string;
  feeTitle: string;
  feeTitleExplain: string;
  isShowIsdAd: boolean;
  isShowOsdAd: boolean;
  materialModalVisible: boolean;
  depositRateDescriptionModalVisible: boolean;
  printVoucherUrl?: string;
  isVehicleStatusPolling: boolean;
  isExpandContractTracker?: boolean;
  contractTrackerRefreshValue?: number;
  isFulfillScrollEnd?: boolean;
  isShowBigImgContainer?: boolean;
  contactDoorStoreModalVisible: boolean;
  contactDoorStoreModalTabId?: any;
}

const defaultOrderQueryIntervalTime = 1000;
const commonOrderQueryIntervalTime = 8000;
export default class OrderDetail extends CPage<OrderPropsType, OrderState> {
  scroller: any;

  mainscroller: any;

  fulfillInnerScroll: any;

  fulfillmentCardScrollView: any; // 履约卡片滚动组件

  fulfillOrderPageSnapShot: any; // 履约模式下详情页快照

  fulfillmentCardSnapShot: any; // 履约卡片快照

  searchPanelModalRef: any; // 出境修改订单弹层

  height: any;

  refreshControl: any;

  inUploadImage: boolean;

  isUploadContractTrackerImage: boolean;

  originAppType: string;

  isKeyboardAwareScrollView: boolean;

  keyboardWillShowListener: any;

  keyboardWillHideListener: any;

  timerQueryOrderId: any = null;

  domsHeiObj = {};

  scrollToDomEnd = false;

  hasPageExposure = false;

  insConfirmData = {};

  // url 带参 dragBack=false
  // 禁用 native 侧滑
  disableDragBackFromUrl = false;

  contentHeight: number;

  orderStatusHeight: number;

  directScrollFinished: boolean;

  isKeyboardShow: boolean;

  isLogin: boolean;

  hasLeavePage: boolean;

  orderQueryIntervalTime: number;

  orderQueryOnCancelMaxCount: number;

  orderQueryOnCancelCount: number;

  orderStatusCtrip: string = '';

  triggerScreenSnapShot: any; // 截图上传触发

  triggerContractTrackerSnapShot: any; // 触发履约卡片快照上传

  handelFulfillReachBottom: any; // 履约卡滚动触底

  // timeout
  tempTimer1: any = null;

  tempTimer2: any = null;

  imagePrefetchTimer: any = null;

  contractTrackerSnapShotTimer: any = null;

  timeIsShowPage: any = null;

  sendOnScrollEvent: any = null;

  timingQueryOrderTime = new Date().getTime();

  claimMoreModalRef: any;

  isFulfillmentReachBottom: boolean;

  bottomSheetDargRef: any;

  npsRef = null;

  npsPosition = 0;

  headerHeight = 0;

  constructor(props) {
    super(props);
    this.state = {
      headerScroll: false,
      refreshResult: null,
      isShowCancelPolicyModal: false,
      vendorCallModal: false,
      warningTipsModalVisible: false,
      warningTipsModalCotent: [],
      addInstructModalVisible: false,
      isShowCancelPenaltyInfoModal: false,
      isShowPageOtherModule: false,
      isShowDepositIntro: false,
      isConsultProgressModal: false,
      carServiceModalVisible: false,
      carServiceDetailModalProps: {},
      carServiceDetailModalVisible: false,
      serviceClaimMoreVisible: false,
      couponModalVisible: false,
      isFuelTypeModalVisible: false,
      lessModalVisible: false,
      fuelType: '',
      osdPriceDetailModalVisible: false,
      osdFeeTitleExplainModalVisible: false,
      osdInsuranceVisible: false,
      anchorCategory: '',
      osdExcessIntroduceVisible: false,
      insuranceNoticeMustReadVisible: false,
      insuranceReminderEnglishVisible: false,
      englishContent: '',
      insuranceNotice: '',
      feeTitle: '',
      feeTitleExplain: '',
      isShowIsdAd: false,
      isShowOsdAd: false,
      materialModalVisible: false,
      depositRateDescriptionModalVisible: false,
      isVehicleStatusPolling: true,
      isExpandContractTracker: false,
      contractTrackerRefreshValue: 0,
      isFulfillScrollEnd: false,
      isShowBigImgContainer: false,
      contactDoorStoreModalVisible: false,
      contactDoorStoreModalTabId: null,
    };
    // this.mainscroller = React.createRef();
    this.bottomSheetDargRef = React.createRef();
    this.height = 0;
    this.inUploadImage = false;
    this.isUploadContractTrackerImage = false; // 履约卡片快照正在上传
    this.originAppType = AppContext.CarEnv.appType;
    this.isKeyboardAwareScrollView = true; // 滚动组件是否为KeyboardAwareScrollView
    this.keyboardWillShowListener = Keyboard.addListener(
      'keyboardWillShow',
      this.keyboardWillShow.bind(this),
    );
    this.keyboardWillHideListener = Keyboard.addListener(
      'keyboardWillHide',
      this.keyboardWillHide.bind(this),
    );
    this.contentHeight = 0;
    this.orderStatusHeight = 0;
    this.isKeyboardShow = false;
    this.directScrollFinished = false;
    this.isLogin = false;
    this.hasLeavePage = false;
    this.orderQueryIntervalTime = defaultOrderQueryIntervalTime; // 轮询时间，默认为1s，解决进入订单页时状态更新过慢
    this.orderQueryOnCancelMaxCount = 3; // 取消订单最大轮询次数
    this.orderQueryOnCancelCount = 0;
    this.isFulfillmentReachBottom = false;
    this.triggerScreenSnapShot = lodashDebounce(this.updateSnapShotData, 1000);
    this.triggerContractTrackerSnapShot = lodashDebounce(
      this.updateContractTrackerSnapShotData,
      1000,
    );
    // 增加订详滚动监听，滚动效果结束，取消监听
    this.sendOnScrollEvent = lodashDebounce(this.handleScrollEvent, 1000, {
      leading: true,
      trailing: false,
    });

    removeOrderDetailStatusData();
    // 同步售前已缓存的AB实验结果
    GetABCache.syncCacheAb();
    initializeABOrderDetailPage();
  }

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().Order.ID;
  }

  async componentDidMount() {
    super.componentDidMount();
    this.isLogin = User.isLoginSync();
    if (this.isLogin) {
      this.timingQueryOrder(true);
    } else {
      User.toLogin()
        .then(res => {
          if (res) {
            this.timingQueryOrder(true);
          } else {
            this.onLoadError();
          }
        })
        .catch(() => {
          this.onLoadError();
        });
    }
    // 注册页面名称供H5升级保险详情页面回退使用
    CRNPage.registerPage('ODetailContainer', () => {
      CarLog.LogCode({ name: '注册ODetailContainer页面' });
      this.timingQueryOrder();
    });
    CRNPage.registerPage(Channel.getPageId().Order.EN, () => {});
    this.emitEvent();
    this.setStorageCardsTitleFromStorageToRedux();
    this.handlePathUrlOpen();
    // 调用出境修改订单提示接口
    this.props.queryOsdModifyOrderNote();
    if (GetABCache.isStoreInfoSurvey()) {
      const { orderId, setPhoneSurveyShowCount } = this.props;
      initialPhoneSurvey(orderId, setPhoneSurveyShowCount);
    }
  }

  setStorageCardsTitleFromStorageToRedux = async () => {
    const { orderId, setStorageCardsTitle } = this.props;
    const storageCardsTitle = await CarStorage.loadAsync(
      `${StorageKey.CAR_ORDER_DETAIL_CARDS}_${orderId}`,
    );
    let storageCardsTitleArr = [];
    try {
      storageCardsTitleArr = JSON.parse(storageCardsTitle) || [];
    } catch (e) {
      storageCardsTitleArr = [];
    }
    setStorageCardsTitle(storageCardsTitleArr);
  };

  setNextStorageCardsTitleFromReduxToStorage = () => {
    const { nextStorageCardsTitle, orderId } = this.props;
    CarStorage.save(
      `${StorageKey.CAR_ORDER_DETAIL_CARDS}_${orderId}`,
      nextStorageCardsTitle,
      '30d',
    );
  };

  emitEvent = () => {
    const {
      goIsdInsurancePayment,
      setOrderModalsVisible,
      orderId,
      setStoreSurveyCommit = noop,
    } = this.props;
    EventHelper.addEventListener(EventName.orderInsurancePayCallback, data => {
      // 创建保险订单，并支付
      const status = lodashGet(data, 'status');
      const selectedInsuranceList =
        lodashGet(data, 'data.selectedInsuranceList') || [];
      if (
        (status === InsCallStatus.submit || status === InsCallStatus.cancel) &&
        selectedInsuranceList.length > 0
      ) {
        goIsdInsurancePayment(data);
      }
    });

    EventHelper.addEventListener(
      EventName.insConfirmBackToOrderDetail,
      data => {
        const status = lodashGet(data, 'status');
        if (
          status === InsCallStatus.submit ||
          status === InsCallStatus.cancel
        ) {
          const { ctripContinuePay } = this.props;
          ctripContinuePay({ confirmInsuranceData: data });
        }
      },
    );

    EventHelper.addEventListener(EventName.orderBtnsClickFromPhoneCheck, () => {
      setOrderModalsVisible({
        confirmModal: {
          visible: true,
          data: {
            get title() {
              return '当前账号无操作权限，请登录下单账号后操作';
            },
            btns: [
              {
                get name() {
                  return '知道了';
                },
                isPrimary: true,
                onPress: () => {
                  setOrderModalsVisible({
                    confirmModal: {
                      visible: false,
                    },
                  });
                },
              },
            ],
          },
        },
      });
    });
    if (GetABCache.isStoreInfoSurvey()) {
      // 添加提交反馈事件监听
      EventHelper.addEventListener(EventName.storeSurveyCommit, data => {
        const { params } = data || {};
        if (`${params?.[0]?.orderId}` === `${orderId}`) {
          setStoreSurveyCommit(true);
        }
      });
    }
  };

  scrollToPosition = async (y: number) => {
    const { isShowFulfillmentCard } = this.props;
    if (isShowFulfillmentCard) {
      this.closeFulfillmentCard();
      await new Promise(resolve => setTimeout(resolve, 500));
      this.handleMainscrollerScroll(y);
    } else {
      this.handleMainscrollerScroll(y + this.orderStatusHeight + getPixel(50));
    }
  };

  onDirectOpen = () => {
    const { directOpen, directOpenSub = '' } = this.props;

    const delayTime = isAndroid ? 800 : 200;

    if (directOpen === OrderDetailCons.DirectOpen.FeeDetail) {
      setTimeout(() => {
        this.showOsdPriceDetailModal();
      }, delayTime);
    } else if (directOpen === OrderDetailCons.DirectOpen.OsdInsurance) {
      if (directOpenSub === DirectOpenSub.first) {
        // 打开保障服务弹层, 定位到"套餐构成"tab
        // showOsdInsuranceModal的第一个参数没有用到, 所以传缺省值, 下同
        setTimeout(() => {
          this.showOsdInsuranceModal(
            undefined,
            InsuranceAnchorCategory.Insurance,
          );
        }, delayTime);
      } else if (directOpenSub === DirectOpenSub.second) {
        // 打开保障服务弹层, 定位到"保障详情"tab
        setTimeout(() => {
          this.showOsdInsuranceModal(
            undefined,
            InsuranceAnchorCategory.insuranceDetail,
          );
        }, delayTime);
      } else if (directOpenSub === DirectOpenSub.third) {
        // 打开保障服务弹层, 定位到"理赔流程"tab
        setTimeout(() => {
          this.showOsdInsuranceModal(
            undefined,
            InsuranceAnchorCategory.insuranceClaim,
          );
        }, delayTime);
      }
    }
  };

  handlePathUrlOpen = () => {
    const { directOpen, link } = this.props;
    if (Number(directOpen) === OrderDetailCons.DirectOpen.Link && link) {
      xRouter.navigateTo({ url: decodeURIComponent(link) });
    }
  };

  setOrderIntervalTime = ({
    orderStatusCtrip,
    isQuickInterval,
  }: {
    orderStatusCtrip?: OrderDetailCons.OrderStatusCtrip;
    isQuickInterval?: boolean;
  }) => {
    if (isQuickInterval) {
      this.orderQueryIntervalTime = defaultOrderQueryIntervalTime;
      return;
    }
    const intervalList: any = [
      `${OrderDetailCons.OrderStatusCtrip.PROCESSING}`,
      `${OrderDetailCons.OrderStatusCtrip.CANCELLING}`,
      `${OrderDetailCons.OrderStatusCtrip.PAYING}`,
    ];
    // 订单中间状态缩短轮询时间
    const interval = intervalList.includes(orderStatusCtrip)
      ? defaultOrderQueryIntervalTime
      : commonOrderQueryIntervalTime;

    if (this.orderQueryIntervalTime !== interval) {
      this.orderQueryIntervalTime = interval;
    }
  };

  closeFulfillmentCard() {
    this.bottomSheetDargRef?.current?.closeBottomSheet();
  }

  onPageReady() {
    this.performanceMonitor.responseMap = lodashGet(
      this.props,
      'appResponseMap',
    );
    this.performanceMonitor.interactiveStart = new Date();
    this.performanceMonitor.isLogin = this.isLogin;
    this.performanceMonitor.hasLeavePage = this.hasLeavePage;
    this.performanceMonitor.submit?.();
    this.performanceMonitor.contentShowStart =
      this.performanceMonitor.interactiveStart;
    this.performanceMonitor.submitTTI?.();
    this.setShowPageOtherModule();
  }

  componentWillUnmount() {
    this.clearTimingQueryOrder();
    this.setNextStorageCardsTitleFromReduxToStorage();
    if (this.tempTimer1) {
      clearTimeout(this.tempTimer1);
    }
    if (this.tempTimer2) {
      clearTimeout(this.tempTimer2);
    }
    if (this.imagePrefetchTimer) {
      clearTimeout(this.imagePrefetchTimer);
    }
    if (this.contractTrackerSnapShotTimer) {
      clearTimeout(this.contractTrackerSnapShotTimer);
    }
    if (this.timeIsShowPage) {
      clearTimeout(this.timeIsShowPage);
    }
    if (scoreSelectTimer) {
      clearTimeout(scoreSelectTimer);
    }
    if (toUpgradeInsTimer) {
      clearTimeout(toUpgradeInsTimer);
    }

    Event.removeEventListener(EventName.orderInsurancePayCallback);
    Event.removeEventListener(EventName.orderBtnsClickFromPhoneCheck);
    Event.removeEventListener(EventName.insConfirmBackToOrderDetail);
    if (GetABCache.isStoreInfoSurvey()) {
      Event.removeEventListener(EventName.storeSurveyCommit);
    }
    const { reset = noop } = this.props;
    reset();
    // setTimeout(() => {
    //   const { reset } = this.props;
    //   reset();
    // }, 0);
  }

  refreshContractTracker() {
    this.setState({
      contractTrackerRefreshValue: new Date().getTime(),
    });
  }

  pageDidAppear() {
    super.pageDidAppear();
    // 如果触发pageDidAppear是应为关闭了营业执照的大图容器则不触发快轮训
    if (!this.state.isShowBigImgContainer) {
      this.setOrderIntervalTime({ isQuickInterval: true });
    }
    // 拨打电话后回到订单详情页触发展示拨打电话弹窗
    if (
      this.props.config?.isSurvey &&
      GetABCache.isStoreInfoSurvey() &&
      this.props.phoneSurveyShowCount === 0
    ) {
      this.showSurveyModal();
    }
    this.timingQueryOrder();

    if (this.pageAppearCount > 1) {
      this.timingQueryOrder();
      this.initialServiceProgress();
      if (!this.state.isShowBigImgContainer) {
        this.fetchOrderFn();
        this.refreshContractTracker();
      } else {
        // 如果触发pageDidAppear是应为关闭了营业执照的大图容器则不触发请求列表
        this.handlChangeIsShowBigImgContainer(false);
      }
      // 调用出境修改订单提示接口
      if (this.props.isNewOsdModifyOrder) {
        this.props.queryOsdModifyOrderNote();
      }
    }
    // 判断续租订单状态是否改变
    const { isShowRenewStatusByStorage } = this.props;
    isShowRenewStatusByStorage();
    // 清除提前还车页面缓存
    const { clearAdvanceCache } = this.props;
    clearAdvanceCache();
    // 用车助手车辆状态轮询开启
    this.setState({ isVehicleStatusPolling: true });
  }

  onDidFocus() {
    this.disableNativeDragBack();
  }

  pageDidDisappear() {
    this.hasLeavePage = true;
    const { orderId, saveRenewalOrderStatus, setAdvanceApplySign } = this.props;
    super.pageDidDisappear({ orderId });
    this.clearTimingQueryOrder();
    saveRenewalOrderStatus();
    // 清除提前还车状态
    setAdvanceApplySign({ sign: null });
    // 用车助手车辆状态轮询关闭
    this.setState({ isVehicleStatusPolling: false });
  }

  disableNativeDragBack() {
    // 是否禁用侧滑
    const { dragBack } = this.props.app.urlQuery;
    if (dragBack === 'false') {
      this.disableDragBackFromUrl = true;
      this.disableDragBack();
    }
  }

  onPageExposure() {
    if (!this.hasPageExposure) {
      const { orderId, orderStatusDesc } = this.props;
      CarLog.LogTrace({
        key: LogKey.c_car_trace_order_page_exposure_222025,
        info: {
          orderId,
          pageId: this.getPageId(),
          orderStatus: orderStatusDesc,
        },
      });
      this.hasPageExposure = true;
    }
  }

  /* eslint-disable camelcase */
  UNSAFE_componentWillReceiveProps(nextProps) {
    const { queryOrderApiStatus } = this.props;
    const { isShowPageOtherModule } = this.state;
    if (
      queryOrderApiStatus === QueryOrderApiStatusType.unstart &&
      nextProps.queryOrderApiStatus !== QueryOrderApiStatusType.unstart
    ) {
      this.performanceMonitor.receiveResStart = new Date();
    }
    this.setOrderIntervalTime({ orderStatusCtrip: nextProps.orderStatusCtrip });
    // 当订单状态发生了变化，切其余模块加载完成,查询当前订单状态下的voc调查问题
    if (
      this.orderStatusCtrip !== nextProps.orderStatusCtrip &&
      isShowPageOtherModule
    ) {
      this.orderStatusCtrip = nextProps.orderStatusCtrip;
      this.initVocModal(nextProps.orderStatusCtrip);
    }
  }

  async initVocModal(orderStatusCtrip) {
    try {
      const { orderId, queryQuestionnaire = Utils.noop } = this.props;
      const res =
        (await CarStorage.loadAsync(StorageKey.CAR_ORDERDETAIL_VOC_MODAL)) ||
        '{}';
      const result = JSON.parse(res);
      const storeKey = `${orderId}_${orderStatusCtrip}`;
      if (!result[storeKey]) {
        // 初始化voc调查弹窗数据
        queryQuestionnaire({ orderId });
        result[storeKey] = true;
        CarStorage.save(StorageKey.CAR_ORDERDETAIL_VOC_MODAL, result, '30d');
      }
      // eslint-disable-next-line no-empty
    } catch (e) {}
  }

  async componentDidUpdate(prevProps, preState) {
    const {
      queryOrderApiStatus,
      modifyOrderWarnModalVisible,
      vocModalVisible,
      orderEhiFreeDepositVisible,
      finalQueryIsFinish,
      isSelfService,
      fulfillmentData,
    } = this.props;
    const {
      modifyOrderWarnModalVisible: preModifyOrderWarnModalVisible,
      vocModalVisible: preVocModalVisible,
      orderEhiFreeDepositVisible: preOrderEhiFreeDepositVisible,
      fulfillmentData: preFulfillmentData,
    } = prevProps;
    const { isShowPageOtherModule } = this.state;

    if (
      prevProps.queryOrderApiStatus === QueryOrderApiStatusType.unstart &&
      queryOrderApiStatus !== QueryOrderApiStatusType.unstart
    ) {
      this.onPageReady();
    }

    if (
      (!preModifyOrderWarnModalVisible && modifyOrderWarnModalVisible) ||
      (!preVocModalVisible && vocModalVisible) ||
      (!preOrderEhiFreeDepositVisible && orderEhiFreeDepositVisible)
    ) {
      this.disableDragBack();
    } else if (
      (preModifyOrderWarnModalVisible && !modifyOrderWarnModalVisible) ||
      (preVocModalVisible && !vocModalVisible) ||
      (preOrderEhiFreeDepositVisible && !orderEhiFreeDepositVisible)
    ) {
      this.enableDragBack();
    }

    if (
      prevProps.queryOrderApiStatus === queryOrderApiStatus &&
      queryOrderApiStatus !== QueryOrderApiStatusType.unstart &&
      finalQueryIsFinish
    ) {
      this.onPageExposure();
      // 有一些未知原因会出现不触发onPageReady的场景，而因isShowPageOtherModule没有更新，导致第二屏模块不会渲染
      // 固在第一屏模块渲染完后，加一层打底处理，保证第二屏的模块可以正常渲染
      this.setShowPageOtherModule();
    }

    // 当加载完第二屏数据
    if (
      isShowPageOtherModule !== preState.isShowPageOtherModule &&
      isShowPageOtherModule
    ) {
      this.initialServiceProgress();
      this.initialPreFetch();
      if (isSelfService && !this.imagePrefetchTimer) {
        this.imagePrefetchTimer = setTimeout(() => {
          Utils.imagePrefetch(selfServicePrefetchImages);
        }, 100);
      }
      // 更新 nps 模块定位
      if (!this.npsPosition && this.npsRef?.$ref?.current) {
        setTimeout(() => {
          xDOMUtils
            .getBoundingClientRect({ node: this.npsRef?.$ref?.current })
            .then(res => {
              if (res && res.height) {
                this.npsPosition = res.top;
              }
            });
        }, 100);
      }
    }

    // 履约卡片快照
    if (preFulfillmentData?.nodeHash !== fulfillmentData?.nodeHash) {
      this.contractTrackerSnapShotTimer = setTimeout(() => {
        this.triggerContractTrackerSnapShot();
      }, 1000);
    }
  }

  updateSnapShotData = async () => {
    const {
      fetchDone,
      orderId = 0,
      orderStatus,
      isShowFulfillmentCard,
    } = this.props;
    const { isShowPageOtherModule } = this.state;
    const pageSnapRef = isShowFulfillmentCard
      ? this.fulfillOrderPageSnapShot
      : this.mainscroller;
    if (
      fetchDone &&
      isShowPageOtherModule &&
      pageSnapRef &&
      !this.inUploadImage
    ) {
      this.inUploadImage = true;
      await UpdateSnapShotData({
        orderId,
        orderStatus,
        mainScrollViewRef: pageSnapRef,
      });
      this.inUploadImage = false;
      this.props.setFetchDone({ fetchDone: false });
    }
  };

  // 上传履约卡片快照
  updateContractTrackerSnapShotData = async () => {
    const { fulfillmentData, orderId = 0, orderStatusCtrip = '' } = this.props;
    if (
      !this.isUploadContractTrackerImage &&
      this.fulfillmentCardSnapShot &&
      (fulfillmentData?.fulfillmentNodeList?.length || 0) > 0
    ) {
      this.isUploadContractTrackerImage = true;
      await UpdateContractTrackerSnapShotData({
        orderId,
        orderStatusCtrip,
        hashCode: fulfillmentData?.nodeHash || '',
        mainScrollViewRef: this.fulfillmentCardSnapShot,
      });
      this.isUploadContractTrackerImage = false;
    }
  };

  fetchOrderFn = () => {
    const { fetchOrder2, orderId } = this.props;
    fetchOrder2({ orderId });
  };

  payCountDownTimeOut = () => {
    const { payCountDownTimeOutFn } = this.props;
    payCountDownTimeOutFn({ timeOut: true });
    this.fetchOrderFn();
  };

  showConsultProgressModal = () => {
    const { orderId, serviceIds } = this.props;
    CarLog.LogCode({
      name: '点击_事件咨询进度',

      info: {
        orderId: `${orderId}`,
        eventId: serviceIds,
        pageId: this.getPageId(),
      },
    });

    this.setState({ isConsultProgressModal: true });
  };

  hideConsultProgressModal = () => {
    this.setState({ isConsultProgressModal: false });
  };

  onUrgeServiceProgress = data => {
    const { urgeServiceProgress } = this.props;
    const { orderId, eventId } = data;
    CarLog.LogCode({
      name: '点击_事件咨询进度_催处理按钮',

      info: {
        orderId: `${orderId}`,
        eventId,
        pageId: this.getPageId(),
      },
    });
    urgeServiceProgress(data);
  };

  initialServiceProgress = () => {
    const { queryServiceProgress = Utils.noop, orderId } = this.props;
    queryServiceProgress({ orderId });
  };

  // 预请求接口
  initialPreFetch = () => {
    const { createPreFetch = Utils.noop, orderId } = this.props;
    createPreFetch({
      orderId,
    });
  };

  showCancelPenaltyInfoModal = () => {
    this.setState({ isShowCancelPenaltyInfoModal: true });
  };

  hideCancelPenaltyInfoModal = () => {
    this.setState({ isShowCancelPenaltyInfoModal: false });
  };

  showPhoneModal = type => {
    const { setPhoneModalVisible = Utils.noop } = this.props;
    setPhoneModalVisible({
      visible: true,
      phoneModalType: type || OrderDetailCons.CustomerPhoneModalType.Phone,
    });
  };

  timingQueryOrder = (isFetchStatic?: boolean) => {
    const { queryOrderStatus, orderId, orderStatusCtrip } = this.props;

    // 已取消订单执行3次请求后不再轮询
    if (orderStatusCtrip === OrderDetailCons.OrderStatusCtrip.CANCELLED) {
      if (this.orderQueryOnCancelCount <= this.orderQueryOnCancelMaxCount) {
        this.orderQueryOnCancelCount += 1;
      } else {
        return;
      }
    }

    if (this.timerQueryOrderId) return;
    queryOrderStatus({ orderId, isFetchStatic });
    this.timerQueryOrderId = setTimeout(() => {
      if (this.timerQueryOrderId) {
        this.clearTimingQueryOrder();
      }
      this.timingQueryOrderTime = new Date().getTime();
      this.timingQueryOrder();
    }, this.orderQueryIntervalTime);
  };

  clearTimingQueryOrder = () => {
    clearTimeout(this.timerQueryOrderId);
    this.timerQueryOrderId = null;
  };

  backCarHome = pageName => {
    CRNPage.getRegisteredPageList(res => {
      if (res && res.includes(pageName)) {
        CRNPage.popToPage(pageName);
        CarLog.LogCode({ name: pageName });
      } else {
        this.backToLastPage();
      }
    });
  };

  backToLastPage = () => {
    this.pop();
    CarLog.LogCode({ name: '点击_订单详情页_后退' });
  };

  pageGoBack = () => {
    const { from, fromUrl } = this.props.app.urlQuery;
    const { fromPage } = this.props;
    if (from === Platform.ORDER_BACK_PARAMS.book) {
      this.backCarHome('TripCarHome');
    } else if (fromUrl) {
      this.backCarHome(fromUrl);
    } else if (fromPage) {
      this.pop(fromPage);
    } else {
      this.backToLastPage();
    }
    EventHelper.sendEvent(EventName.orderBack2Home, {});
    this.setNextStorageCardsTitleFromReduxToStorage();
  };

  // 处理头部标题副标题切换
  handleHeadTitleOnScroll = y => {
    if (
      (y > FIX_OFFESTTOP || y === FIX_OFFESTTOP) &&
      !this.state.headerScroll &&
      y !== 0
    ) {
      this.setState({
        headerScroll: true,
      });
    }
    if (y < FIX_OFFESTTOP && !!this.state.headerScroll && y !== 0) {
      this.setState({
        headerScroll: false,
      });
    }
  };

  headerEvents = event => {
    const { y } = event.nativeEvent.contentOffset;
    this.height = y;
    this.handleHeadTitleOnScroll(this.height);
    // @ts-ignore
    const { contentOffset, layoutMeasurement, contentSize } = event.nativeEvent;
    const distance =
      contentSize.height - contentOffset.y - layoutMeasurement.height;
    const { isShowFulfillmentCard } = this.props;
    if (isShowFulfillmentCard) {
      this.setState({
        isFulfillScrollEnd: distance <= 1,
      });
    }
  };

  switchCancelPolicyModal = (value: boolean) => {
    this.setState({
      isShowCancelPolicyModal: value,
    });
  };

  hideCancelPolicyModal = () => {
    this.switchCancelPolicyModal(false);
  };

  hideReviewUnopenedModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ reviewUnopenedModal: { visible: false } });
  };

  showEasylifeModal = () => {
    const { setEasyLifeTagModalVisible, orderId, orderStatusDesc } = this.props;
    setEasyLifeTagModalVisible(true);
    CarLog.LogCode({
      name: '点击_订单详情页_无忧租详情',

      info: {
        orderId,
        orderStatus: orderStatusDesc,
      },
    });
  };

  closePhoneModal = () => {
    const { setPhoneModalVisible } = this.props;
    setPhoneModalVisible({
      visible: false,
    });
  };

  closePersonPhoneModal = () => {
    const { setPersonPhoneModalVisible } = this.props;
    setPersonPhoneModalVisible({
      visible: false,
    });
  };

  onAddInstructFormPress = () => {
    this.setState({
      addInstructModalVisible: true,
    });
  };

  // 关闭额外驾驶员说明模态框
  onCloseAddInstructModal = () => {
    this.setState({
      addInstructModalVisible: false,
    });
  };

  onPullRelease = () => {
    this.fetchOrderFn();
    this.initialServiceProgress();
    clearTimeout(this.tempTimer1);
    this.tempTimer1 = setTimeout(() => {
      this.setState({
        refreshResult: true,
      });
    }, 2500);
    clearTimeout(this.tempTimer2);
    this.tempTimer2 = setTimeout(() => {
      this.setState({
        refreshResult: null,
      });
    }, 4000);
    // 调用出境修改订单提示接口
    if (this.props.isNewOsdModifyOrder) {
      this.props.queryOsdModifyOrderNote();
    }
  };

  goToAccident = () => {
    const forwardurl = `${Url.ISD_CRN_URL}&initialPage=isdinsuranceagreement&type=1`;
    xRouter.navigateTo({ url: forwardurl });
    CarLog.LogCode({ name: '点击_详情页_事故处理流程' });
  };

  gotoPolicy = (policySelectedId, labName) => {
    const { orderId, orderStatus, logBaseInfo } = this.props;
    CarLog.LogCode({
      name: '点击_订单详情页_全部政策入口',

      info: {
        orderId,
        orderStatus,
        type: labName,
      },
    });
    const selectArr: any = [policySelectedId];
    const policyInfo = getStorePolicyProps(selectArr);
    const param = {
      urlParams: {
        policySelectedId,
        labName,
        logBaseInfo,
        policyInfo,
      },
    };
    // 保存Store数据，用于传参
    CarStorage.save(StorageKey.CAR_CROSS_PARAMS, JSON.stringify(param), '1m');
    const url = Platform.CAR_CROSS_URL.Policy.OSD;
    xRouter.navigateTo({ url });
  };

  showModifyFlightNoModal = type => {
    this.props.setModifyFlightNoModalVisible({ visible: true, type });
  };

  showWarningTipsModal = content => {
    this.setState({
      warningTipsModalVisible: true,
      warningTipsModalCotent: content,
    });
  };

  closeWarningTipsModal = () => {
    this.setState({
      warningTipsModalVisible: false,
    });
  };

  onPressServiceProviderBar = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      businessLicenseModal: {
        visible: true,
      },
    });
  };

  getListWarningInfo = () => {
    const { getListWarningInfo } = this.props;
    getListWarningInfo({ pageIndexId: PageIndexId.Order });
  };

  renderTipView = orderBaseInfo => {
    const { orderStatus, orderTip = {} } = orderBaseInfo || {};
    const { orderWaringInfo, fetchWarningInfoLoading, isOsdModifyNewOrder } =
      this.props;
    const hasWarningInfo = (orderWaringInfo?.warningDtos?.length || 0) > 0;
    const showContinuePayTips =
      orderStatus === OrderDetailCons.ORDER_STATUS.Unsubmitted &&
      orderTip?.urgentWarning &&
      !isOsdModifyNewOrder; // 出境修改订单新单不展示

    return (
      <>
        {!!showContinuePayTips && (
          <ContinuePayTips
            continuePayTip={orderBaseInfo.orderTip.urgentWarning}
          />
        )}
        {!!hasWarningInfo && (
          <BbkComponentWarningTips
            fetchWarningInfoLoading={fetchWarningInfoLoading}
            waringInfo={orderWaringInfo}
            onClick={this.showWarningTipsModal}
            onRetry={this.getListWarningInfo}
          />
        )}
      </>
    );
  };

  showErr = (isFail: any, fn?: () => void) => {
    const errorMsg = '系统繁忙';
    if (isFail) {
      BbkToast.show(errorMsg, 3, () => {
        if (fn && typeof fn === 'function') fn();
      });
    }
  };

  onLoadError = () => {
    // 渲染异常页面
    this.showErr(true, () => {
      this.pageGoBack();
    });
  };

  onBackAndroid() {
    const {
      orderIsdModalVisible,
      feeDeductionVisible,
      modifyFlightNoModalVisible,
      BbkInsuranceDetailProps = {},
      priceDetailModalVisible,

      setIsdOrderChangeModalVisible,
      showFeeDeduction,
      showInsDetailModal,
      setModifyFlightNoModalVisible,
      setPriceDetailModalVisible,
      depositDetailModalVisible,
      setDepositDetailModalVisible = Utils.noop,
      modalsVisible,
      orderDetailConfirmModalVisible,
      closeOrderDetailConfirmModal,
      limitPopVisible,
      setLimitRulePopVisible = Utils.noop,
      locationDatePopVisible,
      setLocationAndDatePopIsShow,
    } = this.props;
    const {
      warningTipsModalVisible,
      isShowCancelPolicyModal,
      addInstructModalVisible,
      carServiceDetailModalVisible,
      serviceClaimMoreVisible,
    } = this.state;
    if (orderIsdModalVisible) {
      setIsdOrderChangeModalVisible({ visible: false });
    } else if (feeDeductionVisible) {
      showFeeDeduction({ visible: false });
    } else if (modifyFlightNoModalVisible) {
      setModifyFlightNoModalVisible({ visible: false });
    } else if (BbkInsuranceDetailProps.visible) {
      showInsDetailModal({ visible: false });
    } else if (warningTipsModalVisible) {
      this.setState({ warningTipsModalVisible: false });
    } else if (priceDetailModalVisible) {
      setPriceDetailModalVisible(false);
    } else if (depositDetailModalVisible) {
      // 关闭押金明细弹层
      setDepositDetailModalVisible(false);
    } else if (modalsVisible?.refundDetailModal?.visible) {
      // 关闭退款进度弹层
      this.hideOrderRefundDetailModal();
    } else if (isShowCancelPolicyModal) {
      // 关闭取消政策弹层
      this.hideCancelPolicyModal();
    } else if (limitPopVisible) {
      // 关闭限行规则弹层
      setLimitRulePopVisible(false);
    } else if (orderDetailConfirmModalVisible) {
      // 关闭门店详情弹层
      closeOrderDetailConfirmModal();
    } else if (addInstructModalVisible) {
      // 关闭增加额外驾驶员弹层
      this.onCloseAddInstructModal();
    } else if (carServiceDetailModalVisible) {
      // 关闭车行服务详情弹层
      this.hideCarServiceDetailModal();
    } else if (serviceClaimMoreVisible) {
      // 关闭查看理赔要求及须知弹层
      this.hideServiceClaimMoreModal();
      this.claimMoreModalRef?.hideServiceClaimMore();
    } else if (locationDatePopVisible) {
      // 出境修改订单弹层
      setLocationAndDatePopIsShow({ visible: false });
    } else {
      this.pageGoBack();
    }
  }

  onCloseInsuranceDetails = () => {
    this.props.showInsDetailModal({ visible: false });
  };

  onCloseDepositIntroModal = () => {
    this.setState({
      isShowDepositIntro: false,
    });
  };

  onBarVendorCallClick = () => {
    this.setState({
      vendorCallModal: true,
    });
    // this.props.showInsDetailModal({ visible: false });
  };

  showOsdPriceDetailModal = () => {
    this.setState({
      osdPriceDetailModalVisible: true,
    });
  };

  hideOsdPriceDetailModal = () => {
    this.setState({
      osdPriceDetailModalVisible: false,
    });
  };

  showOsdFeeTitleExplainModal = (feeTitle, feeTitleExplain) => {
    this.setState({
      osdFeeTitleExplainModalVisible: true,
      feeTitle,
      feeTitleExplain,
    });
  };

  hideOsdFeeTitleExplainModal = () => {
    this.setState({
      osdFeeTitleExplainModalVisible: false,
    });
  };

  showOsdInsuranceModal = (item, anchorCategory) => {
    this.setState({
      osdInsuranceVisible: true,
      anchorCategory: anchorCategory || '',
    });
    const { orderId, orderStatus } = this.props;
    CarLog.LogCode({
      name: '点击_订单详情页_保险详情',

      info: {
        orderId,
        orderStatus,
      },
    });
  };

  hideOsdInsuranceModal = () => {
    this.setState({
      osdInsuranceVisible: false,
      anchorCategory: '',
    });
  };

  showExcessIntroduceModal = () => {
    this.setState({
      osdExcessIntroduceVisible: true,
    });
  };

  closeExcessIntroduceModal = () => {
    this.setState({
      osdExcessIntroduceVisible: false,
    });
  };

  showInsuranceNoticeMustReadModal = insuranceNotice => {
    const { insuranceNoticeMustReadVisible } = this.state;
    if (!insuranceNoticeMustReadVisible) {
      this.setState({
        insuranceNoticeMustReadVisible: true,
        insuranceNotice,
      });
    }
  };

  closeInsuranceNoticeMustReadModal = () => {
    const { insuranceNoticeMustReadVisible } = this.state;
    if (insuranceNoticeMustReadVisible) {
      this.setState({
        insuranceNoticeMustReadVisible: false,
      });
    }
  };

  showInsuranceReminderEnglishModal = englishContent => {
    const { insuranceReminderEnglishVisible } = this.state;
    if (!insuranceReminderEnglishVisible) {
      this.setState({
        insuranceReminderEnglishVisible: true,
        englishContent,
      });
    }
  };

  closeInsuranceReminderEnglishModal = () => {
    const { insuranceReminderEnglishVisible } = this.state;
    if (insuranceReminderEnglishVisible) {
      this.setState({
        insuranceReminderEnglishVisible: false,
      });
    }
  };

  openInsuranceNoticeRule = url => {
    if (url) {
      xRouter.navigateTo({ url });
    }
  };

  hideInsFailedModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      insFailedModalVisible: {
        visible: false,
        data: {
          content: OrderDetailCons.InsMsg.addInsFailModalContent,
          showPayBtn: false,
        },
      },
    });
  };

  handleInsGoPay = () => {
    this.hideInsFailedModal();
    EventHelper.sendEvent(EventName.insConfirmBackToOrderDetail, { status: 2 });
  };

  hideRenewTipsModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ renewTipModal: { visible: false } });
  };

  hideOrderRefundDetailModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ refundDetailModal: { visible: false } });
  };

  showOrderRefundDetailModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ refundDetailModal: { visible: true } });
  };

  hideOptimizationStrengthenModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ optimizationStrengthenModal: { visible: false } });
  };

  showOptimizationStrengthenModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({ optimizationStrengthenModal: { visible: true } });
  };

  isdAdCallBack = data => {
    const { isShowIsdAd } = this.state;
    if (isShowIsdAd !== data?.length > 0) {
      this.setState({
        isShowIsdAd: data?.length > 0,
      });
    }
  };

  osdAdCallBack = data => {
    const { isShowOsdAd } = this.state;
    if (isShowOsdAd !== data?.length > 0) {
      this.setState({
        isShowOsdAd: data?.length > 0,
      });
    }
  };

  getModifyOrderProps = () => {
    const { modalsVisible, setOrderModalsVisible } = this.props;
    if (modalsVisible?.confirmModal?.isModifyOrder) {
      return {
        btns: [
          {
            name: texts.gotit,
            isPrimary: true,
            onPress: () => {
              setOrderModalsVisible({ confirmModal: { visible: false } });
            },
          },
        ],
      };
    }
    return undefined;
  };

  handleContentOnLayout = e => {
    this.contentHeight = e.nativeEvent.layout.height;
    this.triggerScreenSnapShot();
  };

  handleMainscrollerScroll = (height: number, isAnimate = true) => {
    if (height >= 0 && this.mainscroller) {
      if (
        this.isKeyboardAwareScrollView &&
        this.mainscroller?.scrollToPosition
      ) {
        this.mainscroller?.scrollToPosition(0, height, isAnimate);
      } else {
        this.mainscroller?.scrollTo({ x: 0, y: height, animated: isAnimate });
      }
    }
  };

  handleFulfillInnerScroll = (height: number, isAnimate = true) => {
    if (height >= 0 && this.fulfillInnerScroll) {
      if (this.isKeyboardAwareScrollView) {
        this.fulfillInnerScroll?.scrollToPosition?.(0, height, isAnimate);
      } else {
        this.fulfillInnerScroll?.scrollTo?.({
          x: 0,
          y: height,
          animated: isAnimate,
        });
      }
    }
  };

  onScoreSelect = (npsPosition?: number) => {
    if (this.isKeyboardShow) return;
    const { orderId, orderStatus } = this.props;
    clearTimeout(scoreSelectTimer);
    const delay = 200;
    scoreSelectTimer = setTimeout(() => {
      this.handleMainscrollerScroll(npsPosition || this.contentHeight);
    }, delay);
    CarLog.LogCode({
      name: '点击_订单详情页_NPS分数',

      info: {
        orderId,
        orderStatus,
      },
    });
  };

  keyboardWillShow = () => {
    this.isKeyboardShow = true;
  };

  keyboardWillHide = () => {
    this.isKeyboardShow = false;
  };

  hideInsModal = () => {
    this.props.setOrderModalsVisible({
      createInsModalVisible: { visible: false },
    });
  };

  hideAdvanceReturnModal = () => {
    this.props.setOrderModalsVisible({
      advanceReturnModal: { visible: false },
    });
  };

  showAdvanceReturnModal = () => {
    this.props.setOrderModalsVisible({
      advanceReturnModal: { visible: true },
    });
  };

  closeMaterialModal = () => {
    this.setState({
      materialModalVisible: false,
    });
  };

  showModifyDriverInfoModal = item => {
    this.showModifyFlightNoModal(item);
  };

  hideContactDoorStoreModal = () => {
    this.setState({
      contactDoorStoreModalVisible: false,
      contactDoorStoreModalTabId: null,
    });
  };

  showContactDoorStoreModal = (id?) => {
    this.setState({
      contactDoorStoreModalVisible: true,
      contactDoorStoreModalTabId: id,
    });
  };

  clickButtonsBarVendorCall = () => {
    this.showContactDoorStoreModal();
  };

  showDepositRateDescriptionModal = () => {
    this.setState({
      depositRateDescriptionModalVisible: true,
    });
  };

  closeDepositRateDescriptionModal = () => {
    this.setState({
      depositRateDescriptionModalVisible: false,
    });
  };

  /**
   * 查看提车凭证
   */
  onPressVoucher = url => {
    CarLog.LogCode({ name: '点击_取车材料详情页_提车凭证' });
    if (url) {
      xRouter.navigateTo({ url });
    }
  };

  onPressHandleLicense = url => {
    if (url) {
      xRouter.navigateTo({ url });
    }
  };

  onPressToLicense = () => {
    const url = Platform.CAR_CROSS_URL.License.OSD;
    xRouter.navigateTo({ url });
  };

  hideAdvanceReturnFeeModal = () => {
    this.props.setOrderModalsVisible({
      advanceReturnFeeModal: { visible: false },
    });
  };

  hidePickupMaterialModal = () => {
    this.props.setOrderModalsVisible({ pickUpMaterials: { visible: false } });
  };

  showCarServiceModal = () => {
    this.setState({
      carServiceModalVisible: true,
    });
  };

  hideCarServiceModal = () => {
    this.setState({
      carServiceModalVisible: false,
    });
  };

  hideCarServiceDetailModal = () => {
    this.setState({
      carServiceDetailModalVisible: false,
      carServiceDetailModalProps: {},
    });
  };

  hideServiceClaimMoreModal = () => {
    this.setState({
      serviceClaimMoreVisible: false,
    });
  };

  setCarServiceDetailProps = data => {
    this.setState({
      carServiceDetailModalProps: data,
      carServiceDetailModalVisible: true,
    });
  };

  onPressServiceClaimMore = () => {
    this.setState({
      serviceClaimMoreVisible: true,
    });
  };

  hideOrderCouponModal = () => {
    this.setState({
      couponModalVisible: false,
    });
  };

  showOrderCouponModal = () => {
    this.setState({
      couponModalVisible: true,
    });
  };

  handleOldInsLayout = e => {
    setOrderDetailStatusData(
      OrderDetailStatusKeyList.oldInsLayoutY,
      e?.nativeEvent?.layout?.y,
    );
  };

  handleDirectOpenScrollToSection = (e, directOpenName: string) => {
    if (this.directScrollFinished) return;
    const { directOpen, finalQueryIsFinish } = this.props;

    if (String(directOpen) === directOpenName && finalQueryIsFinish) {
      const y = e?.nativeEvent?.layout?.y;
      setTimeout(() => {
        this.scrollToPosition(y);
        this.directScrollFinished = true;
      }, 200);
    }
  };

  handleNewInsLayout = e => {
    this.handleDirectOpenScrollToSection(
      e,
      String(OrderDetailCons.DirectOpen.UpgradeCarService),
    );
  };

  handleIsdCarServiceLayout = e => {
    this.handleDirectOpenScrollToSection(
      e,
      OrderDetailCons.DirectOpen.IsdCarService,
    );
  };

  handleIsdGuaranteeLayout = e => {
    this.handleDirectOpenScrollToSection(
      e,
      OrderDetailCons.DirectOpen.IsdGuarantee,
    );
  };

  handleOsdInsLayout = e => {
    this.handleDirectOpenScrollToSection(
      e,
      OrderDetailCons.DirectOpen.OsdInsurance,
    );
  };

  handleTravelLimitLayout = e => {
    this.handleDirectOpenScrollToSection(
      e,
      String(OrderDetailCons.DirectOpen.TravelLimit),
    );
  };

  handleRefundLayout = e => {
    this.handleDirectOpenScrollToSection(
      e,
      OrderDetailCons.DirectOpen.OrderRefundDetail,
    );
  };

  renderSecondScreen = () => {
    const {
      driverInfo = {},

      carRentalMustReadData,
      config,
      isShowTravelLimit,

      logBaseInfo,
      orderId,
      orderStatus,
      receipt = {},
      ces = {},
    } = this.props;
    const { inpsSceneId } = config || {};
    const { orderStatusCtrip } = orderStatus || {};

    return (
      <>
        {/* 保障服务 */}
        <XViewExposure
          testID={CarLog.LogExposure({
            name: '曝光_订单详情页_保险详情',
            info: logBaseInfo,
          })}
          style={styles.insWrap}
          onLayout={this.handleOsdInsLayout}
        >
          <Insurance
            showDetail={this.showOsdInsuranceModal}
            showEnglish={this.showInsuranceReminderEnglishModal}
          />

          <BuyInsurance />
        </XViewExposure>
        {/* 旅行限制 */}
        {isShowTravelLimit && (
          <View
            testID={CarLog.LogExposure({
              name: '曝光_订单详情页_旅行限制',
              info: logBaseInfo,
            })}
            onLayout={this.handleTravelLimitLayout}
          >
            <TravelLimit style={styles.travelLimitWrap} />
          </View>
        )}
        {/* 屏蔽附加产品模块等完整的产品需求 */}
        <AdditionalProduct />
        {/* 预定信息（驾驶员信息）模块 */}
        <CustomerInfo
          data={driverInfo}
          showModifyDriverInfoModal={this.showModifyDriverInfoModal}
          testID={CarLog.LogExposure({
            name: '曝光_订单详情页_驾驶员信息模块',
          })}
        />
        {/* 报销凭证模块 */}
        <ReceiptModule receipt={receipt} orderId={orderId} />
        {/* 辅助信息模块（你可能想知道 + 门店政策） */}
        <SupplementaryInfo
          onPolicyPress={this.gotoPolicy}
          carRentalMustReadData={carRentalMustReadData}
          orderStatusCtrip={orderStatusCtrip}
          orderId={orderId}
        />
        {/* TAPP 减碳模块 */}
        <OrderLessEnter />
        {/* CES 问卷反馈 */}
        <CESEntry ces={ces} />
        {/* NPS 满意度 */}
        {orderStatusCtrip !== OrderDetailCons.OrderStatusCtrip.COMPLETED &&
          !!INps &&
          !!inpsSceneId && (
            <XViewExposure
              testID={CarLog.LogExposure({ name: '曝光_订单详情页_NPS模块' })}
              style={{
                backgroundColor: color.white,
                paddingBottom: getPixel(-16),
                marginTop: getPixel(16),
              }}
            >
              <INps
                sceneId={String(inpsSceneId)}
                env={Application.env}
                bizId="car"
                orderId={orderId}
                pageId={this.getPageId()}
                orderType="order"
                containerStyle={styles.npsWrap}
                onScoreChange={() => this.onScoreSelect()}
              />
            </XViewExposure>
          )}
        {/* TODO-@zyr hramony暂不支持KeyboardAvoidingView键盘自适应功能 */}
        {/* 临时解决方案为给滚动视图底部加个固定高度view 后续框架支持再去掉 */}
        {isHarmony && <View style={{ height: 100 }} />}
      </>
    );
  };

  setShowPageOtherModule = () => {
    const delayTime = isAndroid ? 800 : 100;
    this.timeIsShowPage = setTimeout(() => {
      if (this.state.isShowPageOtherModule) {
        return;
      }
      this.lazyLoadOtherModules();
      this.setState({ isShowPageOtherModule: true }, () => {
        this.onDirectOpen();
      });
    }, delayTime);
  };

  handlePageContentLayout = e => {
    this.handleContentOnLayout(e);
  };

  handleOrderStatusLayout = e => {
    this.orderStatusHeight = getPixel(e.nativeEvent.layout.height);
  };

  handleContractTrackerLayout = () => {
    this.triggerContractTrackerSnapShot();
  };

  setEtcIntroModal = visible => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      etcIntroModal: {
        visible,
      },
    });
  };

  onPressEtcUseHelperModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      etcUseHelperModal: {
        visible: true,
      },
    });
    CarLog.LogCode({ name: '点击_门店与车型配置_ETC使用注意事项' });
  };

  closeEtcIntroModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      etcIntroModal: {
        visible: false,
      },
    });
  };

  closeEtcUseHelperModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      etcUseHelperModal: {
        visible: false,
      },
    });
  };

  closeVehicleUseNotesModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      vehicleUseNotesModal: {
        visible: false,
      },
    });
  };

  closeClaimProcessModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      claimProcessVisible: {
        visible: false,
      },
    });
  };

  closePickUpMaterialsModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      pickUpMaterialsModal: {
        visible: false,
      },
    });
  };

  closeBusinessTimePolicyModal = () => {
    const { setOrderModalsVisible, modalsVisible } = this.props;
    setOrderModalsVisible({
      businessTimePolicyModal: {
        visible: false,
        data: {
          type: modalsVisible.businessTimePolicyModal?.data?.type,
        },
      },
    });
  };

  closeBusinessTimeModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      businessTimeModal: {
        visible: false,
      },
    });
  };

  closefuelDescModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      fuelDescModalVisible: {
        visible: false,
      },
    });
  };

  showFlightDelayRulesModal = () => {
    const { setFlightDelayRulesModalVisible = Utils.noop } = this.props;
    setFlightDelayRulesModalVisible(true);
  };

  handleScrollEvent = () => {
    EventHelper.sendEvent(EventName.OrderDetailOnScroll, {});
  };

  onScroll = event => {
    this.headerEvents(event);
    this.sendOnScrollEvent();
  };

  renderHeaderContent = () => {
    const { restAssuredTag, orderBaseInfo } = this.props;
    return (
      <>
        {this.renderTipView(orderBaseInfo)}
        <RestAssuredBanner tag={restAssuredTag} />
      </>
    );
  };

  onDargViewAnimateEnd = ({ dragStatus }: { dragStatus: DargViewStatus }) => {
    this.setState({
      isExpandContractTracker: dragStatus === DargViewStatus.Expand,
      isFulfillScrollEnd: false,
    });
    if (dragStatus === DargViewStatus.Default) {
      this.handleFulfillInnerScroll(0, false);
    }
  };

  getMainViewHeight = () => {
    const bottomGap =
      (Device.isiPhoneX ? getPixel(69) : getPixel(33)) + getPixel(32);
    // 主信息层滚动区域高度 = 屏幕高度 - 头部 - 底部 - 订单产品信息头部区域 - 底部按钮组高度
    return vh(100) - DEFAULT_HEADER_HEIGHT - bottomGap - getPixel(196);
  };

  // 订单状态模块
  renderOrderStatus = () => {
    return (
      <OrderStatus
        showConsultProgressModal={this.showConsultProgressModal}
        onTimeOut={this.payCountDownTimeOut}
      />
    );
  };

  // 订单信息模块
  renderOrderInfoContent = () => {
    const { isShowPageOtherModule } = this.state;
    const {
      safeRent,
      easyLifeTags,
      extendedInfo,
      finalQueryIsFinish,
      logBaseInfo,
      config,
      orderId,
      orderStatus,
    } = this.props;

    const { isShowOsdAd } = this.state;
    const isStoreInfoSurvey = GetABCache.isStoreInfoSurvey();
    const { inpsSceneId } = config || {};
    const { orderStatusCtrip } = orderStatus || {};

    const showOrderAmountFirst = [
      OrderDetailCons.OrderStatusCtrip.WAITING_PAY,
      OrderDetailCons.OrderStatusCtrip.PAYING,
      OrderDetailCons.OrderStatusCtrip.CANCELLING,
      OrderDetailCons.OrderStatusCtrip.CANCELLED,
    ].includes(orderStatusCtrip);

    return (
      <View className={c2xStyles.orderInfoBg}>
        <View className={c2xStyles.orderInfoCard}>
          {showOrderAmountFirst && (
            <OrderAmount
              showOsdPriceDetailModal={this.showOsdPriceDetailModal}
              headerContent={this.renderHeaderContent}
              onPressTandC={this.gotoPolicy}
              handleRefundLayout={this.handleRefundLayout}
              logBaseInfo={logBaseInfo}
            />
          )}
          <PickupMaterials />
          <MustRead />
          {/* 企业微信入群 */}
          <MicroEnter />
        </View>
        {/** 点评卡片 */}
        <CommentCard />
        {!showOrderAmountFirst && (
          <View className={c2xStyles.orderAmount}>
            <OrderAmount
              showOsdPriceDetailModal={this.showOsdPriceDetailModal}
              headerContent={this.renderHeaderContent}
              onPressTandC={this.gotoPolicy}
              handleRefundLayout={this.handleRefundLayout}
              logBaseInfo={logBaseInfo}
            />
          </View>
        )}
        {/* 无忧租特权 Tab */}
        {!!safeRent && easyLifeTags?.length > 0 && (
          <PrivilegeTab
            onPress={this.showEasylifeModal}
            isNewNoWorry={extendedInfo?.newNoWorry}
          />
        )}
        {isShowPageOtherModule && (
          <View className={isShowOsdAd && c2xStyles.osdAdWrap}>
            <AdSlider
              width={AdWidth}
              height={AdHeight}
              getAdData={this.osdAdCallBack}
              impId="01011TUOYRH0128JNXKRWHNCNKC"
            />
          </View>
        )}

        {/* NPS 满意度 */}
        {orderStatusCtrip === OrderDetailCons.OrderStatusCtrip.COMPLETED &&
          !!INps &&
          !!inpsSceneId && (
            <XViewExposure
              testID={CarLog.LogExposure({ name: '曝光_订单详情页_NPS模块' })}
              ref={ref => {
                this.npsRef = ref;
              }}
            >
              <INps
                sceneId={String(inpsSceneId)}
                env={Application.env}
                bizId="car"
                orderId={orderId}
                pageId={this.getPageId()}
                orderType="order"
                containerStyle={styles.middleNpsWrap}
                onScoreChange={() => {
                  // 应滚动距离 = NPS模块位置 - 头部高度 + MarginTop(16)
                  this.onScoreSelect(this.npsPosition - this.headerHeight + 16);
                }}
              />
            </XViewExposure>
          )}

        {/* 车型信息 */}
        <Vehicle />
        <PickReturnTab
          orderId={orderId}
          isStoreInfoSurvey={isStoreInfoSurvey}
          onPolicyPress={this.gotoPolicy}
          onPressPhone={this.showContactDoorStoreModal}
        />
        {!finalQueryIsFinish && (
          <BbkSkeletonLoading
            visible={true}
            pageName={PageType.OrderDetailFirstScreen2}
          />
        )}
        {lazySelector(isShowPageOtherModule, this.renderSecondScreen)}
      </View>
    );
  };

  renderPageContent = () => {
    const { refreshResult } = this.state;
    const { orderStatus } = this.props;
    let backgroundColor = '#F3F9FF';
    if (orderStatus?.orderStatusCtrip === OrderStatusCtrip.WAITING_PAY) {
      backgroundColor = '#FEF8F3';
    } else if (orderStatus?.orderStatusCtrip === OrderStatusCtrip.CONFIRMED) {
      backgroundColor = '#EFFBF7';
    }

    return (
      <BbkCustomScrollView
        headStyle={{ backgroundColor }}
        scrollViewHeaderStyle={{ backgroundColor }}
        refreshIsGradient={true}
        refreshGradientColor={LgColor}
        getRef={ref => {
          this.mainscroller = ref;
        }}
        onScroll={this.onScroll}
        style={styles.contentBg}
        onPullRelease={this.onPullRelease}
        refreshResult={refreshResult}
        isKeyboardAwareScrollView={this.isKeyboardAwareScrollView}
        footerEnable={false}
      >
        <View
          style={{ flex: 1, backgroundColor: color.bookingOptimizationGrayBg }}
          onLayout={this.handlePageContentLayout}
        >
          {this.renderOrderStatus()}
          {this.renderOrderInfoContent()}
        </View>
      </BbkCustomScrollView>
    );
  };

  hideVocModal = () => {
    CarLog.LogCode({ name: '点击_订单详情页_VOC用户反馈模块_关闭按钮' });
    const { setVocModalVisible = Utils.noop } = this.props;
    setVocModalVisible({
      vocModalVisible: false,
    });
  };

  showSurveyModal = async () => {
    const { orderId, setPhoneSurveyShowCount = noop } = this.props;
    setPhoneSurveyShowCount({
      count: 1,
    });
    setPhoneSurvey(orderId);
  };

  showFuelTypeModal = fuelType => {
    this.setState({
      isFuelTypeModalVisible: true,
      fuelType,
    });
  };

  hideFuelTypeModal = () => {
    this.setState({
      isFuelTypeModalVisible: false,
    });
  };

  showLessModal = () => {
    this.setState({
      lessModalVisible: true,
    });
  };

  hideLessModal = () => {
    this.setState({
      lessModalVisible: false,
    });
  };

  pressAnswer = (question, answer) => {
    const {
      orderId,
      orderStatusDesc,
      orderStatus,
      saveQuestionnaire = Utils.noop,
    } = this.props;
    const {
      questionId,
      content: questionContent,
      prCarExceptionCode,
    } = question;
    const { code: answerId, content: answerContent } = answer;
    // 回答问题
    saveQuestionnaire({
      orderId,
      questionId,
      answerCode: answerId,
      prCarExceptionCode,
      ctripStatusCode: orderStatus,
    });
    CarLog.LogCode({
      name: '点击_订单详情页_VOC用户反馈',

      info: {
        orderId,
        orderStatus: orderStatusDesc,
        questionId,
        questionContent,
        answerId,
        prCarExceptionCode,
        answerContent,
      },
    });
  };

  getTitle = (obj, vehicleName?: string) => {
    const { title = '' } = obj || {};
    let { description = '' } = obj || {};
    if (!title && !description) return null;
    if (vehicleName) {
      description = description.replace('{0}', vehicleName);
    }
    return {
      headerText: title,
      items: description
        .split('\n')
        .filter(v => v)
        .map(v => ({ title: v })),
    };
  };

  handleBusinessLicenseClose = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      businessLicenseModal: {
        visible: false,
      },
    });
  };

  handlChangeIsShowBigImgContainer = (bool: boolean) => {
    if (bool === this.state.isShowBigImgContainer) {
      return;
    }
    this.setState({
      isShowBigImgContainer: bool,
    });
  };

  handShowBigImgContainer = () => {
    this.handlChangeIsShowBigImgContainer(true);
  };

  handleClaimMoreModalRef = ref => {
    this.claimMoreModalRef = ref;
  };

  handleSearchPanelModalRef = ref => {
    this.searchPanelModalRef = ref;
  };

  closeOsdModifyOrderModal = () => {
    this.props.setLocationAndDatePopIsShow({ visible: false });
  };

  renderPageModals = () => {
    const {
      feeDeductionVisible,
      modifyFlightNoModalVisible,
      phoneModalVisible,
      phoneList,
      phoneModalType,
      orderIsdModalVisible,
      orderId,
      orderStatus,
      orderStatusDesc,
      modalsVisible,
      setOrderModalsVisible,
      labelsModalVisible,
      setLabelsModalVisible,
      carTags,
      easyLifeTagModalVisible,
      easyLifeTags,
      safeRent,
      setEasyLifeTagModalVisible,
      questionnaires,
      queryOrderApiStatus,
      orderBaseInfo,
      freezeDepositExplain,
      vehicleInfo,
      vocModalVisible,
      ehiModifyOrderModalVisible,
      serviceProgressList,
      urgeServiceIds,
      config,
      operationButtons,
      orderPriceInfoFee,
      orderDetailResponse,
      extendedInfo,
      isuranceBox,
      finalQueryIsFinish,
      vendorInfo,
      addInstructData,
      isEasyLife2024,
      isShowFulfillmentCard,
      logBaseInfo,
      locationDatePopVisible,
      osdModifyOrderNote,
      setLocationAndDatePopIsShow,
    } = this.props;
    const {
      warningTipsModalVisible,
      warningTipsModalCotent,
      addInstructModalVisible,
      isShowDepositIntro,
      isConsultProgressModal,
      couponModalVisible,
      isFuelTypeModalVisible,
      lessModalVisible,
      fuelType,
      osdPriceDetailModalVisible,
      osdFeeTitleExplainModalVisible,
      osdInsuranceVisible,
      anchorCategory,
      osdExcessIntroduceVisible,
      feeTitle,
      feeTitleExplain,
      insuranceNoticeMustReadVisible,
      insuranceNotice,
      insuranceReminderEnglishVisible,
      englishContent,
      materialModalVisible,
      depositRateDescriptionModalVisible,
      printVoucherUrl,
      contactDoorStoreModalVisible,
      contactDoorStoreModalTabId,
    } = this.state;
    const isLicenseApprove = extendedInfo?.osdDetailVersion === 'B';
    const isShowPage =
      queryOrderApiStatus === QueryOrderApiStatusType.success &&
      !!orderBaseInfo.orderId;

    const pageModalProps = {
      visible: addInstructModalVisible,
      onMaskPress: this.onCloseAddInstructModal,
      style: styles.shadow,
    };

    const { packageInfos = [] } = isuranceBox;
    const curPackageId = packageInfos?.[0]?.insPackageId;
    const depositRateDescriptionInfo = extendedInfo?.promptInfos?.find(
      f => f.type === OrderDetailCons.ListPromptType.MarketBanner,
    );
    const materials = getMaterialsNew({
      materials: extendedInfo?.pickUpMaterials,
      isShowQuestion: !!depositRateDescriptionInfo,
      vendorId: vendorInfo?.bizVendorCode,
    });
    return (
      <>
        {/* 国内修改订单弹层，不会出现：对应的action setIsdOrderChangeModalVisible 没有置为true的时候 */}
        {!!orderIsdModalVisible && <OrderChangeContainerIsd />}
        {/* 不明扣费弹层，不会出现： showFeeDeduction这个action没有置为true的时候 */}
        {!!feeDeductionVisible && <OrderFeeDeduction />}
        {/* 无忧租弹层 */}
        {(!!safeRent || isEasyLife2024) && easyLifeTags?.length > 0 && (
          <EasyLifeTagListModal
            visible={easyLifeTagModalVisible}
            data={easyLifeTags}
            isEasyLife2024={isEasyLife2024}
            onCancel={() => setEasyLifeTagModalVisible(false)}
          />
        )}
        <SesameGuider useModal={true} />
        {/* 去免押弹层 & 放弃免押弹层 */}
        <DepositPaymentModal />
        {/* 押金政策 */}
        <DepositDetailModal />

        {/* 冻结押金 */}
        <DepositIntroduceModal
          freezeDepositExplain={freezeDepositExplain}
          visible={isShowDepositIntro}
          onClose={this.onCloseDepositIntroModal}
        />

        {/* 小问卷 */}
        {questionnaires?.length > 0 && !couponModalVisible && (
          <VocModal
            vocModalVisible={
              vocModalVisible && !modalsVisible?.depositPaymentModal?.visible
            }
            questionnaires={questionnaires}
            onHide={this.hideVocModal}
            onPressAnswer={this.pressAnswer}
            orderId={orderId}
            orderStatus={orderStatusDesc}
          />
        )}
        {/* 额外驾驶员说明 */}
        <AddInstructModal
          pageModalProps={pageModalProps}
          addInstructData={addInstructData}
        />

        {/* 咨询进度弹层 */}
        {(serviceProgressList?.length || 0) > 0 && (
          <ServiceProgressModal
            urgeServiceIds={urgeServiceIds}
            orderId={orderId}
            orderStatus={orderStatus}
            pageId={this.getPageId()}
            urgeServiceProgress={this.onUrgeServiceProgress}
            onHide={this.hideConsultProgressModal}
            modalVisible={isConsultProgressModal}
            serviceProgressList={serviceProgressList}
          />
        )}
        {/* 修改航班号弹层，对应的 SET_MODIFYFLIGHTNOMODALVISIBLE 永远不会置为true */}
        {!!modifyFlightNoModalVisible && <ModifyFlightNoModal />}
        {this.props.isDebugMode && (
          <AssistiveTouch
            onPress={() => {
              Taro.navigateTo({
                url: '/pages/demo/debug/index',
              });
            }}
          />
        )}
        {selector(
          isShowPage,
          <ButtonsBottomBar
            operationButtons={operationButtons}
            isPositionAbsolute={isShowFulfillmentCard}
            setPhoneModalVisible={this.clickButtonsBarVendorCall}
            finalQueryIsFinish={finalQueryIsFinish}
          />,
        )}

        <LessDetailModal
          visible={lessModalVisible}
          onClose={this.hideLessModal}
          data={vehicleInfo?.esgInfo?.reducedCarbonEmissionRatio}
        />

        {/* 能源说明弹窗，内部介绍了各种能源方式，比如插电式、增程式、纯电动是什么意思 */}
        <FuelTypeModal
          visible={isFuelTypeModalVisible}
          fuelType={fuelType}
          onCancel={this.hideFuelTypeModal}
          info={config?.fuelTypeModalInfo}
        />

        {/* 车辆标签弹层 */}
        <CarLabelsModal
          packageItems={carTags}
          pageModalProps={{
            visible: labelsModalVisible,
            onMaskPress: () => setLabelsModalVisible(false),
          }}
        />

        {/* 统一客服电话弹层 */}
        {selector(
          isShowPage,
          <CustomerPhoneModal
            type={phoneModalType}
            modalVisible={phoneModalVisible}
            // @ts-ignore 已定义，提示未找到
            menuList={phoneList}
            onRequestClose={this.closePhoneModal}
          />,
        )}
        {/* 优选介绍弹窗，不会出现，optimizeModalVisible没有置为true的时候 */}
        <BbkOptimizeModal
          visible={modalsVisible?.optimizeModalVisible?.visible || false}
          onClose={() => {
            setOrderModalsVisible({ optimizeModalVisible: { visible: false } });
          }}
          items={OrderDetailCons.ctripSelected}
        />

        {/* 保险订单创建loading */}
        <CreateInsModal
          visible={modalsVisible?.createInsModalVisible?.visible || false}
          onClose={this.hideInsModal}
          title={modalsVisible.createInsModalVisible?.data?.title}
          content={modalsVisible.createInsModalVisible?.data?.content}
        />

        {/* 保险订单创建失败弹窗 */}
        <InsFailedModal
          visible={modalsVisible?.insFailedModalVisible?.visible || false}
          onClose={this.hideInsFailedModal}
          onPay={this.handleInsGoPay}
          onBack={this.hideInsFailedModal}
          title={OrderDetailCons.InsMsg.insFailModalTitle}
          content={
            modalsVisible?.insFailedModalVisible?.data?.content ||
            OrderDetailCons.InsMsg.insFailModalContent
          }
          backBtnText={OrderDetailCons.InsMsg.backBtnText}
          payBtnText={OrderDetailCons.InsMsg.payBtnText}
          showPayBtn={
            modalsVisible?.insFailedModalVisible?.data?.showPayBtn ?? true
          }
        />

        {/* 取车材料要求，应该不会出现，没有找到pickUpMaterials赋值的地方 */}
        <PickupMaterialsModal
          visible={modalsVisible?.pickUpMaterials?.visible || false}
          data={modalsVisible?.pickUpMaterials?.data}
          onHide={this.hidePickupMaterialModal}
        />

        <ConfirmModal
          visible={modalsVisible?.confirmModal?.visible}
          {...modalsVisible?.confirmModal?.data}
          {...this.getModifyOrderProps()}
        />

        {/* 修改订单提示 */}
        <ModifyOrderWarnModal />
        {/* 一嗨修改订单提示 */}
        {ehiModifyOrderModalVisible && <EHiModifyOrderSesameGuider />}
        {/* 不可点评弹层 ，样式丢失很久了，不知道会不会弹出来 */}
        <ReviewUnopenedModal
          onHide={this.hideReviewUnopenedModal}
          modalVisible={modalsVisible?.reviewUnopenedModal?.visible || false}
          orderBaseInfo={orderBaseInfo}
        />

        {/* 公告，配置出 */}
        <NewWarningTipsModal
          visible={warningTipsModalVisible || false}
          onClose={this.closeWarningTipsModal}
          title={texts.warningTipDetailText}
          content={warningTipsModalCotent}
        />

        {/* 一嗨门店免押规则，不知道什么时候会出 */}
        <EhiFreeDepositRuleModal />

        {/** 芝麻重复订单提示弹层 */}
        <SesameRepeatOrderModal />
        <OrderDetaiPriceDetailModal role={PageRole.ORDERDETAIL} />

        {/** 保险购买二次确认提示弹层 */}
        <BuyInsConfirmModal />
        {/** 继续支付失败文案提示 */}
        <ContinuePayFailDialog
          confirmBtnText={texts.gotIt}
          cancelBtnText={texts.reBookingText}
        />

        <PriceDetailModalOsd
          visible={osdPriceDetailModalVisible}
          role={PageRole.ORDERDETAIL}
          data={orderPriceInfoFee}
          onClose={this.hideOsdPriceDetailModal}
          showExplainModal={this.showOsdFeeTitleExplainModal}
          testID={CarLog.LogExposure({
            name: '曝光_订单详情页_费用明细',

            info: logBaseInfo,
          })}
        />

        <OsdFeeTitleExplainModal
          visible={osdFeeTitleExplainModalVisible}
          feeTitle={feeTitle}
          feeTitleExplain={feeTitleExplain}
          onCancel={this.hideOsdFeeTitleExplainModal}
        />

        <BbkInsuranceSuitsModalOsd
          visible={osdInsuranceVisible}
          anchorCategory={anchorCategory}
          role={PageRole.ORDERDETAIL}
          data={orderDetailResponse}
          packageInfos={packageInfos}
          insPackageId={curPackageId}
          packageId={curPackageId}
          selectPackageId={curPackageId}
          onCancel={this.hideOsdInsuranceModal}
          openExcessIntroduceModal={this.showExcessIntroduceModal}
          openInsuranceNoticeMustRead={this.showInsuranceNoticeMustReadModal}
          openInsuranceNoticeRule={this.openInsuranceNoticeRule}
          testID={CarLog.LogExposure({
            name: '曝光_订单详情页_保障服务_保障服务弹层',

            info: logBaseInfo,
          })}
        />

        <ExcessIntroduceModalOsd
          visible={osdExcessIntroduceVisible}
          onCancel={this.closeExcessIntroduceModal}
          excessIntroduce={config?.excessIntroduceNew}
          excessIntroduceBgOneNew={config?.excessIntroduceBgOneNew}
          excessIntroduceBgTwoNew={config?.excessIntroduceBgTwoNew}
          excessIntroduceDesc={config?.excessIntroduceDesc}
        />

        <InsuranceNoticeMustReadModal
          visible={insuranceNoticeMustReadVisible}
          onCancel={this.closeInsuranceNoticeMustReadModal}
          insuranceNotice={insuranceNotice}
        />

        <InsuranceReminderEnglishModal
          visible={insuranceReminderEnglishVisible}
          onCancel={this.closeInsuranceReminderEnglishModal}
          englishContent={englishContent}
        />

        {isLicenseApprove && (
          <BbkMaterialModalNew
            visible={materialModalVisible}
            materials={materials}
            printVoucherUrl={printVoucherUrl}
            onCancel={this.closeMaterialModal}
            onPressVoucher={this.onPressVoucher}
            onPressHandleLicense={this.onPressHandleLicense}
            onPressToLicense={this.onPressToLicense}
            onPressQuestion={this.showDepositRateDescriptionModal}
          />
        )}

        <FlightDelayRulesModal
          testID={CarLog.LogExposure({
            name: '曝光_订单详情页_航班延误政策弹层_航班延误政策',

            info: logBaseInfo,
          })}
        />

        {isLicenseApprove && (
          <DepositRateDescriptionModal
            visible={depositRateDescriptionModalVisible}
            title={depositRateDescriptionInfo?.title}
            desc={
              depositRateDescriptionInfo?.contents?.[0]?.stringObjs?.[0]
                ?.content
            }
            onCancel={this.closeDepositRateDescriptionModal}
          />
        )}
        {/* 境外营业时间收费标准弹层 */}

        <BusinessTimePolicyModal
          currentType={modalsVisible.businessTimePolicyModal?.data?.type}
          visible={modalsVisible.businessTimePolicyModal.visible}
          onClose={this.closeBusinessTimePolicyModal}
        />

        {/* 境外营业时间详情弹层 */}

        <BusinessTimeModal
          currentType={modalsVisible.businessTimeModal?.data?.type}
          visible={modalsVisible.businessTimeModal.visible}
          onClose={this.closeBusinessTimeModal}
        />

        <FuelDescriptionModal
          visible={modalsVisible.fuelDescModalVisible?.visible}
          onClose={this.closefuelDescModal}
        />

        {/* 出境修改订单弹层 */}

        <OsdModifyOrderModal
          visible={locationDatePopVisible}
          setLocationAndDatePopIsShow={setLocationAndDatePopIsShow}
          searchPanelModalRefFn={this.handleSearchPanelModalRef}
          currentPageId={this.getPageId()}
          orderId={orderId}
          osdModifyOrderNote={osdModifyOrderNote}
          pressSearchCallback={this.closeOsdModifyOrderModal}
        />

        <ContactDoorStoreModal
          visible={contactDoorStoreModalVisible}
          onClose={this.hideContactDoorStoreModal}
          activeIndex={contactDoorStoreModalTabId}
          pageName={Channel.getPageId().Order.EN}
          orderId={orderId}
          orderStatus={orderStatus}
        />
        <VehModal />
      </>
    );
  };

  renderPage() {
    const { queryOrderApiStatus } = this.props;
    const isSuccess = [
      QueryOrderApiStatusType.success,
      QueryOrderApiStatusType.before,
    ].includes(queryOrderApiStatus);
    const isFail = queryOrderApiStatus === QueryOrderApiStatusType.fail;

    const { isShowPageOtherModule } = this.state;
    return (
      // @ts-ignore 升级072
      <ViewPort style={styles.pageContainer}>
        <Header
          goback={this.pageGoBack}
          onHeaderLayout={e => {
            this.headerHeight = e?.nativeEvent?.layout?.height;
          }}
        />

        {true && (
          <>
            {this.renderPageContent()}
            {lazySelector(isShowPageOtherModule, this.renderPageModals)}
          </>
        )}
        {/* {!isSuccess &&
          (isFail ? (
            <ErrorNetWork
              title="系统繁忙"
              buttonText="再试一次"
              hasError={isFail}
              operateButtonPress={this.fetchOrderFn}
              buttonTestId={UITestID.car_testid_page_order_retry}
            />
          ) : (
            <LoadingView />
          ))} */}
      </ViewPort>
    );
  }
}
