const path = require('path');
module.exports = {
  rnVersion: '0.72.5', //0.70.1 0.72.5 harmony       默认 0.70.1  这个配置仅在本地开发生效
  extraPackageJson: {
    dependencies: {
      zustand: '4.4.4',
      swr: '2.2.0',
      immer: '10.0.2',
      uuid: '3.4.0',
      'react-native-keyboard-aware-scroll-view': '0.9.5',
    },
  },
  99999999: {
    extraPackageJson: {
      scripts: {
        ios: 'crn run-ios --port 5389 --app-version 8.68.6',
        postinstall: 'npm run lazyRequire && node ./scripts/postinstall.js',
        lazyRequire: 'node --harmony ./scripts/lazyRequireReplace',
        report: 'node __tests__/report.js',
        'test:car-order':
         "cross-env JEST_TEST_ENV=pipeline jest --config jest.config.js --json --outputFile='./coverage/car-order/case-result.json' --forceExit || true ",
       'badge': 'mkdir -p ./public && cp -r ./coverage ./public && node __tests__/badge.js',
      },
      iconFontOnline: {
        crn_font_car_ctrip_v1:
          'http://developer.fx.ctripcorp.com/api/iconfont.node.js?zipUrl=http%3A%2F%2Ficonfont.nfes.ctripcorp.com%2Fdownload%2Fproject-crn_font_car_ctrip_v1-0.0.17.zip',
        crn_font_xtaro_car_osd:
          'http://developer.fx.ctripcorp.com/api/iconfont.node.js?zipUrl=http%3A%2F%2Ficonfont.nfes.ctripcorp.com%2Fdownload%2Fproject-crn_font_xtaro_car_osd-0.0.20.zip',
      },

      dependencies: {
        '@ctrip/crn-ext-adsdk': '3.4.13',
        '@ctrip/rr-react-native': '5.1.4',
        '@ctrip/ipoll-urs-crn': '1.2.16',
        '@ctrip/bbk-trace': '1.1.3',
        "@ctrip/crn-inps": "1.0.17",
        "promise-timeout": "1.3.0",
        '@ctrip/mock-fetcher': '1.0.8',
      },
      devDependencies: {
       '@babel/core': '^7.25.7',
       'babel-jest': '^27.5.1',
       jest: '^29.6',
       'ts-jest': '^29.2',
       'jest-environment-jsdom': '^29.7.0',
       'react-test-renderer': '18.2.0',
       '@testing-library/jest-native': '5.4.0',
       '@testing-library/react-native': '12.5.0',
       '@types/jest': '^24.0.24',
       'badge-maker': '^3.3.1',
       lodash: '^4.17.21',
       '@ctrip/xtaro-api': '1.5.0',
       typescript: '^4.5.4',
       '@ctrip/testhub_prod_3816': 'latest',
       '@ctrip/jest-utils': 'latest',
     },
    },
  },
  37: {
    extraPackageJson: {
      scripts: {
        ios: 'crn run-ios --port 5389 --appid 37 --app-version 8.11.2 --simulator "iPhone 14 Pro Max"',
      },
      iconFontOnline: {
        crn_font_xtaro_car_ibu:
          'http://developer.fx.ctripcorp.com/api/iconfont.node.js?zipUrl=http%3A%2F%2Ficonfont.nfes.ctripcorp.com%2Fdownload%2Fproject-crn_font_xtaro_car_ibu-0.0.15.zip',
      },
      dependencies: {
        '@ctrip/crn': 'git+http://git.dev.sh.ctripcorp.com/crn/crn.git#rel/8.23.2_ibu',
      },
    },
    customExtraPlatform: 'trip',
  },
  //自定义CRN模板
  customTemplate: {
    entry: path.join(__dirname, 'template/Entry.njk'),
  },
};
